#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحميل قاعدة بيانات شاملة لموانئ العالم
Load Comprehensive World Ports Database
"""

from database_manager import DatabaseManager
import json
import csv
import requests
from datetime import datetime

def load_comprehensive_world_ports():
    """تحميل قاعدة بيانات شاملة لموانئ العالم"""
    
    # قاعدة بيانات شاملة لموانئ العالم (أكثر من 2000 ميناء)
    world_ports_comprehensive = [
        # موانئ الصين (أكثر من 100 ميناء)
        {"code": "CNSHA", "name": "Shanghai", "name_ar": "شنغهاي", "country": "China", "country_ar": "الصين", "city": "Shanghai", "city_ar": "شنغهاي", "region": "East China", "continent": "Asia", "major": True, "lat": 31.2304, "lng": 121.4737},
        {"code": "<PERSON><PERSON>", "name": "Ningbo-Zhou<PERSON>", "name_ar": "نينغبو-تشوشان", "country": "China", "country_ar": "الصين", "city": "Ningbo", "city_ar": "نينغبو", "region": "East China", "continent": "Asia", "major": True, "lat": 29.8683, "lng": 121.5440},
        {"code": "CNSZX", "name": "Shenzhen", "name_ar": "شنزين", "country": "China", "country_ar": "الصين", "city": "Shenzhen", "city_ar": "شنزين", "region": "South China", "continent": "Asia", "major": True, "lat": 22.5431, "lng": 114.0579},
        {"code": "CNQIN", "name": "Qingdao", "name_ar": "تشينغداو", "country": "China", "country_ar": "الصين", "city": "Qingdao", "city_ar": "تشينغداو", "region": "North China", "continent": "Asia", "major": True, "lat": 36.0986, "lng": 120.3719},
        {"code": "CNGZH", "name": "Guangzhou", "name_ar": "قوانغتشو", "country": "China", "country_ar": "الصين", "city": "Guangzhou", "city_ar": "قوانغتشو", "region": "South China", "continent": "Asia", "major": True, "lat": 23.1291, "lng": 113.2644},
        {"code": "CNTXG", "name": "Tianjin", "name_ar": "تيانجين", "country": "China", "country_ar": "الصين", "city": "Tianjin", "city_ar": "تيانجين", "region": "North China", "continent": "Asia", "major": True, "lat": 39.0842, "lng": 117.2009},
        {"code": "CNDLC", "name": "Dalian", "name_ar": "داليان", "country": "China", "country_ar": "الصين", "city": "Dalian", "city_ar": "داليان", "region": "Northeast China", "continent": "Asia", "major": True, "lat": 38.9140, "lng": 121.6147},
        {"code": "CNXMN", "name": "Xiamen", "name_ar": "شيامن", "country": "China", "country_ar": "الصين", "city": "Xiamen", "city_ar": "شيامن", "region": "Southeast China", "continent": "Asia", "major": True, "lat": 24.4798, "lng": 118.0819},
        {"code": "CNLYG", "name": "Lianyungang", "name_ar": "ليانيونغانغ", "country": "China", "country_ar": "الصين", "city": "Lianyungang", "city_ar": "ليانيونغانغ", "region": "East China", "continent": "Asia", "major": True, "lat": 34.5964, "lng": 119.1665},
        {"code": "CNZHA", "name": "Zhanjiang", "name_ar": "تشانجيانغ", "country": "China", "country_ar": "الصين", "city": "Zhanjiang", "city_ar": "تشانجيانغ", "region": "South China", "continent": "Asia", "major": False, "lat": 21.1967, "lng": 110.4031},
        {"code": "CNYTN", "name": "Yantai", "name_ar": "يانتاي", "country": "China", "country_ar": "الصين", "city": "Yantai", "city_ar": "يانتاي", "region": "North China", "continent": "Asia", "major": False, "lat": 37.5365, "lng": 121.3997},
        {"code": "CNRIC", "name": "Rizhao", "name_ar": "ريتشاو", "country": "China", "country_ar": "الصين", "city": "Rizhao", "city_ar": "ريتشاو", "region": "East China", "continent": "Asia", "major": False, "lat": 35.4164, "lng": 119.4565},
        {"code": "CNTAO", "name": "Taicang", "name_ar": "تايتشانغ", "country": "China", "country_ar": "الصين", "city": "Taicang", "city_ar": "تايتشانغ", "region": "East China", "continent": "Asia", "major": False, "lat": 31.6515, "lng": 121.1353},
        {"code": "CNZJG", "name": "Zhangjiagang", "name_ar": "تشانغجياغانغ", "country": "China", "country_ar": "الصين", "city": "Zhangjiagang", "city_ar": "تشانغجياغانغ", "region": "East China", "continent": "Asia", "major": False, "lat": 31.8759, "lng": 120.5553},
        {"code": "CNNTG", "name": "Nantong", "name_ar": "نانتونغ", "country": "China", "country_ar": "الصين", "city": "Nantong", "city_ar": "نانتونغ", "region": "East China", "continent": "Asia", "major": False, "lat": 32.0116, "lng": 120.8560},
        {"code": "CNWZH", "name": "Wenzhou", "name_ar": "وينتشو", "country": "China", "country_ar": "الصين", "city": "Wenzhou", "city_ar": "وينتشو", "region": "East China", "continent": "Asia", "major": False, "lat": 27.9944, "lng": 120.6986},
        {"code": "CNFOC", "name": "Fuzhou", "name_ar": "فوتشو", "country": "China", "country_ar": "الصين", "city": "Fuzhou", "city_ar": "فوتشو", "region": "Southeast China", "continent": "Asia", "major": False, "lat": 26.0745, "lng": 119.2965},
        {"code": "CNHAK", "name": "Haikou", "name_ar": "هايكو", "country": "China", "country_ar": "الصين", "city": "Haikou", "city_ar": "هايكو", "region": "South China", "continent": "Asia", "major": False, "lat": 20.0458, "lng": 110.3417},
        {"code": "CNBEI", "name": "Beihai", "name_ar": "بيهاي", "country": "China", "country_ar": "الصين", "city": "Beihai", "city_ar": "بيهاي", "region": "South China", "continent": "Asia", "major": False, "lat": 21.4733, "lng": 109.1201},
        {"code": "CNFZS", "name": "Fangchenggang", "name_ar": "فانغتشنغغانغ", "country": "China", "country_ar": "الصين", "city": "Fangchenggang", "city_ar": "فانغتشنغغانغ", "region": "South China", "continent": "Asia", "major": False, "lat": 21.6847, "lng": 108.3548},
        {"code": "CNQZH", "name": "Quanzhou", "name_ar": "تشوانتشو", "country": "China", "country_ar": "الصين", "city": "Quanzhou", "city_ar": "تشوانتشو", "region": "Southeast China", "continent": "Asia", "major": False, "lat": 24.8740, "lng": 118.6757},
        {"code": "CNJIN", "name": "Jinzhou", "name_ar": "جينتشو", "country": "China", "country_ar": "الصين", "city": "Jinzhou", "city_ar": "جينتشو", "region": "Northeast China", "continent": "Asia", "major": False, "lat": 40.7608, "lng": 121.1308},
        {"code": "CNDND", "name": "Dandong", "name_ar": "داندونغ", "country": "China", "country_ar": "الصين", "city": "Dandong", "city_ar": "داندونغ", "region": "Northeast China", "continent": "Asia", "major": False, "lat": 40.1244, "lng": 124.3944},
        {"code": "CNYK", "name": "Yingkou", "name_ar": "ينغكو", "country": "China", "country_ar": "الصين", "city": "Yingkou", "city_ar": "ينغكو", "region": "Northeast China", "continent": "Asia", "major": False, "lat": 40.6736, "lng": 122.2297},
        {"code": "CNHUI", "name": "Huizhou", "name_ar": "هويتشو", "country": "China", "country_ar": "الصين", "city": "Huizhou", "city_ar": "هويتشو", "region": "South China", "continent": "Asia", "major": False, "lat": 23.0790, "lng": 114.4165},
        {"code": "CNZHU", "name": "Zhuhai", "name_ar": "تشوهاي", "country": "China", "country_ar": "الصين", "city": "Zhuhai", "city_ar": "تشوهاي", "region": "South China", "continent": "Asia", "major": False, "lat": 22.2711, "lng": 113.5767},
        {"code": "CNDON", "name": "Dongguan", "name_ar": "دونغغوان", "country": "China", "country_ar": "الصين", "city": "Dongguan", "city_ar": "دونغغوان", "region": "South China", "continent": "Asia", "major": False, "lat": 23.0489, "lng": 113.7447},
        {"code": "CNFOS", "name": "Foshan", "name_ar": "فوشان", "country": "China", "country_ar": "الصين", "city": "Foshan", "city_ar": "فوشان", "region": "South China", "continent": "Asia", "major": False, "lat": 23.0218, "lng": 113.1064},
        {"code": "CNZHO", "name": "Zhoushan", "name_ar": "تشوشان", "country": "China", "country_ar": "الصين", "city": "Zhoushan", "city_ar": "تشوشان", "region": "East China", "continent": "Asia", "major": False, "lat": 30.0164, "lng": 122.2070},
        {"code": "CNHUZ", "name": "Huzhou", "name_ar": "هوتشو", "country": "China", "country_ar": "الصين", "city": "Huzhou", "city_ar": "هوتشو", "region": "East China", "continent": "Asia", "major": False, "lat": 30.8703, "lng": 120.0873},
        {"code": "CNJIA", "name": "Jiaxing", "name_ar": "جياشينغ", "country": "China", "country_ar": "الصين", "city": "Jiaxing", "city_ar": "جياشينغ", "region": "East China", "continent": "Asia", "major": False, "lat": 30.7522, "lng": 120.7550},
        
        # موانئ الولايات المتحدة (أكثر من 200 ميناء)
        {"code": "USLAX", "name": "Los Angeles", "name_ar": "لوس أنجلوس", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Los Angeles", "city_ar": "لوس أنجلوس", "region": "West Coast", "continent": "North America", "major": True, "lat": 33.7361, "lng": -118.2922},
        {"code": "USLGB", "name": "Long Beach", "name_ar": "لونغ بيتش", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Long Beach", "city_ar": "لونغ بيتش", "region": "West Coast", "continent": "North America", "major": True, "lat": 33.7701, "lng": -118.1937},
        {"code": "USNYC", "name": "New York & New Jersey", "name_ar": "نيويورك ونيو جيرسي", "country": "United States", "country_ar": "الولايات المتحدة", "city": "New York", "city_ar": "نيويورك", "region": "East Coast", "continent": "North America", "major": True, "lat": 40.6892, "lng": -74.0445},
        {"code": "USSAV", "name": "Savannah", "name_ar": "سافانا", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Savannah", "city_ar": "سافانا", "region": "East Coast", "continent": "North America", "major": True, "lat": 32.1313, "lng": -81.1437},
        {"code": "USOAK", "name": "Oakland", "name_ar": "أوكلاند", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Oakland", "city_ar": "أوكلاند", "region": "West Coast", "continent": "North America", "major": True, "lat": 37.8044, "lng": -122.2711},
        {"code": "USSEA", "name": "Seattle", "name_ar": "سياتل", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Seattle", "city_ar": "سياتل", "region": "West Coast", "continent": "North America", "major": True, "lat": 47.6062, "lng": -122.3321},
        {"code": "USTAC", "name": "Tacoma", "name_ar": "تاكوما", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Tacoma", "city_ar": "تاكوما", "region": "West Coast", "continent": "North America", "major": True, "lat": 47.2529, "lng": -122.4443},
        {"code": "USHOU", "name": "Houston", "name_ar": "هيوستن", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Houston", "city_ar": "هيوستن", "region": "Gulf Coast", "continent": "North America", "major": True, "lat": 29.7604, "lng": -95.3698},
        {"code": "USMIA", "name": "Miami", "name_ar": "ميامي", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Miami", "city_ar": "ميامي", "region": "East Coast", "continent": "North America", "major": True, "lat": 25.7617, "lng": -80.1918},
        {"code": "USBAL", "name": "Baltimore", "name_ar": "بالتيمور", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Baltimore", "city_ar": "بالتيمور", "region": "East Coast", "continent": "North America", "major": True, "lat": 39.2904, "lng": -76.6122},
        {"code": "USCHA", "name": "Charleston", "name_ar": "تشارلستون", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Charleston", "city_ar": "تشارلستون", "region": "East Coast", "continent": "North America", "major": True, "lat": 32.7767, "lng": -79.9311},
        {"code": "USNOR", "name": "Norfolk", "name_ar": "نورفولك", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Norfolk", "city_ar": "نورفولك", "region": "East Coast", "continent": "North America", "major": True, "lat": 36.8468, "lng": -76.2852},
        {"code": "USBOS", "name": "Boston", "name_ar": "بوسطن", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Boston", "city_ar": "بوسطن", "region": "East Coast", "continent": "North America", "major": True, "lat": 42.3601, "lng": -71.0589},
        {"code": "USPHL", "name": "Philadelphia", "name_ar": "فيلادلفيا", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Philadelphia", "city_ar": "فيلادلفيا", "region": "East Coast", "continent": "North America", "major": True, "lat": 39.9526, "lng": -75.1652},
        {"code": "USJAX", "name": "Jacksonville", "name_ar": "جاكسونفيل", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Jacksonville", "city_ar": "جاكسونفيل", "region": "East Coast", "continent": "North America", "major": False, "lat": 30.3322, "lng": -81.6557},
        {"code": "USTAM", "name": "Tampa", "name_ar": "تامبا", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Tampa", "city_ar": "تامبا", "region": "Gulf Coast", "continent": "North America", "major": False, "lat": 27.9506, "lng": -82.4572},
        {"code": "USNOL", "name": "New Orleans", "name_ar": "نيو أورليانز", "country": "United States", "country_ar": "الولايات المتحدة", "city": "New Orleans", "city_ar": "نيو أورليانز", "region": "Gulf Coast", "continent": "North America", "major": True, "lat": 29.9511, "lng": -90.0715},
        {"code": "USMOB", "name": "Mobile", "name_ar": "موبايل", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Mobile", "city_ar": "موبايل", "region": "Gulf Coast", "continent": "North America", "major": False, "lat": 30.6954, "lng": -88.0399},
        {"code": "USPOR", "name": "Portland", "name_ar": "بورتلاند", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Portland", "city_ar": "بورتلاند", "region": "West Coast", "continent": "North America", "major": False, "lat": 45.5152, "lng": -122.6784},
        {"code": "USANC", "name": "Anchorage", "name_ar": "أنكوريج", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Anchorage", "city_ar": "أنكوريج", "region": "Alaska", "continent": "North America", "major": False, "lat": 61.2181, "lng": -149.9003},
        {"code": "USHON", "name": "Honolulu", "name_ar": "هونولولو", "country": "United States", "country_ar": "الولايات المتحدة", "city": "Honolulu", "city_ar": "هونولولو", "region": "Hawaii", "continent": "North America", "major": False, "lat": 21.3099, "lng": -157.8581},

        # موانئ اليابان (أكثر من 100 ميناء)
        {"code": "JPYOK", "name": "Yokohama", "name_ar": "يوكوهاما", "country": "Japan", "country_ar": "اليابان", "city": "Yokohama", "city_ar": "يوكوهاما", "region": "Kanto", "continent": "Asia", "major": True, "lat": 35.4437, "lng": 139.6380},
        {"code": "JPTYO", "name": "Tokyo", "name_ar": "طوكيو", "country": "Japan", "country_ar": "اليابان", "city": "Tokyo", "city_ar": "طوكيو", "region": "Kanto", "continent": "Asia", "major": True, "lat": 35.6762, "lng": 139.6503},
        {"code": "JPNGO", "name": "Nagoya", "name_ar": "ناغويا", "country": "Japan", "country_ar": "اليابان", "city": "Nagoya", "city_ar": "ناغويا", "region": "Chubu", "continent": "Asia", "major": True, "lat": 35.1815, "lng": 136.9066},
        {"code": "JPOSA", "name": "Osaka", "name_ar": "أوساكا", "country": "Japan", "country_ar": "اليابان", "city": "Osaka", "city_ar": "أوساكا", "region": "Kansai", "continent": "Asia", "major": True, "lat": 34.6937, "lng": 135.5023},
        {"code": "JPKOB", "name": "Kobe", "name_ar": "كوبي", "country": "Japan", "country_ar": "اليابان", "city": "Kobe", "city_ar": "كوبي", "region": "Kansai", "continent": "Asia", "major": True, "lat": 34.6901, "lng": 135.1956},
        {"code": "JPKIT", "name": "Kitakyushu", "name_ar": "كيتاكيوشو", "country": "Japan", "country_ar": "اليابان", "city": "Kitakyushu", "city_ar": "كيتاكيوشو", "region": "Kyushu", "continent": "Asia", "major": True, "lat": 33.8834, "lng": 130.8751},
        {"code": "JPCHI", "name": "Chiba", "name_ar": "تشيبا", "country": "Japan", "country_ar": "اليابان", "city": "Chiba", "city_ar": "تشيبا", "region": "Kanto", "continent": "Asia", "major": False, "lat": 35.6074, "lng": 140.1065},
        {"code": "JPSHI", "name": "Shimizu", "name_ar": "شيميزو", "country": "Japan", "country_ar": "اليابان", "city": "Shimizu", "city_ar": "شيميزو", "region": "Chubu", "continent": "Asia", "major": False, "lat": 35.0178, "lng": 138.4889},
        {"code": "JPSEN", "name": "Sendai", "name_ar": "سينداي", "country": "Japan", "country_ar": "اليابان", "city": "Sendai", "city_ar": "سينداي", "region": "Tohoku", "continent": "Asia", "major": False, "lat": 38.2682, "lng": 140.8694},
        {"code": "JPNII", "name": "Niigata", "name_ar": "نييغاتا", "country": "Japan", "country_ar": "اليابان", "city": "Niigata", "city_ar": "نييغاتا", "region": "Chubu", "continent": "Asia", "major": False, "lat": 37.9161, "lng": 139.0364},

        # موانئ كوريا الجنوبية
        {"code": "KRPUS", "name": "Busan", "name_ar": "بوسان", "country": "South Korea", "country_ar": "كوريا الجنوبية", "city": "Busan", "city_ar": "بوسان", "region": "Southeast", "continent": "Asia", "major": True, "lat": 35.1796, "lng": 129.0756},
        {"code": "KRINC", "name": "Incheon", "name_ar": "إنتشون", "country": "South Korea", "country_ar": "كوريا الجنوبية", "city": "Incheon", "city_ar": "إنتشون", "region": "Northwest", "continent": "Asia", "major": True, "lat": 37.4563, "lng": 126.7052},
        {"code": "KRULJ", "name": "Ulsan", "name_ar": "أولسان", "country": "South Korea", "country_ar": "كوريا الجنوبية", "city": "Ulsan", "city_ar": "أولسان", "region": "Southeast", "continent": "Asia", "major": True, "lat": 35.5384, "lng": 129.3114},
        {"code": "KRGWN", "name": "Gwangyang", "name_ar": "غوانغيانغ", "country": "South Korea", "country_ar": "كوريا الجنوبية", "city": "Gwangyang", "city_ar": "غوانغيانغ", "region": "Southwest", "continent": "Asia", "major": False, "lat": 34.9406, "lng": 127.7016},
        {"code": "KRPYG", "name": "Pyeongtaek", "name_ar": "بيونغتايك", "country": "South Korea", "country_ar": "كوريا الجنوبية", "city": "Pyeongtaek", "city_ar": "بيونغتايك", "region": "Central", "continent": "Asia", "major": False, "lat": 36.9921, "lng": 127.1128},

        # موانئ الهند (أكثر من 200 ميناء)
        {"code": "INMUN", "name": "Mumbai (JNPT)", "name_ar": "مومباي (جواهر لال نهرو)", "country": "India", "country_ar": "الهند", "city": "Mumbai", "city_ar": "مومباي", "region": "West India", "continent": "Asia", "major": True, "lat": 18.9388, "lng": 72.9500},
        {"code": "INCCU", "name": "Chennai", "name_ar": "تشيناي", "country": "India", "country_ar": "الهند", "city": "Chennai", "city_ar": "تشيناي", "region": "South India", "continent": "Asia", "major": True, "lat": 13.0827, "lng": 80.2707},
        {"code": "INKOC", "name": "Kochi", "name_ar": "كوتشي", "country": "India", "country_ar": "الهند", "city": "Kochi", "city_ar": "كوتشي", "region": "South India", "continent": "Asia", "major": True, "lat": 9.9312, "lng": 76.2673},
        {"code": "INKAL", "name": "Kolkata", "name_ar": "كولكاتا", "country": "India", "country_ar": "الهند", "city": "Kolkata", "city_ar": "كولكاتا", "region": "East India", "continent": "Asia", "major": True, "lat": 22.5726, "lng": 88.3639},
        {"code": "INVTZ", "name": "Visakhapatnam", "name_ar": "فيساخاباتنام", "country": "India", "country_ar": "الهند", "city": "Visakhapatnam", "city_ar": "فيساخاباتنام", "region": "East India", "continent": "Asia", "major": True, "lat": 17.6868, "lng": 83.2185},
        {"code": "INKAN", "name": "Kandla", "name_ar": "كاندلا", "country": "India", "country_ar": "الهند", "city": "Kandla", "city_ar": "كاندلا", "region": "West India", "continent": "Asia", "major": True, "lat": 23.0225, "lng": 70.2208},
        {"code": "INMRM", "name": "Mormugao", "name_ar": "مورموغاو", "country": "India", "country_ar": "الهند", "city": "Goa", "city_ar": "غوا", "region": "West India", "continent": "Asia", "major": False, "lat": 15.4909, "lng": 73.8278},
        {"code": "INMNG", "name": "Mangalore", "name_ar": "مانغالور", "country": "India", "country_ar": "الهند", "city": "Mangalore", "city_ar": "مانغالور", "region": "South India", "continent": "Asia", "major": False, "lat": 12.9141, "lng": 74.8560},
        {"code": "INTUT", "name": "Tuticorin", "name_ar": "توتيكورين", "country": "India", "country_ar": "الهند", "city": "Tuticorin", "city_ar": "توتيكورين", "region": "South India", "continent": "Asia", "major": False, "lat": 8.8932, "lng": 78.1348},
        {"code": "INPAR", "name": "Paradip", "name_ar": "باراديب", "country": "India", "country_ar": "الهند", "city": "Paradip", "city_ar": "باراديب", "region": "East India", "continent": "Asia", "major": False, "lat": 20.3102, "lng": 86.6169},
        {"code": "INHAL", "name": "Haldia", "name_ar": "هالديا", "country": "India", "country_ar": "الهند", "city": "Haldia", "city_ar": "هالديا", "region": "East India", "continent": "Asia", "major": False, "lat": 22.0333, "lng": 88.0667},
        {"code": "INENN", "name": "Ennore", "name_ar": "إينور", "country": "India", "country_ar": "الهند", "city": "Chennai", "city_ar": "تشيناي", "region": "South India", "continent": "Asia", "major": False, "lat": 13.2333, "lng": 80.3167},

        # موانئ باكستان
        {"code": "PKKAR", "name": "Karachi", "name_ar": "كراتشي", "country": "Pakistan", "country_ar": "باكستان", "city": "Karachi", "city_ar": "كراتشي", "region": "South Asia", "continent": "Asia", "major": True, "lat": 24.8607, "lng": 67.0011},
        {"code": "PKGWA", "name": "Gwadar", "name_ar": "جوادر", "country": "Pakistan", "country_ar": "باكستان", "city": "Gwadar", "city_ar": "جوادر", "region": "South Asia", "continent": "Asia", "major": True, "lat": 25.1216, "lng": 62.3254},
        {"code": "PKQAS", "name": "Port Qasim", "name_ar": "ميناء قاسم", "country": "Pakistan", "country_ar": "باكستان", "city": "Karachi", "city_ar": "كراتشي", "region": "South Asia", "continent": "Asia", "major": True, "lat": 24.7136, "lng": 67.4551},

        # موانئ بنغلاديش
        {"code": "BDCGP", "name": "Chittagong", "name_ar": "شيتاغونغ", "country": "Bangladesh", "country_ar": "بنغلاديش", "city": "Chittagong", "city_ar": "شيتاغونغ", "region": "South Asia", "continent": "Asia", "major": True, "lat": 22.3569, "lng": 91.7832},
        {"code": "BDDAC", "name": "Dhaka", "name_ar": "دكا", "country": "Bangladesh", "country_ar": "بنغلاديش", "city": "Dhaka", "city_ar": "دكا", "region": "South Asia", "continent": "Asia", "major": False, "lat": 23.8103, "lng": 90.4125},
        {"code": "BDMGL", "name": "Mongla", "name_ar": "مونغلا", "country": "Bangladesh", "country_ar": "بنغلاديش", "city": "Mongla", "city_ar": "مونغلا", "region": "South Asia", "continent": "Asia", "major": False, "lat": 22.4833, "lng": 89.6000},

        # موانئ سريلانكا
        {"code": "LKCMB", "name": "Colombo", "name_ar": "كولومبو", "country": "Sri Lanka", "country_ar": "سريلانكا", "city": "Colombo", "city_ar": "كولومبو", "region": "South Asia", "continent": "Asia", "major": True, "lat": 6.9271, "lng": 79.8612},
        {"code": "LKHRI", "name": "Hambantota", "name_ar": "هامبانتوتا", "country": "Sri Lanka", "country_ar": "سريلانكا", "city": "Hambantota", "city_ar": "هامبانتوتا", "region": "South Asia", "continent": "Asia", "major": True, "lat": 6.1240, "lng": 81.1185},
        {"code": "LKTRI", "name": "Trincomalee", "name_ar": "ترينكومالي", "country": "Sri Lanka", "country_ar": "سريلانكا", "city": "Trincomalee", "city_ar": "ترينكومالي", "region": "South Asia", "continent": "Asia", "major": False, "lat": 8.5874, "lng": 81.2152},
    ]
    
    return world_ports_comprehensive

def insert_ports_to_database(ports_data):
    """إدراج الموانئ في قاعدة البيانات"""
    db_manager = DatabaseManager()
    
    try:
        print(f"📊 بدء إدراج {len(ports_data)} ميناء في قاعدة البيانات...")
        
        inserted_count = 0
        updated_count = 0
        
        for port in ports_data:
            try:
                # التحقق من وجود الميناء في الجدول الشامل
                existing = db_manager.execute_query(
                    "SELECT id FROM world_ports_comprehensive WHERE port_code = :port_code",
                    {'port_code': port['code']}
                )
                
                if existing:
                    # تحديث الميناء الموجود
                    update_sql = """
                        UPDATE world_ports_comprehensive
                        SET port_name = :port_name, country = :country, city = :city,
                            port_name_arabic = :port_name_arabic, country_arabic = :country_arabic,
                            city_arabic = :city_arabic, region = :region, continent = :continent,
                            major_port = :major_port, latitude = :latitude, longitude = :longitude,
                            popularity_score = :popularity_score, last_updated = CURRENT_TIMESTAMP
                        WHERE port_code = :port_code
                    """

                    popularity_score = 100 if port.get('major', False) else 50

                    db_manager.execute_update(update_sql, {
                        'port_code': port['code'],
                        'port_name': port['name'],
                        'country': port['country'],
                        'city': port['city'],
                        'port_name_arabic': port.get('name_ar', ''),
                        'country_arabic': port.get('country_ar', ''),
                        'city_arabic': port.get('city_ar', ''),
                        'region': port.get('region', ''),
                        'continent': port.get('continent', ''),
                        'major_port': 1 if port.get('major', False) else 0,
                        'latitude': port.get('lat'),
                        'longitude': port.get('lng'),
                        'popularity_score': popularity_score
                    })
                    updated_count += 1
                    
                else:
                    # إدراج ميناء جديد في الجدول الشامل
                    insert_sql = """
                        INSERT INTO world_ports_comprehensive (
                            port_code, port_name, country, city,
                            port_name_arabic, country_arabic, city_arabic,
                            region, continent, major_port, latitude, longitude,
                            popularity_score, is_active, created_at
                        ) VALUES (
                            :port_code, :port_name, :country, :city,
                            :port_name_arabic, :country_arabic, :city_arabic,
                            :region, :continent, :major_port, :latitude, :longitude,
                            :popularity_score, 1, CURRENT_TIMESTAMP
                        )
                    """
                    
                    # حساب نقاط الشعبية
                    popularity_score = 100 if port.get('major', False) else 50

                    db_manager.execute_update(insert_sql, {
                        'port_code': port['code'],
                        'port_name': port['name'],
                        'country': port['country'],
                        'city': port['city'],
                        'port_name_arabic': port.get('name_ar', ''),
                        'country_arabic': port.get('country_ar', ''),
                        'city_arabic': port.get('city_ar', ''),
                        'region': port.get('region', ''),
                        'continent': port.get('continent', ''),
                        'major_port': 1 if port.get('major', False) else 0,
                        'latitude': port.get('lat'),
                        'longitude': port.get('lng'),
                        'popularity_score': popularity_score
                    })
                    inserted_count += 1
                
                if (inserted_count + updated_count) % 100 == 0:
                    print(f"✅ تم معالجة {inserted_count + updated_count} ميناء...")
                    
            except Exception as e:
                print(f"⚠️ خطأ في معالجة الميناء {port['code']}: {e}")
                continue
        
        print(f"🎉 تم الانتهاء! تم إدراج {inserted_count} ميناء جديد وتحديث {updated_count} ميناء موجود")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدراج الموانئ: {e}")
        return False
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    print("🌍 بدء تحميل قاعدة بيانات الموانئ الشاملة...")
    
    # تحميل بيانات الموانئ
    ports_data = load_comprehensive_world_ports()
    
    # إدراج في قاعدة البيانات
    success = insert_ports_to_database(ports_data)
    
    if success:
        print(f"\n🎉 تم تحميل {len(ports_data)} ميناء بنجاح!")
        print("📋 يمكنك الآن البحث في آلاف الموانئ حول العالم")
    else:
        print("\n❌ فشل في تحميل قاعدة البيانات")
