-- =====================================================
-- إنشاء جميع الـ Triggers المطلوبة
-- Create All Required Triggers
-- =====================================================

-- 1. Trigger لجدول PURCHASE_ORDER_PAYMENTS
CREATE OR REPLACE TRIGGER purchase_order_payments_trigger
    BEFORE INSERT ON PURCHASE_ORDER_PAYMENTS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := PURCHASE_ORDER_PAYMENTS_SEQ.NEXTVAL;
    END IF;
    
    -- حساب المبلغ بالعملة الأساسية
    :NEW.base_currency_amount := :NEW.payment_amount * NVL(:NEW.exchange_rate, 1);
    
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

-- 2. Trigger لجدول PURCHASE_ORDER_STATUS_LOG
CREATE OR REPLACE TRIGGER purchase_order_status_log_trigger
    BEFORE INSERT ON PURCHASE_ORDER_STATUS_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := PURCHASE_ORDER_STATUS_LOG_SEQ.NEXTVAL;
    END IF;
END;
/

-- 3. Trigger لجدول GOODS_RECEIPTS
CREATE OR REPLACE TRIGGER goods_receipts_trigger
    BEFORE INSERT ON GOODS_RECEIPTS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := GOODS_RECEIPTS_SEQ.NEXTVAL;
    END IF;
    
    -- إنشاء رقم إيصال تلقائي إذا لم يكن محدد
    IF :NEW.receipt_number IS NULL THEN
        :NEW.receipt_number := 'GR' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(GOODS_RECEIPTS_SEQ.CURRVAL, 4, '0');
    END IF;
    
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

-- 4. Trigger لجدول GOODS_RECEIPT_ITEMS
CREATE OR REPLACE TRIGGER goods_receipt_items_trigger
    BEFORE INSERT ON GOODS_RECEIPT_ITEMS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := GOODS_RECEIPT_ITEMS_SEQ.NEXTVAL;
    END IF;
END;
/

-- 5. Trigger لجدول RECONCILIATION_CYCLES
CREATE OR REPLACE TRIGGER reconciliation_cycles_trigger
    BEFORE INSERT ON RECONCILIATION_CYCLES
    FOR EACH ROW
BEGIN
    IF :NEW.cycle_id IS NULL THEN
        :NEW.cycle_id := RECONCILIATION_CYCLES_SEQ.NEXTVAL;
    END IF;
    
    -- تحديث تاريخ التعديل
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- 6. Trigger لجدول SUPPLIER_RECONCILIATION
CREATE OR REPLACE TRIGGER supplier_reconciliation_trigger
    BEFORE INSERT ON SUPPLIER_RECONCILIATION
    FOR EACH ROW
BEGIN
    IF :NEW.reconciliation_id IS NULL THEN
        :NEW.reconciliation_id := SUPPLIER_RECONCILIATION_SEQ.NEXTVAL;
    END IF;
    
    -- حساب الفروقات تلقائياً
    :NEW.opening_difference := NVL(:NEW.system_opening_balance, 0) - NVL(:NEW.supplier_opening_balance, 0);
    :NEW.debit_difference := NVL(:NEW.system_debit_amount, 0) - NVL(:NEW.supplier_debit_amount, 0);
    :NEW.credit_difference := NVL(:NEW.system_credit_amount, 0) - NVL(:NEW.supplier_credit_amount, 0);
    :NEW.closing_difference := NVL(:NEW.system_closing_balance, 0) - NVL(:NEW.supplier_closing_balance, 0);
    :NEW.total_difference := :NEW.opening_difference + :NEW.debit_difference + :NEW.credit_difference + :NEW.closing_difference;
    
    -- تحديد حالة المطابقة تلقائياً
    IF ABS(:NEW.total_difference) <= NVL(:NEW.match_tolerance, 0.01) THEN
        :NEW.reconciliation_status := 'MATCHED';
        :NEW.auto_matched := 'Y';
    ELSE
        :NEW.reconciliation_status := 'UNMATCHED';
        :NEW.auto_matched := 'N';
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- 7. Trigger لجدول RECONCILIATION_DIFFERENCES
CREATE OR REPLACE TRIGGER reconciliation_differences_trigger
    BEFORE INSERT ON RECONCILIATION_DIFFERENCES
    FOR EACH ROW
BEGIN
    IF :NEW.difference_id IS NULL THEN
        :NEW.difference_id := RECONCILIATION_DIFFERENCES_SEQ.NEXTVAL;
    END IF;
    
    -- حساب الفرق والنسبة المئوية
    :NEW.difference_amount := NVL(:NEW.expected_amount, 0) - NVL(:NEW.actual_amount, 0);
    
    IF NVL(:NEW.expected_amount, 0) != 0 THEN
        :NEW.difference_percentage := (:NEW.difference_amount / :NEW.expected_amount) * 100;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- 8. Trigger لجدول RECONCILIATION_ADJUSTMENTS
CREATE OR REPLACE TRIGGER reconciliation_adjustments_trigger
    BEFORE INSERT ON RECONCILIATION_ADJUSTMENTS
    FOR EACH ROW
BEGIN
    IF :NEW.adjustment_id IS NULL THEN
        :NEW.adjustment_id := RECONCILIATION_ADJUSTMENTS_SEQ.NEXTVAL;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;
/

-- 9. Trigger لجدول RECONCILIATION_ACTIVITY
CREATE OR REPLACE TRIGGER reconciliation_activity_trg
    BEFORE INSERT ON RECONCILIATION_ACTIVITY
    FOR EACH ROW
BEGIN
    IF :NEW.log_id IS NULL THEN
        :NEW.log_id := RECONCILIATION_ACTIVITY_SEQ.NEXTVAL;
    END IF;
END;
/

-- 10. Trigger لجدول SUPPLIER_PERFORMANCE
CREATE OR REPLACE TRIGGER supplier_performance_trg
    BEFORE INSERT ON SUPPLIER_PERFORMANCE
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_PERFORMANCE_SEQ.NEXTVAL;
    END IF;
    
    -- حساب التقييم الإجمالي
    :NEW.overall_rating := (
        NVL(:NEW.quality_rating, 0) + 
        NVL(:NEW.delivery_rating, 0) + 
        NVL(:NEW.price_rating, 0) + 
        NVL(:NEW.service_rating, 0) + 
        NVL(:NEW.compliance_rating, 0)
    ) / 5;
    
    -- تحديد فئة الأداء
    IF :NEW.overall_rating >= 4.5 THEN
        :NEW.performance_category := 'EXCELLENT';
    ELSIF :NEW.overall_rating >= 3.5 THEN
        :NEW.performance_category := 'GOOD';
    ELSIF :NEW.overall_rating >= 2.5 THEN
        :NEW.performance_category := 'AVERAGE';
    ELSIF :NEW.overall_rating >= 1.5 THEN
        :NEW.performance_category := 'POOR';
    ELSE
        :NEW.performance_category := 'CRITICAL';
    END IF;
    
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

-- 11. Trigger لجدول INTEGRATION_NOTIFICATIONS
CREATE OR REPLACE TRIGGER integration_notifications_trigger
    BEFORE INSERT ON INTEGRATION_NOTIFICATIONS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := INTEGRATION_NOTIFICATIONS_SEQ.NEXTVAL;
    END IF;
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

-- رسالة نجاح
SELECT 'تم إنشاء جميع الـ Triggers بنجاح!' as status FROM dual;

COMMIT;
