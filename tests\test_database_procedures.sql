-- =====================================================
-- اختبارات الإجراءات المخزنة للنظام المحاسبي
-- Database Procedures Tests
-- =====================================================

-- إعداد بيانات الاختبار
-- Test Data Setup

-- 1. إنشاء بيانات اختبار للموردين
INSERT INTO suppliers (id, name, code, contact_person, phone, email, status)
SELECT 9001, 'مورد اختبار 1', 'TEST001', 'أحمد محمد', '0501234567', '<EMAIL>', 'active' FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM suppliers WHERE id = 9001);

INSERT INTO suppliers (id, name, code, contact_person, phone, email, status)
SELECT 9002, 'مورد اختبار 2', 'TEST002', 'محمد أحمد', '0507654321', '<EMAIL>', 'active' FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM suppliers WHERE id = 9002);

-- 2. إنشاء بيانات اختبار للصرافين
INSERT INTO money_changers (id, name, code, contact_person, phone, email, status)
SELECT 9001, 'صراف اختبار', 'TESTMC001', 'علي سالم', '0509876543', '<EMAIL>', 'active' FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM money_changers WHERE id = 9001);

-- 3. إنشاء رصيد اختبار للصراف
INSERT INTO CURRENT_BALANCES (
    entity_type_code, entity_id, currency_code,
    opening_balance, debit_amount, credit_amount, current_balance,
    total_transactions_count, last_transaction_date,
    created_at, updated_at, created_by, updated_by,
    description
)
SELECT 
    'MONEY_CHANGER', 9001, 'SAR',
    100000, 0, 0, 100000,
    0, SYSDATE,
    SYSDATE, SYSDATE, 1, 1,
    'رصيد اختبار للصراف'
FROM DUAL
WHERE NOT EXISTS (
    SELECT 1 FROM CURRENT_BALANCES 
    WHERE entity_type_code = 'MONEY_CHANGER' 
    AND entity_id = 9001 
    AND currency_code = 'SAR'
);

-- 4. إنشاء حوالة اختبار
INSERT INTO transfers (
    id, request_number, beneficiary_name, bank_name, amount, currency,
    purpose, branch_name, status, priority_level,
    created_at, updated_at, created_by, updated_by
)
SELECT 
    9001, 'TEST-TRF-001', 'مستفيد اختبار', 'بنك اختبار', 15000, 'SAR',
    'غرض اختبار', 'فرع اختبار', 'approved', 'normal',
    SYSDATE, SYSDATE, 1, 1
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM transfers WHERE id = 9001);

-- =====================================================
-- اختبارات دوال التحقق
-- Validation Functions Tests
-- =====================================================

-- اختبار 1: التحقق من رصيد الصراف - رصيد كافي
DECLARE
    v_result VARCHAR2(4000);
BEGIN
    v_result := CHECK_MONEY_CHANGER_BALANCE(9001, 10000, 'SAR');
    
    IF v_result LIKE 'OK:%' THEN
        DBMS_OUTPUT.PUT_LINE('✅ اختبار 1 نجح: التحقق من رصيد كافي');
        DBMS_OUTPUT.PUT_LINE('   النتيجة: ' || v_result);
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ اختبار 1 فشل: ' || v_result);
    END IF;
END;
/

-- اختبار 2: التحقق من رصيد الصراف - رصيد غير كافي
DECLARE
    v_result VARCHAR2(4000);
BEGIN
    v_result := CHECK_MONEY_CHANGER_BALANCE(9001, 150000, 'SAR');
    
    IF v_result LIKE 'ERROR:%' THEN
        DBMS_OUTPUT.PUT_LINE('✅ اختبار 2 نجح: التحقق من رصيد غير كافي');
        DBMS_OUTPUT.PUT_LINE('   النتيجة: ' || v_result);
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ اختبار 2 فشل: ' || v_result);
    END IF;
END;
/

-- اختبار 3: التحقق من توزيعات الموردين - صحيحة
DECLARE
    v_result VARCHAR2(4000);
    v_distributions CLOB := '[{"supplier_id": 9001, "amount": 9000}, {"supplier_id": 9002, "amount": 6000}]';
BEGIN
    v_result := VALIDATE_SUPPLIER_DISTRIBUTIONS(9001, v_distributions);
    
    IF v_result LIKE 'OK:%' THEN
        DBMS_OUTPUT.PUT_LINE('✅ اختبار 3 نجح: توزيعات صحيحة');
        DBMS_OUTPUT.PUT_LINE('   النتيجة: ' || v_result);
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ اختبار 3 فشل: ' || v_result);
    END IF;
END;
/

-- اختبار 4: التحقق من توزيعات الموردين - مبالغ غير متطابقة
DECLARE
    v_result VARCHAR2(4000);
    v_distributions CLOB := '[{"supplier_id": 9001, "amount": 8000}, {"supplier_id": 9002, "amount": 6000}]';
BEGIN
    v_result := VALIDATE_SUPPLIER_DISTRIBUTIONS(9001, v_distributions);
    
    IF v_result LIKE 'ERROR:%' THEN
        DBMS_OUTPUT.PUT_LINE('✅ اختبار 4 نجح: اكتشاف عدم تطابق المبالغ');
        DBMS_OUTPUT.PUT_LINE('   النتيجة: ' || v_result);
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ اختبار 4 فشل: ' || v_result);
    END IF;
END;
/

-- اختبار 5: التحقق الشامل - جميع البيانات صحيحة
DECLARE
    v_result VARCHAR2(4000);
    v_distributions CLOB := '[{"supplier_id": 9001, "amount": 9000}, {"supplier_id": 9002, "amount": 6000}]';
BEGIN
    v_result := VALIDATE_TRANSFER_EXECUTION(9001, 9001, 15000, 'SAR', v_distributions);
    
    IF v_result LIKE 'OK:%' THEN
        DBMS_OUTPUT.PUT_LINE('✅ اختبار 5 نجح: التحقق الشامل');
        DBMS_OUTPUT.PUT_LINE('   النتيجة: ' || v_result);
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ اختبار 5 فشل: ' || v_result);
    END IF;
END;
/

-- =====================================================
-- اختبارات تنفيذ الحوالة
-- Transfer Execution Tests
-- =====================================================

-- اختبار 6: تنفيذ حوالة كاملة
DECLARE
    v_distributions CLOB := '[{"supplier_id": 9001, "amount": 9000}, {"supplier_id": 9002, "amount": 6000}]';
    v_balance_before NUMBER;
    v_balance_after NUMBER;
    v_supplier1_balance NUMBER;
    v_supplier2_balance NUMBER;
BEGIN
    -- الحصول على الرصيد قبل التنفيذ
    SELECT current_balance INTO v_balance_before
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 9001 AND currency_code = 'SAR';
    
    -- تنفيذ الحوالة
    EXECUTE_TRANSFER_ACCOUNTING(
        p_transfer_id => 9001,
        p_money_changer_id => 9001,
        p_total_amount => 15000,
        p_currency_code => 'SAR',
        p_supplier_distributions => v_distributions,
        p_user_id => 1
    );
    
    -- التحقق من النتائج
    SELECT current_balance INTO v_balance_after
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 9001 AND currency_code = 'SAR';
    
    -- التحقق من أرصدة الموردين
    SELECT NVL(current_balance, 0) INTO v_supplier1_balance
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'SUPPLIER' AND entity_id = 9001 AND currency_code = 'SAR';
    
    SELECT NVL(current_balance, 0) INTO v_supplier2_balance
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'SUPPLIER' AND entity_id = 9002 AND currency_code = 'SAR';
    
    -- التحقق من صحة النتائج
    IF (v_balance_before - v_balance_after = 15000) AND 
       (v_supplier1_balance = 9000) AND 
       (v_supplier2_balance = 6000) THEN
        DBMS_OUTPUT.PUT_LINE('✅ اختبار 6 نجح: تنفيذ الحوالة');
        DBMS_OUTPUT.PUT_LINE('   رصيد الصراف: ' || v_balance_before || ' -> ' || v_balance_after);
        DBMS_OUTPUT.PUT_LINE('   رصيد المورد 1: ' || v_supplier1_balance);
        DBMS_OUTPUT.PUT_LINE('   رصيد المورد 2: ' || v_supplier2_balance);
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ اختبار 6 فشل: أرصدة غير صحيحة');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('❌ اختبار 6 فشل: ' || SQLERRM);
END;
/

-- اختبار 7: إلغاء الحوالة
DECLARE
    v_balance_before NUMBER;
    v_balance_after NUMBER;
    v_supplier1_balance NUMBER;
    v_supplier2_balance NUMBER;
BEGIN
    -- الحصول على الرصيد قبل الإلغاء
    SELECT current_balance INTO v_balance_before
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 9001 AND currency_code = 'SAR';
    
    -- إلغاء الحوالة
    CANCEL_TRANSFER_ACCOUNTING(
        p_transfer_id => 9001,
        p_user_id => 1,
        p_cancellation_reason => 'اختبار إلغاء الحوالة'
    );
    
    -- التحقق من النتائج
    SELECT current_balance INTO v_balance_after
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER' AND entity_id = 9001 AND currency_code = 'SAR';
    
    -- التحقق من أرصدة الموردين
    SELECT current_balance INTO v_supplier1_balance
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'SUPPLIER' AND entity_id = 9001 AND currency_code = 'SAR';
    
    SELECT current_balance INTO v_supplier2_balance
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'SUPPLIER' AND entity_id = 9002 AND currency_code = 'SAR';
    
    -- التحقق من صحة النتائج (يجب أن تعود الأرصدة كما كانت)
    IF (v_balance_after - v_balance_before = 15000) AND 
       (v_supplier1_balance = 0) AND 
       (v_supplier2_balance = 0) THEN
        DBMS_OUTPUT.PUT_LINE('✅ اختبار 7 نجح: إلغاء الحوالة');
        DBMS_OUTPUT.PUT_LINE('   رصيد الصراف: ' || v_balance_before || ' -> ' || v_balance_after);
        DBMS_OUTPUT.PUT_LINE('   رصيد المورد 1: ' || v_supplier1_balance);
        DBMS_OUTPUT.PUT_LINE('   رصيد المورد 2: ' || v_supplier2_balance);
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ اختبار 7 فشل: أرصدة غير صحيحة بعد الإلغاء');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('❌ اختبار 7 فشل: ' || SQLERRM);
END;
/

-- =====================================================
-- تنظيف بيانات الاختبار
-- Cleanup Test Data
-- =====================================================

-- حذف بيانات الاختبار
DELETE FROM transfer_supplier_distributions WHERE transfer_id = 9001;
DELETE FROM transfer_activity_log WHERE transfer_id = 9001;
DELETE FROM CURRENT_BALANCES WHERE entity_id IN (9001, 9002) AND entity_type_code IN ('SUPPLIER', 'MONEY_CHANGER');
DELETE FROM transfers WHERE id = 9001;
DELETE FROM suppliers WHERE id IN (9001, 9002);
DELETE FROM money_changers WHERE id = 9001;

COMMIT;

-- عرض ملخص الاختبارات
SELECT 'تم الانتهاء من جميع اختبارات النظام المحاسبي' as result FROM DUAL;
