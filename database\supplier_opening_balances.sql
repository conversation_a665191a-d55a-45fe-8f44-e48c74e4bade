-- =====================================================
-- نظام الأرصدة الافتتاحية للموردين
-- Supplier Opening Balances System
-- =====================================================

-- 1. إنشاء جدول الأرصدة الافتتاحية الرئيسي
CREATE TABLE SUPPLIER_OPENING_BALANCES (
    id NUMBER PRIMARY KEY,
    supplier_id NUMBER NOT NULL,
    
    -- معلومات الفترة المحاسبية
    fiscal_period_start_date DATE NOT NULL,
    fiscal_period_end_date DATE,
    fiscal_year NUMBER(4) GENERATED ALWAYS AS (EXTRACT(YEAR FROM fiscal_period_start_date)),
    
    -- معلومات الرصيد
    currency_code VARCHAR2(3) NOT NULL DEFAULT 'SAR',
    opening_balance_amount NUMBER(15,2) NOT NULL,
    exchange_rate NUMBER(15,6) DEFAULT 1,
    base_currency_amount NUMBER(15,2) GENERATED ALWAYS AS (opening_balance_amount * exchange_rate),
    
    -- تصنيف الرصيد
    balance_type VARCHAR2(20) DEFAULT 'DEBIT' CHECK (balance_type IN ('DEBIT', 'CREDIT')),
    account_category VARCHAR2(30) DEFAULT 'TRADE' CHECK (account_category IN ('TRADE', 'SERVICE', 'GOVERNMENT', 'INTERNAL')),
    
    -- معلومات إضافية
    notes VARCHAR2(1000),
    reference_document VARCHAR2(100), -- مرجع المستند المصدر
    source_system VARCHAR2(50) DEFAULT 'MANUAL', -- مصدر البيانات
    
    -- حالة الرصيد
    status VARCHAR2(20) DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'UNDER_REVIEW', 'APPROVED', 'POSTED', 'CANCELLED')),
    
    -- معلومات المراجعة والاعتماد
    reviewed_date TIMESTAMP,
    reviewed_by NUMBER,
    reviewed_notes VARCHAR2(500),
    
    approved_date TIMESTAMP,
    approved_by NUMBER,
    approval_notes VARCHAR2(500),
    
    posted_date TIMESTAMP,
    posted_by NUMBER,
    posting_reference VARCHAR2(100), -- مرجع الترحيل
    
    -- معلومات التدقيق
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER NOT NULL,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by NUMBER,
    
    -- معلومات النسخة والتتبع
    version_number NUMBER DEFAULT 1,
    is_active NUMBER(1) DEFAULT 1 CHECK (is_active IN (0, 1)),
    
    -- قيود إضافية
    CONSTRAINT fk_supplier_opening_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id)
    
    -- قيد فريد لمنع التكرار
    CONSTRAINT uk_supplier_opening_unique UNIQUE (supplier_id, fiscal_period_start_date, currency_code)
);

-- 2. إنشاء جدول تاريخ التعديلات (Audit Trail)
CREATE TABLE SUPPLIER_OPENING_BALANCES_AUDIT (
    audit_id NUMBER PRIMARY KEY,
    opening_balance_id NUMBER NOT NULL,
    
    -- نوع العملية
    operation_type VARCHAR2(20) NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE', 'STATUS_CHANGE')),
    
    -- البيانات القديمة والجديدة
    old_values CLOB,
    new_values CLOB,
    changed_fields VARCHAR2(1000), -- قائمة الحقول المتغيرة
    
    -- معلومات العملية
    operation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    operation_by NUMBER NOT NULL,
    operation_reason VARCHAR2(500),
    ip_address VARCHAR2(45),
    user_agent VARCHAR2(500),
    
    -- معلومات إضافية
    session_id VARCHAR2(100),
    transaction_id VARCHAR2(100),
    
    CONSTRAINT fk_opening_audit_balance FOREIGN KEY (opening_balance_id) REFERENCES SUPPLIER_OPENING_BALANCES(id)
);

-- 3. إنشاء جدول الفترات المحاسبية
CREATE TABLE FISCAL_PERIODS (
    id NUMBER PRIMARY KEY,
    period_name VARCHAR2(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    fiscal_year NUMBER(4) NOT NULL,
    
    -- حالة الفترة
    status VARCHAR2(20) DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'CLOSED', 'LOCKED')),
    
    -- معلومات الإغلاق
    closed_date TIMESTAMP,
    closed_by NUMBER,
    
    -- معلومات التدقيق
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER NOT NULL,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by NUMBER,
    
    -- قيود
    CONSTRAINT uk_fiscal_period_unique UNIQUE (fiscal_year, start_date)
);

-- 4. إنشاء جدول ملخص الأرصدة الافتتاحية
CREATE TABLE OPENING_BALANCES_SUMMARY (
    id NUMBER PRIMARY KEY,
    fiscal_period_start_date DATE NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    
    -- إحصائيات الأرصدة
    total_suppliers_count NUMBER DEFAULT 0,
    debit_balances_count NUMBER DEFAULT 0,
    credit_balances_count NUMBER DEFAULT 0,
    
    total_debit_amount NUMBER(15,2) DEFAULT 0,
    total_credit_amount NUMBER(15,2) DEFAULT 0,
    net_balance_amount NUMBER(15,2) DEFAULT 0,
    
    -- إحصائيات الحالة
    draft_count NUMBER DEFAULT 0,
    approved_count NUMBER DEFAULT 0,
    posted_count NUMBER DEFAULT 0,
    
    -- معلومات التحديث
    last_calculated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    calculated_by NUMBER,
    
    -- قيود
    CONSTRAINT uk_opening_summary_unique UNIQUE (fiscal_period_start_date, currency_code)
);

-- 5. إنشاء المتسلسلات (Sequences)
CREATE SEQUENCE SUPPLIER_OPENING_BALANCES_SEQ START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SUPPLIER_OPENING_AUDIT_SEQ START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE FISCAL_PERIODS_SEQ START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE OPENING_BALANCES_SUMMARY_SEQ START WITH 1 INCREMENT BY 1 NOCACHE;

-- 6. إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_sup_open_supplier ON SUPPLIER_OPENING_BALANCES(supplier_id);
CREATE INDEX idx_sup_open_fiscal ON SUPPLIER_OPENING_BALANCES(fiscal_period_start_date);
CREATE INDEX idx_sup_open_currency ON SUPPLIER_OPENING_BALANCES(currency_code);
CREATE INDEX idx_sup_open_status ON SUPPLIER_OPENING_BALANCES(status);
CREATE INDEX idx_sup_open_created ON SUPPLIER_OPENING_BALANCES(created_date);
CREATE INDEX idx_sup_open_amount ON SUPPLIER_OPENING_BALANCES(opening_balance_amount);

-- فهارس جدول التدقيق
CREATE INDEX idx_open_audit_balance ON SUPPLIER_OPENING_BALANCES_AUDIT(opening_balance_id);
CREATE INDEX idx_open_audit_date ON SUPPLIER_OPENING_BALANCES_AUDIT(operation_date);
CREATE INDEX idx_open_audit_user ON SUPPLIER_OPENING_BALANCES_AUDIT(operation_by);

-- فهارس الفترات المحاسبية
CREATE INDEX idx_fiscal_year ON FISCAL_PERIODS(fiscal_year);
CREATE INDEX idx_fiscal_dates ON FISCAL_PERIODS(start_date, end_date);

-- 7. إضافة تعليقات للجداول والحقول
COMMENT ON TABLE SUPPLIER_OPENING_BALANCES IS 'جدول الأرصدة الافتتاحية للموردين';
COMMENT ON COLUMN SUPPLIER_OPENING_BALANCES.opening_balance_amount IS 'مبلغ الرصيد الافتتاحي';
COMMENT ON COLUMN SUPPLIER_OPENING_BALANCES.balance_type IS 'نوع الرصيد: مدين أو دائن';
COMMENT ON COLUMN SUPPLIER_OPENING_BALANCES.status IS 'حالة الرصيد: مسودة، تحت المراجعة، معتمد، مرحل';

COMMENT ON TABLE SUPPLIER_OPENING_BALANCES_AUDIT IS 'جدول تتبع تعديلات الأرصدة الافتتاحية';
COMMENT ON TABLE FISCAL_PERIODS IS 'جدول الفترات المحاسبية';
COMMENT ON TABLE OPENING_BALANCES_SUMMARY IS 'جدول ملخص الأرصدة الافتتاحية';

-- إظهار رسالة نجاح
SELECT 'تم إنشاء جداول الأرصدة الافتتاحية بنجاح!' AS status FROM DUAL;
