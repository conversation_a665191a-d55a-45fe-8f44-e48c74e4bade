
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ميزات التصدير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- مكتبات التصدير -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    
    <!-- DataTables -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
</head>
<body>
    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="card border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body text-white">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-download fa-3x opacity-75"></i>
                            </div>
                            <div>
                                <h1 class="h2 mb-1 fw-bold">اختبار ميزات التصدير</h1>
                                <p class="mb-0 opacity-90">اختبار تصدير البيانات إلى Excel و PDF مع تنسيق احترافي</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex gap-2 justify-content-end">
                            <button class="btn btn-light btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-1"></i>
                                تصدير Excel
                            </button>
                            <button class="btn btn-outline-light btn-sm" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-1"></i>
                                تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات التصدير -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-excel me-2"></i>
                            ميزات تصدير Excel
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>تصدير جميع البيانات المعروضة</li>
                            <li><i class="fas fa-check text-success me-2"></i>تنسيق احترافي مع ألوان</li>
                            <li><i class="fas fa-check text-success me-2"></i>عناوين أعمدة واضحة</li>
                            <li><i class="fas fa-check text-success me-2"></i>ورقة إحصائيات منفصلة</li>
                            <li><i class="fas fa-check text-success me-2"></i>رموز العملات الصحيحة</li>
                            <li><i class="fas fa-check text-success me-2"></i>تاريخ ووقت التصدير</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-pdf me-2"></i>
                            ميزات تصدير PDF
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>تخطيط أفقي (Landscape)</li>
                            <li><i class="fas fa-check text-success me-2"></i>جدول منسق بألوان متناوبة</li>
                            <li><i class="fas fa-check text-success me-2"></i>عنوان وتاريخ التصدير</li>
                            <li><i class="fas fa-check text-success me-2"></i>أرقام الصفحات</li>
                            <li><i class="fas fa-check text-success me-2"></i>إحصائيات في نفس الملف</li>
                            <li><i class="fas fa-check text-success me-2"></i>تقليص النصوص الطويلة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول البيانات للاختبار -->
        <div class="card border-0 shadow-sm">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    بيانات الاختبار (10 صنف)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="testTable" class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>كود الصنف</th>
                                <th>اسم الصنف</th>
                                <th>أمر الشراء</th>
                                <th>المورد</th>
                                <th>الكمية</th>
                                <th>سعر الوحدة</th>
                                <th>إجمالي القيمة</th>
                                <th>الوحدة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
        
                            <tr>
                                <td>001-1023-</td>
                                <td><strong>حلوى سي سي عملاق جديد 12×30×22جم</strong></td>
                                <td><span class="badge bg-info">صنف من أمر PO-2025-0015</span></td>
                                <td>شركة وايسدوم هاوس</td>
                                <td>3,000</td>
                                <td>¥115.00</td>
                                <td><strong>¥345,000.00</strong></td>
                                <td>كرتون</td>
                                <td>2025-08-17</td>
                                <td><span class="badge bg-success">مسودة</span></td>
                            </tr>
            
                            <tr>
                                <td>001-1009-</td>
                                <td><strong>علكة ثلاجة العائلة 6*380*2جم</strong></td>
                                <td><span class="badge bg-info">صنف من أمر PO-2025-0013</span></td>
                                <td>شركة يابايشينج-الصين</td>
                                <td>2,111</td>
                                <td>¥162.00</td>
                                <td><strong>¥341,982.00</strong></td>
                                <td>كرتون</td>
                                <td>2025-08-17</td>
                                <td><span class="badge bg-success">مسودة</span></td>
                            </tr>
            
                            <tr>
                                <td>001-0604-</td>
                                <td><strong>علكة بوكر 12×200×3جم</strong></td>
                                <td><span class="badge bg-info">صنف من أمر PO-2025-0014</span></td>
                                <td>شركة يابايشينج-الصين</td>
                                <td>2,062</td>
                                <td>¥148.00</td>
                                <td><strong>¥305,176.00</strong></td>
                                <td>كرتون</td>
                                <td>2025-08-17</td>
                                <td><span class="badge bg-success">مسودة</span></td>
                            </tr>
            
                            <tr>
                                <td>001-0925-</td>
                                <td><strong>حلوى بودرة حامض سوبر 12*100*11جم</strong></td>
                                <td><span class="badge bg-info">صنف من أمر PO-2025-0017</span></td>
                                <td>شركة ياهوا فود كومبنى</td>
                                <td>980</td>
                                <td>¥264.00</td>
                                <td><strong>¥258,720.00</strong></td>
                                <td>كرتون</td>
                                <td>2025-08-17</td>
                                <td><span class="badge bg-success">مسودة</span></td>
                            </tr>
            
                            <tr>
                                <td>001-0792-</td>
                                <td><strong>حلوى كرسبي 12×200×3جم</strong></td>
                                <td><span class="badge bg-info">صنف من أمر PO-2025-0010</span></td>
                                <td>شركة رايسن</td>
                                <td>2,040</td>
                                <td>¥123.00</td>
                                <td><strong>¥250,920.00</strong></td>
                                <td>كرتون</td>
                                <td>2025-08-14</td>
                                <td><span class="badge bg-success">تم التسليم</span></td>
                            </tr>
            
                            <tr>
                                <td>001-0941-</td>
                                <td><strong>حلوى بودرة جامبو 12×48×15جم</strong></td>
                                <td><span class="badge bg-info">صنف من أمر PO-2025-0016</span></td>
                                <td>شركة ياهوا فود كومبنى</td>
                                <td>1,240</td>
                                <td>¥136.00</td>
                                <td><strong>¥168,640.00</strong></td>
                                <td>كرتون</td>
                                <td>2025-08-17</td>
                                <td><span class="badge bg-success">مسودة</span></td>
                            </tr>
            
                            <tr>
                                <td>001-1027-</td>
                                <td><strong>جيلي سليس صودا 24*30*20جم</strong></td>
                                <td><span class="badge bg-info">صنف من أمر PO-2025-0012</span></td>
                                <td>شركة يونجي-الصين</td>
                                <td>928</td>
                                <td>¥135.00</td>
                                <td><strong>¥125,280.00</strong></td>
                                <td>كرتون</td>
                                <td>2025-08-17</td>
                                <td><span class="badge bg-success">تم التسليم</span></td>
                            </tr>
            
                            <tr>
                                <td>001-0385-</td>
                                <td><strong>حلوى جيلي سمارت قلوب 16×30×35جم</strong></td>
                                <td><span class="badge bg-info">صنف من أمر PO-2025-0011</span></td>
                                <td>شركة يونجي-الصين</td>
                                <td>820</td>
                                <td>¥100.00</td>
                                <td><strong>¥82,000.00</strong></td>
                                <td>كرتون</td>
                                <td>2025-08-16</td>
                                <td><span class="badge bg-success">تم التسليم</span></td>
                            </tr>
            
                            <tr>
                                <td>001-0385-</td>
                                <td><strong>حلوى جيلي سمارت قلوب 16×30×35جم</strong></td>
                                <td><span class="badge bg-info">صنف من أمر PO-2025-0012</span></td>
                                <td>شركة يونجي-الصين</td>
                                <td>390</td>
                                <td>¥100.00</td>
                                <td><strong>¥39,000.00</strong></td>
                                <td>كرتون</td>
                                <td>2025-08-17</td>
                                <td><span class="badge bg-success">تم التسليم</span></td>
                            </tr>
            
                            <tr>
                                <td>001-1027-</td>
                                <td><strong>جيلي سليس صودا 24*30*20جم</strong></td>
                                <td><span class="badge bg-info">صنف من أمر PO-2025-0011</span></td>
                                <td>شركة يونجي-الصين</td>
                                <td>280</td>
                                <td>¥135.00</td>
                                <td><strong>¥37,800.00</strong></td>
                                <td>كرتون</td>
                                <td>2025-08-16</td>
                                <td><span class="badge bg-success">تم التسليم</span></td>
                            </tr>
            
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- نتائج الاختبار -->
        <div class="mt-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clipboard-check me-2"></i>
                        نتائج الاختبار
                    </h5>
                </div>
                <div class="card-body">
                    <div id="testResults">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            اضغط على أزرار التصدير أعلاه لاختبار الميزات
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    // خريطة العملات
    const currencySymbols = {{
        'CNY': '¥',
        'USD': '$',
        'EUR': '€',
        'SAR': '﷼'
    }};
    
    function getCurrencySymbol(currencyCode) {{
        return currencySymbols[currencyCode] || currencyCode;
    }}

    // تهيئة DataTable
    $(document).ready(function() {{
        $('#testTable').DataTable({{
            language: {{
                processing: "جاري المعالجة...",
                search: "بحث:",
                lengthMenu: "أظهر _MENU_ مدخلات",
                info: "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                paginate: {{
                    first: "الأول",
                    previous: "السابق", 
                    next: "التالي",
                    last: "الأخير"
                }}
            }},
            pageLength: 10,
            order: [[6, 'desc']]
        }});
    }});

    // دالة تصدير Excel
    function exportToExcel() {{
        console.log('بدء تصدير Excel...');
        
        try {{
            // جلب البيانات من الجدول
            const table = $('#testTable').DataTable();
            const data = table.rows().data().toArray();
            
            // إنشاء workbook
            const wb = XLSX.utils.book_new();
            
            // تحضير البيانات
            const exportData = [];
            
            // العناوين
            exportData.push([
                'كود الصنف', 'اسم الصنف', 'أمر الشراء', 'المورد',
                'الكمية', 'سعر الوحدة', 'إجمالي القيمة', 'الوحدة',
                'تاريخ الإنشاء', 'الحالة'
            ]);
            
            // البيانات من الجدول
            $('#testTable tbody tr').each(function() {{
                const row = [];
                $(this).find('td').each(function() {{
                    row.push($(this).text().trim());
                }});
                exportData.push(row);
            }});
            
            // إنشاء worksheet
            const ws = XLSX.utils.aoa_to_sheet(exportData);
            
            // تنسيق العرض
            ws['!cols'] = [
                {{width: 15}}, {{width: 30}}, {{width: 20}}, {{width: 25}},
                {{width: 12}}, {{width: 15}}, {{width: 18}}, {{width: 10}},
                {{width: 15}}, {{width: 12}}
            ];
            
            XLSX.utils.book_append_sheet(wb, ws, 'بيانات الاختبار');
            
            // تصدير الملف
            const fileName = `اختبار_تصدير_Excel_${{new Date().toISOString().split('T')[0]}}.xlsx`;
            XLSX.writeFile(wb, fileName);
            
            showResult('success', 'تم تصدير Excel بنجاح!', 'تم إنشاء الملف: ' + fileName);
            
        }} catch (error) {{
            showResult('danger', 'فشل تصدير Excel', error.message);
        }}
    }}

    // دالة تصدير PDF
    function exportToPDF() {{
        console.log('بدء تصدير PDF...');
        
        try {{
            const {{ jsPDF }} = window.jspdf;
            const doc = new jsPDF('l', 'mm', 'a4');
            
            // العنوان
            doc.setFontSize(20);
            doc.setTextColor(70, 110, 234);
            doc.text('اختبار تصدير PDF', 148, 20, {{ align: 'center' }});
            
            // معلومات
            doc.setFontSize(10);
            doc.setTextColor(100, 100, 100);
            const exportDate = new Date().toLocaleDateString('ar-SA');
            doc.text(`تاريخ التصدير: ${{exportDate}}`, 20, 35);
            doc.text(`عدد الأصناف: {len(results)}`, 200, 35);
            
            // الجدول
            const tableData = [];
            $('#testTable tbody tr').each(function() {{
                const row = [];
                $(this).find('td').each(function(index) {{
                    let text = $(this).text().trim();
                    // تقليص النصوص الطويلة
                    if (index === 1 && text.length > 25) text = text.substring(0, 25) + '...';
                    if (index === 3 && text.length > 20) text = text.substring(0, 20) + '...';
                    row.push(text);
                }});
                tableData.push(row);
            }});
            
            doc.autoTable({{
                head: [['كود الصنف', 'اسم الصنف', 'أمر الشراء', 'المورد', 'الكمية', 'سعر الوحدة', 'إجمالي القيمة', 'الوحدة', 'تاريخ الإنشاء', 'الحالة']],
                body: tableData,
                startY: 45,
                styles: {{ fontSize: 8, cellPadding: 2 }},
                headStyles: {{ fillColor: [70, 110, 234], textColor: [255, 255, 255] }},
                alternateRowStyles: {{ fillColor: [245, 245, 245] }}
            }});
            
            // حفظ الملف
            const fileName = `اختبار_تصدير_PDF_${{new Date().toISOString().split('T')[0]}}.pdf`;
            doc.save(fileName);
            
            showResult('success', 'تم تصدير PDF بنجاح!', 'تم إنشاء الملف: ' + fileName);
            
        }} catch (error) {{
            showResult('danger', 'فشل تصدير PDF', error.message);
        }}
    }}

    function showResult(type, title, message) {{
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
        
        $('#testResults').html(`
            <div class="alert ${{alertClass}}">
                <h6><i class="fas ${{icon}} me-2"></i>${{title}}</h6>
                <p class="mb-0">${{message}}</p>
            </div>
        `);
    }}
    </script>
</body>
</html>
        