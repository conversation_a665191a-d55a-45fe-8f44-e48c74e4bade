<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API طلبات الحوالات المحدث</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>اختبار API طلبات الحوالات المحدث</h1>
        
        <div class="row">
            <div class="col-md-6">
                <button id="testAPI" class="btn btn-primary mb-3">اختبار API</button>
                <button id="testConsole" class="btn btn-info mb-3">فحص Console</button>
                <button id="clearResult" class="btn btn-warning mb-3">مسح النتائج</button>
            </div>
        </div>
        
        <div id="result" class="mt-3"></div>
        <div id="rawData" class="mt-3"></div>
    </div>

    <script>
        // Test API
        document.getElementById('testAPI').addEventListener('click', async function() {
            console.log('🧪 بدء اختبار API...');
            
            try {
                const response = await fetch('/transfers/api/transfer-requests');
                console.log('📡 استجابة الخادم:', response.status, response.statusText);
                
                const data = await response.json();
                console.log('📊 البيانات المستلمة:', data);
                
                const resultDiv = document.getElementById('result');
                const rawDataDiv = document.getElementById('rawData');
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h5>✅ نجح الاختبار!</h5>
                            <p>تم تحميل <strong>${data.count}</strong> طلب بنجاح</p>
                        </div>
                    `;
                    
                    if (data.data && data.data.length > 0) {
                        const firstRecord = data.data[0];
                        resultDiv.innerHTML += `
                            <div class="card">
                                <div class="card-header">أول سجل:</div>
                                <div class="card-body">
                                    <p><strong>ID:</strong> ${firstRecord.id}</p>
                                    <p><strong>رقم الطلب:</strong> ${firstRecord.request_number}</p>
                                    <p><strong>المستفيد:</strong> ${firstRecord.beneficiary_name}</p>
                                    <p><strong>المبلغ:</strong> ${firstRecord.amount} ${firstRecord.currency}</p>
                                    <p><strong>الفرع:</strong> ${firstRecord.branch_name || 'غير محدد'}</p>
                                    <p><strong>الصراف:</strong> ${firstRecord.money_changer_name || 'غير محدد'}</p>
                                    <p><strong>الحالة:</strong> ${firstRecord.status}</p>
                                </div>
                            </div>
                        `;
                    }
                    
                    rawDataDiv.innerHTML = `
                        <div class="card">
                            <div class="card-header">البيانات الخام:</div>
                            <div class="card-body">
                                <pre style="max-height: 400px; overflow-y: auto;">${JSON.stringify(data, null, 2)}</pre>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>❌ فشل الاختبار!</h5>
                            <p>رسالة الخطأ: ${data.message || 'خطأ غير معروف'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ خطأ في الاختبار:', error);
                document.getElementById('result').innerHTML = `
                    <div class="alert alert-danger">
                        <h5>❌ خطأ في الشبكة!</h5>
                        <p>${error.message}</p>
                        <small>تحقق من Console للمزيد من التفاصيل</small>
                    </div>
                `;
            }
        });
        
        // Test console
        document.getElementById('testConsole').addEventListener('click', function() {
            console.log('🔍 اختبار Console...');
            console.log('📍 الموقع الحالي:', window.location.href);
            console.log('🌐 معلومات المتصفح:', navigator.userAgent);
            console.log('📊 حالة الصفحة:', document.readyState);
            
            // Test fetch availability
            if (typeof fetch !== 'undefined') {
                console.log('✅ fetch متاح');
            } else {
                console.log('❌ fetch غير متاح');
            }
            
            document.getElementById('result').innerHTML = `
                <div class="alert alert-info">
                    تم تشغيل اختبارات Console - تحقق من أدوات المطور
                </div>
            `;
        });
        
        // Clear results
        document.getElementById('clearResult').addEventListener('click', function() {
            document.getElementById('result').innerHTML = '';
            document.getElementById('rawData').innerHTML = '';
            console.clear();
        });
        
        // Auto-run test on page load
        window.addEventListener('load', function() {
            console.log('📄 تم تحميل الصفحة');
            setTimeout(() => {
                document.getElementById('testAPI').click();
            }, 1000);
        });
    </script>
</body>
</html>