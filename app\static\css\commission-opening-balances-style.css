/* 
تصميم نظام عمولات مندوبي المشتريات - مطابق لتصميم الأرصدة الافتتاحية
Commission Representatives System - Matching Opening Balances Design
*/

:root {
    --primary: #2c3e50;
    --secondary: #3498db;
    --success: #27ae60;
    --warning: #f39c12;
    --danger: #e74c3c;
    --info: #17a2b8;
    --light: #f8f9fa;
    --dark: #343a40;
    --surface: #ffffff;
    --border: #dee2e6;
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --radius: 12px;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: var(--text-primary);
}

/* Header */
.page-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
    font-weight: 300;
}

/* Breadcrumb */
.breadcrumb-container {
    background: var(--surface);
    padding: 1rem 0;
    border-bottom: 1px solid var(--border);
    margin-bottom: 2rem;
}

.breadcrumb {
    background: none;
    margin-bottom: 0;
    padding: 0;
}

.breadcrumb-item {
    font-size: 0.9rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: var(--text-muted);
}

.breadcrumb-item.active {
    color: var(--primary);
    font-weight: 600;
}

/* Control Panel */
.control-panel {
    background: var(--surface);
    border-radius: var(--radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border);
}

.control-panel h5 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Buttons */
.btn-modern {
    border-radius: var(--radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border: none;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.btn-primary.btn-modern {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: white;
}

.btn-success.btn-modern {
    background: linear-gradient(135deg, var(--success) 0%, #2ecc71 100%);
    color: white;
}

.btn-warning.btn-modern {
    background: linear-gradient(135deg, var(--warning) 0%, #e67e22 100%);
    color: white;
}

.btn-danger.btn-modern {
    background: linear-gradient(135deg, var(--danger) 0%, #c0392b 100%);
    color: white;
}

.btn-info.btn-modern {
    background: linear-gradient(135deg, var(--info) 0%, #3498db 100%);
    color: white;
}

.btn-light.btn-modern {
    background: var(--surface);
    color: var(--text-primary);
    border: 1px solid var(--border);
}

/* Stats Cards */
.stats-container {
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--surface);
    border-radius: var(--radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border);
    transition: all 0.3s ease;
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
}

.stat-icon.primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
}

.stat-icon.success {
    background: linear-gradient(135deg, var(--success) 0%, #2ecc71 100%);
}

.stat-icon.warning {
    background: linear-gradient(135deg, var(--warning) 0%, #e67e22 100%);
}

.stat-icon.info {
    background: linear-gradient(135deg, var(--info) 0%, #3498db 100%);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Data Table Container */
.data-table-container {
    background: var(--surface);
    border-radius: var(--radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border);
}

.data-table-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
}

.data-table-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Table Styling */
.table-modern {
    margin-bottom: 0;
}

.table-modern thead th {
    background: linear-gradient(135deg, var(--light) 0%, #e9ecef 100%);
    color: var(--text-primary);
    font-weight: 600;
    border: none;
    padding: 1rem 0.75rem;
    font-size: 0.9rem;
}

.table-modern tbody td {
    padding: 1rem 0.75rem;
    border-top: 1px solid var(--border);
    vertical-align: middle;
}

.table-modern tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* Form Controls */
.form-control-modern {
    border-radius: var(--radius);
    border: 1px solid var(--border);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: var(--surface);
}

.form-control-modern:focus {
    border-color: var(--secondary);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    background: var(--surface);
}

.form-label {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Modal Styling */
.modal-content {
    border-radius: var(--radius);
    border: none;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: white;
    border-radius: var(--radius) var(--radius) 0 0;
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid var(--border);
    padding: 1rem 2rem;
}

/* Badges */
.badge-modern {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.8rem;
}

/* Alerts */
.alert-modern {
    border-radius: var(--radius);
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border);
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .control-panel {
        padding: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .data-table-container {
        padding: 1rem;
    }
    
    .btn-modern {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}
