/**
 * النظام المحاسبي المتقدم - ملف JavaScript الرئيسي
 * Advanced Accounting System - Main JavaScript File
 */

// Global Variables
let currentLanguage = document.documentElement.lang || 'ar';
let textDirection = document.documentElement.dir || 'rtl';

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    initializeTooltips();
    initializePopovers();
    initializeDataTables();
    initializeFormValidation();
    initializeNotifications();
});

/**
 * Initialize Application
 */
function initializeApp() {
    console.log('تم تحميل النظام المحاسبي المتقدم');
    
    // Add fade-in animation to main content
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }
    
    // Initialize sidebar toggle for mobile
    initializeSidebarToggle();
    
    // Initialize auto-save functionality
    initializeAutoSave();
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
}

/**
 * Initialize Tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Initialize Popovers
 */
function initializePopovers() {
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * Initialize DataTables
 */
function initializeDataTables() {
    if (typeof $.fn.DataTable !== 'undefined') {
        $('.data-table').DataTable({
            language: {
                url: currentLanguage === 'ar' ? 
                    'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json' : 
                    'https://cdn.datatables.net/plug-ins/1.13.6/i18n/en-GB.json'
            },
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']],
            columnDefs: [
                {
                    targets: 'no-sort',
                    orderable: false
                }
            ]
        });
    }
}

/**
 * Initialize Form Validation
 */
function initializeFormValidation() {
    // Bootstrap validation
    const forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // Custom validation rules
    addCustomValidationRules();
}

/**
 * Add Custom Validation Rules
 */
function addCustomValidationRules() {
    // Arabic text validation
    const arabicInputs = document.querySelectorAll('.arabic-only');
    arabicInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            const arabicRegex = /^[\u0600-\u06FF\s\d]+$/;
            if (this.value && !arabicRegex.test(this.value)) {
                this.setCustomValidity('يرجى إدخال نص باللغة العربية فقط');
            } else {
                this.setCustomValidity('');
            }
        });
    });
    
    // English text validation
    const englishInputs = document.querySelectorAll('.english-only');
    englishInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            const englishRegex = /^[a-zA-Z\s\d]+$/;
            if (this.value && !englishRegex.test(this.value)) {
                this.setCustomValidity('Please enter English text only');
            } else {
                this.setCustomValidity('');
            }
        });
    });
    
    // Number validation
    const numberInputs = document.querySelectorAll('.numbers-only');
    numberInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            this.value = this.value.replace(/[^\d.]/g, '');
        });
    });
}

/**
 * Initialize Notifications
 */
function initializeNotifications() {
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

/**
 * Initialize Sidebar Toggle
 */
function initializeSidebarToggle() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
        });
    }
}

/**
 * Initialize Auto-Save
 */
function initializeAutoSave() {
    const autoSaveForms = document.querySelectorAll('.auto-save');
    autoSaveForms.forEach(function(form) {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(function(input) {
            input.addEventListener('change', function() {
                autoSaveForm(form);
            });
        });
    });
}

/**
 * Auto-save form data
 */
function autoSaveForm(form) {
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    localStorage.setItem('autosave_' + form.id, JSON.stringify(data));
    showNotification('تم حفظ البيانات تلقائياً', 'info', 2000);
}

/**
 * Load auto-saved data
 */
function loadAutoSavedData(formId) {
    const savedData = localStorage.getItem('autosave_' + formId);
    if (savedData) {
        const data = JSON.parse(savedData);
        const form = document.getElementById(formId);
        
        for (let [key, value] of Object.entries(data)) {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = value;
            }
        }
    }
}

/**
 * Initialize Keyboard Shortcuts
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+S or Cmd+S to save
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            const saveButton = document.querySelector('.btn-save, [type="submit"]');
            if (saveButton) {
                saveButton.click();
            }
        }
        
        // Ctrl+N or Cmd+N for new record
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            const newButton = document.querySelector('.btn-new');
            if (newButton) {
                newButton.click();
            }
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) {
                    modal.hide();
                }
            }
        }
    });
}

/**
 * Show Notification
 */
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        ${textDirection === 'rtl' ? 'right' : 'left'}: 20px;
        z-index: 9999;
        min-width: 300px;
    `;
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(function() {
        const bsAlert = new bootstrap.Alert(notification);
        bsAlert.close();
    }, duration);
}

/**
 * Confirm Dialog
 */
function confirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * Format Currency
 */
function formatCurrency(amount, currency = 'SAR') {
    const formatter = new Intl.NumberFormat(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
    });
    
    return formatter.format(amount);
}

/**
 * Format Date
 */
function formatDate(date, format = 'short') {
    const formatter = new Intl.DateTimeFormat(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', {
        dateStyle: format
    });
    
    return formatter.format(new Date(date));
}

/**
 * Validate Form
 */
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

/**
 * Reset Form
 */
function resetForm(formId) {
    const form = document.getElementById(formId);
    if (form) {
        form.reset();
        form.classList.remove('was-validated');
        
        // Remove validation classes
        const inputs = form.querySelectorAll('.is-invalid, .is-valid');
        inputs.forEach(function(input) {
            input.classList.remove('is-invalid', 'is-valid');
        });
        
        // Clear auto-saved data
        localStorage.removeItem('autosave_' + formId);
    }
}

/**
 * Load Data via AJAX
 */
function loadData(url, callback) {
    fetch(url)
        .then(response => response.json())
        .then(data => callback(data))
        .catch(error => {
            console.error('Error loading data:', error);
            showNotification('حدث خطأ في تحميل البيانات', 'danger');
        });
}

/**
 * Submit Form via AJAX
 */
function submitForm(formId, successCallback) {
    const form = document.getElementById(formId);
    if (!form) return;
    
    const formData = new FormData(form);
    
    fetch(form.action, {
        method: form.method,
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'تم الحفظ بنجاح', 'success');
            if (successCallback) {
                successCallback(data);
            }
        } else {
            showNotification(data.message || 'حدث خطأ', 'danger');
        }
    })
    .catch(error => {
        console.error('Error submitting form:', error);
        showNotification('حدث خطأ في الإرسال', 'danger');
    });
}

/**
 * Print Page
 */
function printPage() {
    window.print();
}

/**
 * Export to Excel
 */
function exportToExcel(tableId, filename = 'export') {
    // This would require a library like SheetJS
    showNotification('ميزة التصدير قيد التطوير', 'info');
}

/**
 * Toggle Theme
 */
function toggleTheme() {
    const body = document.body;
    body.classList.toggle('dark-theme');
    
    const isDark = body.classList.contains('dark-theme');
    localStorage.setItem('theme', isDark ? 'dark' : 'light');
}

/**
 * Load Theme Preference
 */
function loadThemePreference() {
    const theme = localStorage.getItem('theme');
    if (theme === 'dark') {
        document.body.classList.add('dark-theme');
    }
}

// Load theme preference on page load
loadThemePreference();
