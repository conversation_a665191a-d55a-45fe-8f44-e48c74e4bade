<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلبات الحوالات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/voice-search.css') }}" rel="stylesheet">
    
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --success: #27ae60;
            --warning: #f39c12;
            --danger: #e74c3c;
            --info: #17a2b8;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .card-modern {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .table-modern thead th {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border: none;
            padding: 1rem;
            font-weight: 600;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 0 2px;
            transition: all 0.3s ease;
        }

        .btn-view { background: var(--info); color: white; }
        .btn-edit { background: var(--warning); color: white; }
        .btn-print { background: #6f42c1; color: white; }
        .btn-delete { background: var(--danger); color: white; }
        .btn-approve { background: var(--success); color: white; }
        .btn-execute { background: #007bff; color: white; }
        .btn-docs { background: var(--info); color: white; }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
        }

        .control-panel {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-control-modern {
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 0.75rem;
        }

        .btn-modern {
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }

        .breadcrumb-container {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .breadcrumb {
            background: none;
            margin: 0;
            padding: 0;
        }

        .breadcrumb-item a {
            color: var(--secondary);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary);
        }

        .breadcrumb-item.active {
            color: var(--primary);
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid px-3">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-exchange-alt me-3"></i>
                        طلبات الحوالات
                    </h1>
                    <p class="mb-0 opacity-75">إدارة ومتابعة جميع طلبات التحويلات المالية</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2 justify-content-end">
                        <button class="btn btn-success btn-lg" onclick="addNewRequest()">
                            <i class="fas fa-plus me-2"></i>إضافة طلب جديد
                        </button>
                        <button class="btn btn-primary btn-lg" onclick="loadData()">
                            <i class="fas fa-sync-alt me-2"></i>تحديث
                        </button>
                        <button class="btn btn-info btn-lg" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-2"></i>تصدير
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
        <div class="container-fluid px-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/dashboard">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="/transfers">
                            <i class="fas fa-exchange-alt me-1"></i>الحوالات
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-list me-1"></i>قائمة طلبات الحوالات
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid px-3">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h3 class="mb-1" id="totalRequests">0</h3>
                            <p class="text-muted mb-0">إجمالي الطلبات</p>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h3 class="mb-1" id="pendingRequests">0</h3>
                            <p class="text-muted mb-0">طلبات معلقة</p>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h3 class="mb-1" id="approvedRequests">0</h3>
                            <p class="text-muted mb-0">طلبات معتمدة</p>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h3 class="mb-1" id="totalAmount">0.00</h3>
                            <p class="text-muted mb-0">إجمالي المبالغ</p>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="control-panel">
            <div class="row align-items-end">
                <div class="col-lg-3 col-md-6 mb-3">
                    <label class="form-label fw-bold">البحث السريع</label>
                    <div class="input-group">
                        <input type="text" id="searchInput" class="form-control form-control-modern"
                               placeholder="ابحث برقم الطلب أو المستفيد..." autocomplete="off">
                        <button type="button" class="btn btn-outline-primary voice-search-btn" id="voiceSearchBtn" title="البحث الصوتي">
                            <i class="fas fa-microphone text-primary"></i>
                        </button>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label fw-bold">الحالة</label>
                    <select id="statusFilter" class="form-control form-control-modern">
                        <option value="">جميع الحالات</option>
                        <option value="pending">معلق</option>
                        <option value="approved">معتمد</option>
                        <option value="rejected">مرفوض</option>
                        <option value="executed">منفذ</option>
                    </select>
                </div>
                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label fw-bold">العملة</label>
                    <select id="currencyFilter" class="form-control form-control-modern">
                        <option value="">جميع العملات</option>
                        <option value="USD">دولار أمريكي</option>
                        <option value="EUR">يورو</option>
                        <option value="SAR">ريال سعودي</option>
                        <option value="AED">درهم إماراتي</option>
                    </select>
                </div>
                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label fw-bold">الفرع</label>
                    <select id="branchFilter" class="form-control form-control-modern">
                        <option value="">جميع الفروع</option>
                    </select>
                </div>
                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label fw-bold">الصراف/البنك</label>
                    <select id="moneyChangerFilter" class="form-control form-control-modern">
                        <option value="">جميع الصرافين</option>
                    </select>
                </div>
                <div class="col-lg-1 col-md-6 mb-3">
                    <label class="form-label fw-bold">&nbsp;</label>
                    <div>
                        <button class="btn btn-warning btn-sm" onclick="clearFilters()" title="مسح جميع الفلاتر">
                            <i class="fas fa-eraser"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfer Requests Table -->
        <div class="card-modern">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        قائمة طلبات الحوالات
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-warning btn-sm" onclick="bulkApprove()">
                            <i class="fas fa-check me-1"></i>اعتماد جماعي
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="bulkReject()">
                            <i class="fas fa-times me-1"></i>رفض جماعي
                        </button>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>رقم الطلب</th>
                            <th>المستفيد</th>
                            <th>المبلغ</th>
                            <th>العملة</th>
                            <th>الفرع</th>
                            <th>الصراف</th>
                            <th>نوع الحوالة</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="transferRequestsTableBody">
                        <tr>
                            <td colspan="11" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <div class="mt-2">جاري تحميل البيانات...</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div>جاري معالجة الطلب...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        var transferRequestsData = [];
        var filteredData = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة');

            // تطبيق الفلاتر من URL
            applyFiltersFromURL();

            loadData();
            initializeEventListeners();
        });

        // Initialize event listeners
        function initializeEventListeners() {
            // Search input
            document.getElementById('searchInput').addEventListener('input', filterData);
            
            // Filter selects
            document.getElementById('statusFilter').addEventListener('change', filterData);
            document.getElementById('currencyFilter').addEventListener('change', filterData);
            document.getElementById('branchFilter').addEventListener('change', filterData);
            document.getElementById('moneyChangerFilter').addEventListener('change', filterData);
            
            // Select all checkbox
            document.getElementById('selectAll').addEventListener('change', function() {
                var checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
                for (var i = 0; i < checkboxes.length; i++) {
                    checkboxes[i].checked = this.checked;
                }
            });
        }

        // Load data from API
        function loadData() {
            console.log('بدء تحميل البيانات...');
            showLoading();
            
            fetch('/transfers/api/transfer-requests')
                .then(function(response) {
                    console.log('استجابة الخادم:', response.status);
                    
                    if (!response.ok) {
                        throw new Error('HTTP error! status: ' + response.status);
                    }
                    
                    return response.json();
                })
                .then(function(data) {
                    console.log('البيانات المستلمة:', data);
                    
                    if (data.success) {
                        transferRequestsData = data.data || [];
                        filteredData = transferRequestsData.slice(); // copy array
                        console.log('عدد السجلات:', transferRequestsData.length);
                        updateStatistics();
                        updateFilterOptions();

                        // تطبيق الفلاتر من URL بعد تحميل البيانات
                        setTimeout(function() {
                            applyFiltersFromURL();
                            filterData(); // تطبيق الفلاتر
                        }, 100);

                        renderTable();
                    } else {
                        console.error('خطأ في البيانات:', data.message);
                        showError('فشل في تحميل البيانات: ' + (data.message || 'خطأ غير معروف'));
                    }
                })
                .catch(function(error) {
                    console.error('خطأ في تحميل البيانات:', error);
                    showError('حدث خطأ في تحميل البيانات: ' + error.message);
                    
                    // عرض بيانات تجريبية للاختبار
                    transferRequestsData = [
                        {
                            id: 1,
                            request_number: 'TR-2025-0001',
                            beneficiary_name: 'مستفيد تجريبي',
                            amount: 1000,
                            currency: 'USD',
                            branch_name: 'الفرع الرئيسي',
                            money_changer_name: 'صراف تجريبي',
                            transfer_type: 'bank',
                            status: 'pending',
                            created_at: '2025-09-09 00:16:18'
                        }
                    ];
                    filteredData = transferRequestsData.slice();
                    updateStatistics();
                    renderTable();
                })
                .finally(function() {
                    hideLoading();
                });
        }

        // Test API function
        function testAPI() {
            console.log('بدء اختبار API...');
            
            fetch('/transfers/api/transfer-requests')
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    if (data.success) {
                        alert('✅ اختبار API نجح!\nعدد السجلات: ' + data.count);
                    } else {
                        alert('❌ اختبار API فشل!\nالرسالة: ' + data.message);
                    }
                })
                .catch(function(error) {
                    alert('❌ خطأ في اختبار API!\nالخطأ: ' + error.message);
                });
        }

        // Update statistics
        function updateStatistics() {
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');

            // إحصائيات عامة من جميع البيانات
            var allStats = {
                total: transferRequestsData.length,
                pending: transferRequestsData.filter(function(r) { return r.status === 'pending'; }).length,
                approved: transferRequestsData.filter(function(r) { return r.status === 'approved'; }).length,
                completed: transferRequestsData.filter(function(r) { return r.status === 'completed'; }).length,
                totalAmount: transferRequestsData.reduce(function(sum, r) { return sum + parseFloat(r.amount || 0); }, 0)
            };

            // إحصائيات البيانات المعروضة حالياً (المفلترة)
            var currentStats = {
                total: filteredData.length,
                pending: filteredData.filter(function(r) { return r.status === 'pending'; }).length,
                approved: filteredData.filter(function(r) { return r.status === 'approved'; }).length,
                completed: filteredData.filter(function(r) { return r.status === 'completed'; }).length,
                totalAmount: filteredData.reduce(function(sum, r) { return sum + parseFloat(r.amount || 0); }, 0)
            };

            // تحديث البطاقات بشكل منطقي
            if (status === 'pending') {
                // صفحة الطلبات المعلقة - عرض إحصائيات الطلبات المعلقة
                document.getElementById('totalRequests').textContent = currentStats.total;
                document.getElementById('pendingRequests').textContent = currentStats.total;
                document.getElementById('approvedRequests').textContent = allStats.approved;
                document.getElementById('totalAmount').textContent = formatCurrencyEnglish(currentStats.totalAmount);
            } else if (status === 'approved') {
                // صفحة تنفيذ الحوالات - عرض إحصائيات الطلبات المعتمدة
                document.getElementById('totalRequests').textContent = currentStats.total;
                document.getElementById('pendingRequests').textContent = allStats.pending;
                document.getElementById('approvedRequests').textContent = currentStats.total;
                document.getElementById('totalAmount').textContent = formatCurrencyEnglish(currentStats.totalAmount);
            } else {
                // صفحة عامة - عرض جميع الإحصائيات
                document.getElementById('totalRequests').textContent = allStats.total;
                document.getElementById('pendingRequests').textContent = allStats.pending;
                document.getElementById('approvedRequests').textContent = allStats.approved;
                document.getElementById('totalAmount').textContent = formatCurrencyEnglish(allStats.totalAmount);
            }
        }

        // Update filter options
        function updateFilterOptions() {
            // Update branch filter
            var branches = [];
            for (var i = 0; i < transferRequestsData.length; i++) {
                var branchName = transferRequestsData[i].branch_name;
                if (branchName && branchName !== 'غير محدد' && branches.indexOf(branchName) === -1) {
                    branches.push(branchName);
                }
            }

            var branchFilter = document.getElementById('branchFilter');
            branchFilter.innerHTML = '<option value="">جميع الفروع</option>';
            for (var i = 0; i < branches.length; i++) {
                branchFilter.innerHTML += '<option value="' + branches[i] + '">' + branches[i] + '</option>';
            }

            // Update money changer filter
            var moneyChangers = [];
            for (var i = 0; i < transferRequestsData.length; i++) {
                var moneyChangerName = transferRequestsData[i].money_changer_name;
                if (moneyChangerName && moneyChangerName !== 'غير محدد' && moneyChangers.indexOf(moneyChangerName) === -1) {
                    moneyChangers.push(moneyChangerName);
                }
            }

            var moneyChangerFilter = document.getElementById('moneyChangerFilter');
            moneyChangerFilter.innerHTML = '<option value="">جميع الصرافين</option>';
            for (var i = 0; i < moneyChangers.length; i++) {
                moneyChangerFilter.innerHTML += '<option value="' + moneyChangers[i] + '">' + moneyChangers[i] + '</option>';
            }
        }

        // Filter data
        function filterData() {
            var searchTerm = document.getElementById('searchInput').value.toLowerCase();
            var statusFilter = document.getElementById('statusFilter').value;
            var currencyFilter = document.getElementById('currencyFilter').value;
            var branchFilter = document.getElementById('branchFilter').value;
            var moneyChangerFilter = document.getElementById('moneyChangerFilter').value;

            filteredData = transferRequestsData.filter(function(request) {
                var matchesSearch = !searchTerm ||
                    request.request_number.toLowerCase().indexOf(searchTerm) !== -1 ||
                    request.beneficiary_name.toLowerCase().indexOf(searchTerm) !== -1;

                var matchesStatus = !statusFilter || request.status === statusFilter;
                var matchesCurrency = !currencyFilter || request.currency === currencyFilter;
                var matchesBranch = !branchFilter || request.branch_name === branchFilter;
                var matchesMoneyChanger = !moneyChangerFilter || request.money_changer_name === moneyChangerFilter;

                return matchesSearch && matchesStatus && matchesCurrency && matchesBranch && matchesMoneyChanger;
            });

            renderTable();
        }

        // Clear filters
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('currencyFilter').value = '';
            document.getElementById('branchFilter').value = '';
            document.getElementById('moneyChangerFilter').value = '';
            filterData();
        }

        // Apply filters from URL parameters
        function applyFiltersFromURL() {
            var urlParams = new URLSearchParams(window.location.search);

            // تطبيق فلتر الحالة
            var status = urlParams.get('status');
            if (status) {
                document.getElementById('statusFilter').value = status;
                console.log('تم تطبيق فلتر الحالة:', status);
            }

            // تطبيق فلتر العملة
            var currency = urlParams.get('currency');
            if (currency) {
                document.getElementById('currencyFilter').value = currency;
                console.log('تم تطبيق فلتر العملة:', currency);
            }

            // تطبيق فلتر الفرع
            var branch = urlParams.get('branch');
            if (branch) {
                document.getElementById('branchFilter').value = branch;
                console.log('تم تطبيق فلتر الفرع:', branch);
            }

            // تطبيق فلتر الصراف
            var moneyChanger = urlParams.get('money_changer');
            if (moneyChanger) {
                document.getElementById('moneyChangerFilter').value = moneyChanger;
                console.log('تم تطبيق فلتر الصراف:', moneyChanger);
            }

            // تطبيق البحث
            var search = urlParams.get('search');
            if (search) {
                document.getElementById('searchInput').value = search;
                console.log('تم تطبيق البحث:', search);
            }

            // تحديث عنوان الصفحة حسب الفلتر
            updatePageTitle();
        }

        // Update page title based on filters
        function updatePageTitle() {
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');
            var titleElement = document.querySelector('.page-header h1');
            var subtitleElement = document.querySelector('.page-header p');
            var breadcrumbActive = document.querySelector('.breadcrumb-item.active');

            if (status === 'pending') {
                // تحديث العنوان
                titleElement.textContent = '⏳ الطلبات المعلقة';
                subtitleElement.textContent = 'إدارة ومتابعة الطلبات المعلقة للمراجعة والاعتماد';
                // تحديث title الصفحة
                document.title = 'الطلبات المعلقة - نظام الحوالات';
                // تحديث breadcrumb
                breadcrumbActive.innerHTML = '<i class="fas fa-clock me-1"></i>الطلبات المعلقة';
            } else if (status === 'approved') {
                // تحديث العنوان
                titleElement.textContent = '⚡ تنفيذ الحوالات';
                subtitleElement.textContent = 'تنفيذ ومتابعة الطلبات المعتمدة الجاهزة للتحويل';
                // تحديث title الصفحة
                document.title = 'تنفيذ الحوالات - نظام الحوالات';
                // تحديث breadcrumb
                breadcrumbActive.innerHTML = '<i class="fas fa-play-circle me-1"></i>تنفيذ الحوالات';
            } else if (status === 'completed') {
                // تحديث العنوان
                titleElement.textContent = '🎯 الطلبات المنفذة';
                subtitleElement.textContent = 'إدارة ومتابعة الطلبات المنفذة والمكتملة';
                // تحديث title الصفحة
                document.title = 'الطلبات المنفذة - نظام الحوالات';
                // تحديث breadcrumb
                breadcrumbActive.innerHTML = '<i class="fas fa-check-circle me-1"></i>الطلبات المنفذة';
            } else {
                // تحديث العنوان
                titleElement.textContent = '🔄 طلبات الحوالات';
                subtitleElement.textContent = 'إدارة ومتابعة جميع طلبات التحويلات المالية';
                // تحديث title الصفحة
                document.title = 'طلبات الحوالات - نظام الحوالات';
                // تحديث breadcrumb
                breadcrumbActive.innerHTML = '<i class="fas fa-list me-1"></i>قائمة طلبات الحوالات';
            }
        }

        // Render table
        function renderTable() {
            var tbody = document.getElementById('transferRequestsTableBody');
            
            if (filteredData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="11" class="text-center py-4"><i class="fas fa-inbox fa-3x text-muted mb-3"></i><div>لا توجد طلبات حوالات</div></td></tr>';
                return;
            }

            var html = '';
            for (var i = 0; i < filteredData.length; i++) {
                var request = filteredData[i];
                html += '<tr>';
                html += '<td><input type="checkbox" class="form-check-input" value="' + request.id + '"></td>';
                html += '<td><strong class="text-primary">' + request.request_number + '</strong></td>';
                html += '<td>' + request.beneficiary_name + '</td>';
                html += '<td><strong>' + formatCurrencyEnglish(request.amount) + '</strong></td>';
                html += '<td><span class="badge bg-secondary">' + request.currency + '</span></td>';
                html += '<td>' + (request.branch_name || 'غير محدد') + '</td>';
                html += '<td>' + (request.money_changer_name || 'غير محدد') + '</td>';
                html += '<td>' + getTransferTypeLabel(request.transfer_type) + '</td>';
                html += '<td><span class="badge bg-warning">' + getStatusLabel(request.status) + '</span></td>';
                html += '<td>' + formatDate(request.created_at) + '</td>';
                html += '<td>';
                html += '<div class="d-flex gap-1">';
                html += '<button class="action-btn btn-view" onclick="viewRequest(' + request.id + ')" title="عرض"><i class="fas fa-eye"></i></button>';
                html += '<button class="action-btn btn-edit" onclick="editRequest(' + request.id + ')" title="تعديل"><i class="fas fa-edit"></i></button>';
                html += '<button class="action-btn btn-print" onclick="printRequest(' + request.id + ')" title="طباعة نموذج الحوالة"><i class="fas fa-print"></i></button>';
                html += '<button class="action-btn btn-docs" onclick="manageDocuments(' + request.id + ')" title="إدارة الوثائق"><i class="fas fa-file-alt"></i></button>';
                if (request.status === 'pending') {
                    html += '<button class="action-btn btn-approve" onclick="approveRequest(' + request.id + ')" title="اعتماد"><i class="fas fa-check"></i></button>';
                }
                if (request.status === 'approved') {
                    html += '<button class="action-btn btn-execute" onclick="executeRequest(' + request.id + ')" title="تنفيذ الطلب"><i class="fas fa-play"></i></button>';
                }
                html += '<button class="action-btn btn-delete" onclick="deleteRequest(' + request.id + ')" title="حذف"><i class="fas fa-trash"></i></button>';
                html += '</div>';
                html += '</td>';
                html += '</tr>';
            }
            
            tbody.innerHTML = html;
        }

        // Helper functions
        function getStatusLabel(status) {
            var labels = {
                'pending': 'معلق',
                'approved': 'معتمد',
                'rejected': 'مرفوض',
                'executed': 'منفذ'
            };
            return labels[status] || status;
        }

        function getTransferTypeLabel(type) {
            var labels = {
                'bank': 'بنكية',
                'cash': 'نقدية',
                'money_changer': 'صراف',
                'online': 'إلكترونية'
            };
            return labels[type] || type;
        }

        function formatCurrencyEnglish(amount) {
            return parseFloat(amount || 0).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        function formatDate(dateString) {
            // تنسيق التاريخ الميلادي بأرقام إنجليزية
            var date = new Date(dateString);
            var day = date.getDate().toString().padStart(2, '0');
            var month = (date.getMonth() + 1).toString().padStart(2, '0');
            var year = date.getFullYear();
            var hours = date.getHours().toString().padStart(2, '0');
            var minutes = date.getMinutes().toString().padStart(2, '0');

            return day + '/' + month + '/' + year + ' ' + hours + ':' + minutes;
        }

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showError(message) {
            alert(message);
        }

        // Action functions
        function addNewRequest() {
            window.location.href = '/transfers/new-request';
        }

        function viewRequest(id) {
            window.open('/transfers/view-request/' + id, '_blank');
        }

        function editRequest(id) {
            window.location.href = '/transfers/edit-request/' + id;
        }

        function manageDocuments(id) {
            window.open('/transfers/requests/' + id + '/documents', '_blank');
        }

        function executeRequest(id) {
            // فتح نافذة تنفيذ الطلب
            window.open('/transfers/requests/' + id + '/execute', '_blank');
        }

        function executeRequest(id) {
            // فتح نافذة عرض الطلب للتنفيذ
            window.open('/transfers/view-request/' + id, '_blank');
        }

        function executeRequest(id) {
            // فتح نافذة تنفيذ الطلبات المخصصة
            window.open('/transfers/execution?request_id=' + id, '_blank');
        }

        function printRequest(requestId) {
            console.log('🖨️ طباعة نموذج الطلب:', requestId);

            // فتح صفحة الطباعة في نافذة جديدة مع إعدادات أفضل
            const printUrl = `/transfers/print-request/${requestId}`;

            // محاولة فتح نافذة جديدة
            const printWindow = window.open(
                printUrl,
                'printWindow',
                'width=1000,height=800,scrollbars=yes,resizable=yes,menubar=no,toolbar=no,location=no,status=no'
            );

            if (printWindow) {
                printWindow.focus();
                console.log('✅ تم فتح نافذة الطباعة بنجاح');
            } else {
                // إذا تم حظر النوافذ المنبثقة، اعرض تنبيه وانتقل مباشرة
                alert('يرجى السماح للنوافذ المنبثقة لهذا الموقع لفتح نموذج الطباعة');
                window.open(printUrl, '_blank');
            }
        }

        function deleteRequest(id) {
            if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
                console.log('Delete request:', id);
            }
        }

        function approveRequest(id) {
            if (confirm('هل أنت متأكد من اعتماد هذا الطلب؟')) {
                // إظهار مؤشر التحميل
                showLoading();

                // إرسال طلب الاعتماد إلى الخادم
                fetch('/transfers/requests/' + id + '/approve', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'approve',
                        request_id: id
                    })
                })
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    hideLoading();

                    if (data.success) {
                        alert('✅ تم اعتماد الطلب بنجاح!');
                        // إعادة تحميل البيانات لتحديث الحالة
                        loadData();
                    } else {
                        alert('❌ فشل في اعتماد الطلب: ' + (data.message || 'خطأ غير معروف'));
                    }
                })
                .catch(function(error) {
                    hideLoading();
                    console.error('خطأ في اعتماد الطلب:', error);
                    alert('❌ حدث خطأ في اعتماد الطلب: ' + error.message);
                });
            }
        }

        function bulkApprove() {
            var selected = getSelectedRequests();
            if (selected.length === 0) {
                alert('يرجى اختيار طلبات للاعتماد');
                return;
            }
            if (confirm('هل أنت متأكد من اعتماد ' + selected.length + ' طلب؟')) {
                console.log('Bulk approve:', selected);
            }
        }

        function bulkReject() {
            var selected = getSelectedRequests();
            if (selected.length === 0) {
                alert('يرجى اختيار طلبات للرفض');
                return;
            }
            if (confirm('هل أنت متأكد من رفض ' + selected.length + ' طلب؟')) {
                console.log('Bulk reject:', selected);
            }
        }

        function getSelectedRequests() {
            var checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            var selected = [];
            for (var i = 0; i < checkboxes.length; i++) {
                selected.push(checkboxes[i].value);
            }
            return selected;
        }

        function exportToExcel() {
            console.log('Export to Excel');
        }


    </script>

    <!-- Voice Search Script -->
    <script src="{{ url_for('static', filename='js/voice-search.js') }}"></script>
    <script>
        // تهيئة البحث الصوتي
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء البحث الصوتي مع الزر الموجود
            const searchInput = document.getElementById('searchInput');
            const voiceButton = document.getElementById('voiceSearchBtn');

            if (searchInput && voiceButton) {
                const voiceSearch = new VoiceSearch({
                    searchInput: searchInput,
                    searchButton: voiceButton,
                    onResult: function(text, confidence) {
                        console.log('🎤 نتيجة البحث الصوتي:', text);
                        searchInput.value = text;
                        filterData(); // تطبيق البحث فوراً
                    },
                    onError: function(error) {
                        console.error('خطأ في البحث الصوتي:', error);
                    }
                });
            }
        });
    </script>
</body>
</html>
