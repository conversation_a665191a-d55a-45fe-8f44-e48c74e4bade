"""
خدمة تحويل HTML العربي إلى PDF
تستخدم weasyprint لتحويل النموذج العربي الجميل إلى PDF
"""

import os
import io
from datetime import datetime
from typing import Dict, Optional
import tempfile

try:
    from xhtml2pdf import pisa
    import arabic_reshaper
    from bidi.algorithm import get_display
    XHTML2PDF_AVAILABLE = True
except ImportError:
    XHTML2PDF_AVAILABLE = False


class ArabicHTMLToPDFService:
    """خدمة تحويل HTML العربي إلى PDF"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        pass
    
    def create_arabic_html_content(self, order_data: Dict) -> str:
        """إنشاء محتوى HTML عربي محسن للـ PDF"""
        
        # تحضير البيانات
        order_number = order_data.get('order_number', 'غير محدد')
        tracking_number = order_data.get('tracking_number', 'غير محدد')
        booking_number = order_data.get('booking_number', 'غير محدد')
        agent_name = order_data.get('agent_name', 'غير محدد')
        agent_phone = order_data.get('agent_phone', 'غير محدد')
        delivery_location = order_data.get('delivery_location', 'غير محدد')
        expected_date = order_data.get('expected_completion_date', 'غير محدد')
        status_arabic = self._get_status_arabic(order_data.get('order_status', 'draft'))
        current_date = datetime.now().strftime('%Y-%m-%d')
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M')
        
        html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمر تسليم - {order_number}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
            background: white;
            padding: 20px;
            font-size: 12px;
        }}
        
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
        }}
        
        .header {{
            text-align: center;
            border-bottom: 3px solid #1f4e79;
            padding-bottom: 15px;
            margin-bottom: 25px;
        }}
        
        .company-name {{
            font-size: 24px;
            font-weight: 700;
            color: #1f4e79;
            margin-bottom: 8px;
        }}
        
        .document-title {{
            font-size: 18px;
            font-weight: 600;
            color: #2c5aa0;
            margin-bottom: 10px;
        }}
        
        .section {{
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            page-break-inside: avoid;
        }}
        
        .section-title {{
            font-size: 14px;
            font-weight: 600;
            color: #1f4e79;
            margin-bottom: 12px;
            padding-bottom: 6px;
            border-bottom: 2px solid #e3f2fd;
        }}
        
        .data-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 8px 0;
        }}
        
        .data-table th,
        .data-table td {{
            padding: 8px 10px;
            text-align: right;
            border: 1px solid #ddd;
            font-size: 11px;
        }}
        
        .data-table th {{
            background-color: #1f4e79;
            color: white;
            font-weight: 600;
        }}
        
        .data-table tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        
        .order-info {{
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }}
        
        .shipment-info {{
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
        }}
        
        .agent-info {{
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
        }}
        
        .delivery-info {{
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
        }}
        
        .status-badge {{
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            background: #007bff;
            color: white;
        }}
        
        .footer {{
            border-top: 2px solid #1f4e79;
            padding-top: 15px;
            margin-top: 25px;
            text-align: center;
            font-size: 10px;
            color: #6c757d;
            page-break-inside: avoid;
        }}
        
        .company-info {{
            margin-bottom: 8px;
            line-height: 1.6;
        }}
        
        .print-date {{
            font-size: 9px;
            color: #adb5bd;
            margin-top: 8px;
        }}
        
        @page {{
            size: A4;
            margin: 2cm;
        }}
        
        @media print {{
            body {{ margin: 0; padding: 10px; }}
            .container {{ padding: 0; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <!-- رأس المستند -->
        <div class="header">
            <div class="company-name">شركة النقل والشحن المتطورة</div>
            <div class="document-title">أمر تسليم للمخلص الجمركي</div>
        </div>
        
        <!-- معلومات الأمر -->
        <div class="section order-info">
            <h3 class="section-title">📋 معلومات الأمر</h3>
            <table class="data-table">
                <tr>
                    <th>البيان</th>
                    <th>القيمة</th>
                </tr>
                <tr>
                    <td>رقم الأمر</td>
                    <td><strong>{order_number}</strong></td>
                </tr>
                <tr>
                    <td>تاريخ الإصدار</td>
                    <td>{current_date}</td>
                </tr>
                <tr>
                    <td>حالة الأمر</td>
                    <td><span class="status-badge">{status_arabic}</span></td>
                </tr>
            </table>
        </div>
        
        <!-- معلومات الشحنة -->
        <div class="section shipment-info">
            <h3 class="section-title">📦 المعلومات الأساسية للشحنة</h3>
            <table class="data-table">
                <tr>
                    <th>البيان</th>
                    <th>القيمة</th>
                </tr>
                <tr>
                    <td>رقم التتبع</td>
                    <td><strong>{tracking_number}</strong></td>
                </tr>
                <tr>
                    <td>رقم الحجز</td>
                    <td><strong>{booking_number}</strong></td>
                </tr>
                <tr>
                    <td>نوع الشحنة</td>
                    <td>{order_data.get('shipment_type', 'غير محدد')}</td>
                </tr>
                <tr>
                    <td>الوزن الإجمالي</td>
                    <td>{order_data.get('total_weight', 'غير محدد')} كيلو</td>
                </tr>
                <tr>
                    <td>عدد الطرود</td>
                    <td>{order_data.get('packages_count', 'غير محدد')}</td>
                </tr>
                <tr>
                    <td>وصف البضاعة</td>
                    <td>{order_data.get('cargo_description', 'غير محدد')}</td>
                </tr>
            </table>
        </div>
        
        <!-- بيانات المخلص -->
        <div class="section agent-info">
            <h3 class="section-title">👤 بيانات المخلص الجمركي</h3>
            <table class="data-table">
                <tr>
                    <th>البيان</th>
                    <th>القيمة</th>
                </tr>
                <tr>
                    <td>اسم المخلص</td>
                    <td><strong>{agent_name}</strong></td>
                </tr>
                <tr>
                    <td>اسم الشركة</td>
                    <td>{order_data.get('company_name', 'غير محدد')}</td>
                </tr>
                <tr>
                    <td>رقم الترخيص</td>
                    <td>{order_data.get('license_number', 'غير محدد')}</td>
                </tr>
                <tr>
                    <td>رقم الهاتف</td>
                    <td><strong>{agent_phone}</strong></td>
                </tr>
                <tr>
                    <td>البريد الإلكتروني</td>
                    <td>{order_data.get('agent_email', 'غير محدد')}</td>
                </tr>
            </table>
        </div>
        
        <!-- تفاصيل التسليم -->
        <div class="section delivery-info">
            <h3 class="section-title">🚚 تفاصيل التسليم</h3>
            <table class="data-table">
                <tr>
                    <th>البيان</th>
                    <th>القيمة</th>
                </tr>
                <tr>
                    <td>موقع التسليم</td>
                    <td><strong>{delivery_location}</strong></td>
                </tr>
                <tr>
                    <td>التاريخ المطلوب للتخليص</td>
                    <td><strong>{expected_date}</strong></td>
                </tr>
            </table>
        </div>
        
        <!-- تذييل المستند -->
        <div class="footer">
            <div class="company-info">
                <strong>شركة النقل والشحن المتطورة</strong><br>
                العنوان: المملكة العربية السعودية - الرياض<br>
                الهاتف: +966 11 123 4567 | البريد الإلكتروني: <EMAIL><br>
                الموقع الإلكتروني: www.shipping.com
            </div>
            <div class="print-date">
                تاريخ الطباعة: {current_time}
            </div>
        </div>
    </div>
</body>
</html>
        """
        
        return html_content
    
    def create_arabic_pdf(self, order_data: Dict) -> bytes:
        """تحويل HTML العربي إلى PDF باستخدام xhtml2pdf"""

        if not XHTML2PDF_AVAILABLE:
            raise Exception("xhtml2pdf غير متاح")

        # إنشاء محتوى HTML
        html_content = self.create_arabic_html_content(order_data)

        # تحويل النصوص العربية
        html_content = self._process_arabic_text(html_content)

        # تحويل إلى PDF
        result = io.BytesIO()
        pdf = pisa.pisaDocument(io.BytesIO(html_content.encode('utf-8')), result)

        if pdf.err:
            raise Exception(f"خطأ في إنشاء PDF: {pdf.err}")

        return result.getvalue()

    def _process_arabic_text(self, html_content: str) -> str:
        """معالجة النصوص العربية للعرض الصحيح"""
        # هذه دالة بسيطة - يمكن تحسينها لاحقاً
        return html_content
    
    def create_arabic_pdf_file(self, order_data: Dict) -> str:
        """إنشاء ملف PDF عربي وحفظه"""
        
        # إنشاء PDF
        pdf_data = self.create_arabic_pdf(order_data)
        
        # حفظ PDF في سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        pdf_filename = f"delivery_order_{order_data.get('order_number', 'unknown')}.pdf"
        pdf_path = os.path.join(desktop_path, pdf_filename)
        
        with open(pdf_path, 'wb') as f:
            f.write(pdf_data)
        
        return pdf_path
    
    def _get_status_arabic(self, status):
        """ترجمة حالة الأمر للعربية"""
        status_map = {
            'draft': 'مسودة',
            'sent': 'مرسل',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(status, status)


# إنشاء instance عام
arabic_html_to_pdf_service = ArabicHTMLToPDFService()


def generate_arabic_pdf_from_html(order_data: Dict) -> str:
    """دالة مساعدة لإنشاء PDF عربي من HTML"""
    return arabic_html_to_pdf_service.create_arabic_pdf_file(order_data)
