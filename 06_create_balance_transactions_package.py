#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء Package ترحيل الأرصدة (BT_PKG)
Create Balance Transactions Package (BT_PKG)
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from oracle_manager import OracleManager

def create_bt_pkg_spec():
    """إنشاء Package Specification لترحيل الأرصدة"""
    
    oracle = OracleManager()
    
    print("📦 إنشاء Package Specification: BT_PKG...")
    print("=" * 70)
    
    package_spec = """
    CREATE OR REPLACE PACKAGE BT_PKG AS
    /*
    ========================================================================
    Package: BT_PKG (Balance Transactions Package)
    الغرض: إدارة ترحيل الأرصدة والعمليات المحاسبية بالتسميات المختصرة
    المطور: النظام المحاسبي الموحد
    التاريخ: 2025-09-08
    ========================================================================
    */
    
    -- ========================================================================
    -- الإجراءات (Procedures)
    -- ========================================================================
    
    -- ترحيل معاملة جديدة
    PROCEDURE POST_TXN(
        p_ent_type IN VARCHAR2,           -- نوع الكيان
        p_ent_id IN NUMBER,               -- معرف الكيان
        p_doc_type IN VARCHAR2,           -- نوع المستند
        p_doc_no IN VARCHAR2,             -- رقم المستند
        p_doc_date IN DATE DEFAULT SYSDATE, -- تاريخ المستند
        p_curr IN VARCHAR2,               -- رمز العملة
        p_dr IN NUMBER DEFAULT 0,         -- مبلغ مدين
        p_cr IN NUMBER DEFAULT 0,         -- مبلغ دائن
        p_rate IN NUMBER DEFAULT 1,       -- سعر الصرف
        p_desc IN VARCHAR2 DEFAULT NULL,  -- الوصف
        p_ref IN VARCHAR2 DEFAULT NULL,   -- المرجع
        p_branch IN NUMBER DEFAULT 1,     -- رقم الفرع
        p_user IN NUMBER DEFAULT 1        -- معرف المستخدم
    );
    
    -- عكس معاملة موجودة
    PROCEDURE REVERSE_TXN(
        p_orig_doc IN VARCHAR2,           -- رقم المستند الأصلي
        p_rev_doc IN VARCHAR2,            -- رقم مستند العكس
        p_reason IN VARCHAR2 DEFAULT NULL, -- سبب العكس
        p_user IN NUMBER DEFAULT 1        -- معرف المستخدم
    );
    
    -- تحديث معاملة موجودة
    PROCEDURE UPDATE_TXN(
        p_doc_no IN VARCHAR2,             -- رقم المستند
        p_dr IN NUMBER DEFAULT NULL,      -- مبلغ مدين جديد
        p_cr IN NUMBER DEFAULT NULL,      -- مبلغ دائن جديد
        p_desc IN VARCHAR2 DEFAULT NULL,  -- وصف جديد
        p_user IN NUMBER DEFAULT 1        -- معرف المستخدم
    );
    
    -- حذف معاملة
    PROCEDURE DELETE_TXN(
        p_doc_no IN VARCHAR2,             -- رقم المستند
        p_user IN NUMBER DEFAULT 1        -- معرف المستخدم
    );
    
    -- ========================================================================
    -- الدوال (Functions)
    -- ========================================================================
    
    -- الحصول على الرصيد الحالي
    FUNCTION GET_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_branch IN NUMBER DEFAULT 1,
        p_date IN DATE DEFAULT SYSDATE
    ) RETURN NUMBER;
    
    -- الحصول على رصيد شهري
    FUNCTION GET_MONTH_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_year IN NUMBER,
        p_month IN NUMBER,
        p_branch IN NUMBER DEFAULT 1
    ) RETURN NUMBER;
    
    -- الحصول على تاريخ الرصيد
    FUNCTION GET_BAL_HIST(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_from_date IN DATE,
        p_to_date IN DATE,
        p_branch IN NUMBER DEFAULT 1
    ) RETURN NUMBER;
    
    -- فحص وجود معاملة
    FUNCTION EXISTS_TXN(
        p_doc_no IN VARCHAR2
    ) RETURN NUMBER;
    
    -- الحصول على عدد المعاملات
    FUNCTION GET_TXN_COUNT(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_branch IN NUMBER DEFAULT 1
    ) RETURN NUMBER;
    
    -- التحقق من صحة المعاملة
    FUNCTION VALIDATE_TXN(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_doc_type IN VARCHAR2,
        p_doc_no IN VARCHAR2,
        p_curr IN VARCHAR2,
        p_dr IN NUMBER,
        p_cr IN NUMBER
    ) RETURN VARCHAR2;
    
    END BT_PKG;
    """
    
    try:
        oracle.execute_update(package_spec)
        print("✅ تم إنشاء Package Specification بنجاح!")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء Package Specification: {str(e)}")
        return False

def create_bt_pkg_body():
    """إنشاء Package Body لترحيل الأرصدة"""
    
    oracle = OracleManager()
    
    print("\n📦 إنشاء Package Body: BT_PKG...")
    print("=" * 70)
    
    package_body = """
    CREATE OR REPLACE PACKAGE BODY BT_PKG AS
    
    -- ========================================================================
    -- المتغيرات الداخلية
    -- ========================================================================
    
    c_status_posted CONSTANT VARCHAR2(10) := 'POSTED';
    c_status_reversed CONSTANT VARCHAR2(10) := 'REVERSED';
    
    -- ========================================================================
    -- الإجراءات الداخلية
    -- ========================================================================
    
    -- تسجيل خطأ
    PROCEDURE LOG_ERROR(p_proc_name VARCHAR2, p_error_msg VARCHAR2) IS
    BEGIN
        DBMS_OUTPUT.PUT_LINE('ERROR in ' || p_proc_name || ': ' || p_error_msg);
    END LOG_ERROR;
    
    -- حساب الرصيد الموحد
    FUNCTION CALC_BAL(p_dr NUMBER, p_cr NUMBER) RETURN NUMBER IS
    BEGIN
        RETURN NVL(p_dr, 0) - NVL(p_cr, 0);
    END CALC_BAL;
    
    -- ========================================================================
    -- تنفيذ الدوال
    -- ========================================================================
    
    FUNCTION VALIDATE_TXN(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_doc_type IN VARCHAR2,
        p_doc_no IN VARCHAR2,
        p_curr IN VARCHAR2,
        p_dr IN NUMBER,
        p_cr IN NUMBER
    ) RETURN VARCHAR2 IS
    BEGIN
        -- فحص نوع الكيان
        IF p_ent_type IS NULL THEN
            RETURN 'Entity type cannot be null';
        END IF;
        
        -- فحص معرف الكيان
        IF p_ent_id IS NULL OR p_ent_id <= 0 THEN
            RETURN 'Entity ID must be positive number';
        END IF;
        
        -- فحص نوع المستند
        IF p_doc_type IS NULL THEN
            RETURN 'Document type cannot be null';
        END IF;
        
        -- فحص رقم المستند
        IF p_doc_no IS NULL THEN
            RETURN 'Document number cannot be null';
        END IF;
        
        -- فحص العملة
        IF p_curr IS NULL THEN
            RETURN 'Currency cannot be null';
        END IF;
        
        -- فحص المبالغ
        IF (NVL(p_dr, 0) = 0 AND NVL(p_cr, 0) = 0) THEN
            RETURN 'Either debit or credit amount must be greater than zero';
        END IF;
        
        IF (NVL(p_dr, 0) > 0 AND NVL(p_cr, 0) > 0) THEN
            RETURN 'Cannot have both debit and credit amounts';
        END IF;
        
        RETURN 'VALID';
    END VALIDATE_TXN;
    
    FUNCTION GET_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_branch IN NUMBER DEFAULT 1,
        p_date IN DATE DEFAULT SYSDATE
    ) RETURN NUMBER IS
        v_balance NUMBER := 0;
    BEGIN
        SELECT NVL(SUM(BAL), 0)
        INTO v_balance
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = p_ent_type
        AND entity_id = p_ent_id
        AND currency_code = p_curr
        AND branch_id = p_branch
        AND document_date <= p_date
        AND status = c_status_posted;
        
        RETURN v_balance;
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN 0;
        WHEN OTHERS THEN
            LOG_ERROR('GET_BAL', SQLERRM);
            RETURN 0;
    END GET_BAL;
    
    FUNCTION GET_MONTH_BAL(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_year IN NUMBER,
        p_month IN NUMBER,
        p_branch IN NUMBER DEFAULT 1
    ) RETURN NUMBER IS
        v_balance NUMBER := 0;
    BEGIN
        SELECT NVL(SUM(BAL), 0)
        INTO v_balance
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = p_ent_type
        AND entity_id = p_ent_id
        AND currency_code = p_curr
        AND branch_id = p_branch
        AND year_no = p_year
        AND month_no = p_month
        AND status = c_status_posted;
        
        RETURN v_balance;
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN 0;
        WHEN OTHERS THEN
            LOG_ERROR('GET_MONTH_BAL', SQLERRM);
            RETURN 0;
    END GET_MONTH_BAL;
    
    FUNCTION GET_BAL_HIST(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_from_date IN DATE,
        p_to_date IN DATE,
        p_branch IN NUMBER DEFAULT 1
    ) RETURN NUMBER IS
        v_balance NUMBER := 0;
    BEGIN
        SELECT NVL(SUM(BAL), 0)
        INTO v_balance
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = p_ent_type
        AND entity_id = p_ent_id
        AND currency_code = p_curr
        AND branch_id = p_branch
        AND document_date BETWEEN p_from_date AND p_to_date
        AND status = c_status_posted;
        
        RETURN v_balance;
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN 0;
        WHEN OTHERS THEN
            LOG_ERROR('GET_BAL_HIST', SQLERRM);
            RETURN 0;
    END GET_BAL_HIST;
    
    FUNCTION EXISTS_TXN(
        p_doc_no IN VARCHAR2
    ) RETURN NUMBER IS
        v_count NUMBER := 0;
    BEGIN
        SELECT COUNT(*)
        INTO v_count
        FROM BALANCE_TRANSACTIONS
        WHERE document_number = p_doc_no;
        
        RETURN CASE WHEN v_count > 0 THEN 1 ELSE 0 END;
        
    EXCEPTION
        WHEN OTHERS THEN
            LOG_ERROR('EXISTS_TXN', SQLERRM);
            RETURN 0;
    END EXISTS_TXN;
    
    FUNCTION GET_TXN_COUNT(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_curr IN VARCHAR2,
        p_branch IN NUMBER DEFAULT 1
    ) RETURN NUMBER IS
        v_count NUMBER := 0;
    BEGIN
        SELECT COUNT(*)
        INTO v_count
        FROM BALANCE_TRANSACTIONS
        WHERE entity_type_code = p_ent_type
        AND entity_id = p_ent_id
        AND currency_code = p_curr
        AND branch_id = p_branch
        AND status = c_status_posted;
        
        RETURN v_count;
        
    EXCEPTION
        WHEN OTHERS THEN
            LOG_ERROR('GET_TXN_COUNT', SQLERRM);
            RETURN 0;
    END GET_TXN_COUNT;
    
    -- ========================================================================
    -- تنفيذ الإجراءات
    -- ========================================================================
    
    PROCEDURE POST_TXN(
        p_ent_type IN VARCHAR2,
        p_ent_id IN NUMBER,
        p_doc_type IN VARCHAR2,
        p_doc_no IN VARCHAR2,
        p_doc_date IN DATE DEFAULT SYSDATE,
        p_curr IN VARCHAR2,
        p_dr IN NUMBER DEFAULT 0,
        p_cr IN NUMBER DEFAULT 0,
        p_rate IN NUMBER DEFAULT 1,
        p_desc IN VARCHAR2 DEFAULT NULL,
        p_ref IN VARCHAR2 DEFAULT NULL,
        p_branch IN NUMBER DEFAULT 1,
        p_user IN NUMBER DEFAULT 1
    ) IS
        v_validation VARCHAR2(100);
        v_bal NUMBER;
        v_bal_f NUMBER;
        v_month NUMBER;
        v_year NUMBER;
    BEGIN
        -- التحقق من صحة البيانات
        v_validation := VALIDATE_TXN(p_ent_type, p_ent_id, p_doc_type, p_doc_no, p_curr, p_dr, p_cr);
        IF v_validation != 'VALID' THEN
            RAISE_APPLICATION_ERROR(-20001, v_validation);
        END IF;
        
        -- فحص وجود المعاملة
        IF EXISTS_TXN(p_doc_no) = 1 THEN
            RAISE_APPLICATION_ERROR(-20002, 'Transaction with this document number already exists');
        END IF;
        
        -- حساب القيم
        v_bal := CALC_BAL(p_dr, p_cr);
        v_bal_f := v_bal * NVL(p_rate, 1);
        v_month := EXTRACT(MONTH FROM p_doc_date);
        v_year := EXTRACT(YEAR FROM p_doc_date);
        
        -- إدراج المعاملة
        INSERT INTO BALANCE_TRANSACTIONS (
            entity_type_code, entity_id, document_type_code, document_number,
            document_date, currency_code, debit_amount, credit_amount,
            exchange_rate, base_currency_debit, base_currency_credit,
            description, reference_number, status, created_date, created_by,
            BAL, BAL_F, MONTH_NO, YEAR_NO, BRANCH_ID
        ) VALUES (
            p_ent_type, p_ent_id, p_doc_type, p_doc_no,
            p_doc_date, p_curr, NVL(p_dr, 0), NVL(p_cr, 0),
            NVL(p_rate, 1), 
            CASE WHEN p_dr > 0 THEN p_dr * NVL(p_rate, 1) ELSE 0 END,
            CASE WHEN p_cr > 0 THEN p_cr * NVL(p_rate, 1) ELSE 0 END,
            p_desc, p_ref, c_status_posted, CURRENT_TIMESTAMP, p_user,
            v_bal, v_bal_f, v_month, v_year, p_branch
        );
        
        COMMIT;
        
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            LOG_ERROR('POST_TXN', SQLERRM);
            RAISE;
    END POST_TXN;
    
    PROCEDURE REVERSE_TXN(
        p_orig_doc IN VARCHAR2,
        p_rev_doc IN VARCHAR2,
        p_reason IN VARCHAR2 DEFAULT NULL,
        p_user IN NUMBER DEFAULT 1
    ) IS
        v_count NUMBER;
        
        CURSOR c_orig_txn IS
            SELECT entity_type_code, entity_id, document_type_code,
                   document_date, currency_code, debit_amount, credit_amount,
                   exchange_rate, description, branch_id
            FROM BALANCE_TRANSACTIONS
            WHERE document_number = p_orig_doc
            AND status = c_status_posted;
    BEGIN
        -- فحص وجود المعاملة الأصلية
        IF EXISTS_TXN(p_orig_doc) = 0 THEN
            RAISE_APPLICATION_ERROR(-20003, 'Original transaction not found');
        END IF;
        
        -- فحص عدم وجود معاملة العكس
        IF EXISTS_TXN(p_rev_doc) = 1 THEN
            RAISE_APPLICATION_ERROR(-20004, 'Reversal transaction already exists');
        END IF;
        
        -- إنشاء معاملات العكس
        FOR rec IN c_orig_txn LOOP
            POST_TXN(
                p_ent_type => rec.entity_type_code,
                p_ent_id => rec.entity_id,
                p_doc_type => rec.document_type_code || '_CANCEL',
                p_doc_no => p_rev_doc,
                p_doc_date => SYSDATE,
                p_curr => rec.currency_code,
                p_dr => rec.credit_amount,  -- عكس المبالغ
                p_cr => rec.debit_amount,   -- عكس المبالغ
                p_rate => rec.exchange_rate,
                p_desc => 'Reversal of ' || p_orig_doc || 
                         CASE WHEN p_reason IS NOT NULL THEN ' - ' || p_reason ELSE '' END,
                p_ref => p_orig_doc,
                p_branch => rec.branch_id,
                p_user => p_user
            );
        END LOOP;
        
        -- تحديث حالة المعاملة الأصلية
        UPDATE BALANCE_TRANSACTIONS
        SET status = c_status_reversed,
            updated_date = CURRENT_TIMESTAMP,
            updated_by = p_user
        WHERE document_number = p_orig_doc;
        
        COMMIT;
        
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            LOG_ERROR('REVERSE_TXN', SQLERRM);
            RAISE;
    END REVERSE_TXN;
    
    PROCEDURE UPDATE_TXN(
        p_doc_no IN VARCHAR2,
        p_dr IN NUMBER DEFAULT NULL,
        p_cr IN NUMBER DEFAULT NULL,
        p_desc IN VARCHAR2 DEFAULT NULL,
        p_user IN NUMBER DEFAULT 1
    ) IS
        v_bal NUMBER;
        v_bal_f NUMBER;
        v_rate NUMBER;
    BEGIN
        -- فحص وجود المعاملة
        IF EXISTS_TXN(p_doc_no) = 0 THEN
            RAISE_APPLICATION_ERROR(-20005, 'Transaction not found');
        END IF;
        
        -- الحصول على سعر الصرف الحالي
        SELECT exchange_rate INTO v_rate
        FROM BALANCE_TRANSACTIONS
        WHERE document_number = p_doc_no AND ROWNUM = 1;
        
        -- حساب الرصيد الجديد إذا تم تحديث المبالغ
        IF p_dr IS NOT NULL OR p_cr IS NOT NULL THEN
            v_bal := CALC_BAL(NVL(p_dr, 0), NVL(p_cr, 0));
            v_bal_f := v_bal * v_rate;
        END IF;
        
        -- تحديث المعاملة
        UPDATE BALANCE_TRANSACTIONS SET
            debit_amount = NVL(p_dr, debit_amount),
            credit_amount = NVL(p_cr, credit_amount),
            BAL = NVL(v_bal, BAL),
            BAL_F = NVL(v_bal_f, BAL_F),
            base_currency_debit = CASE WHEN p_dr IS NOT NULL THEN p_dr * v_rate ELSE base_currency_debit END,
            base_currency_credit = CASE WHEN p_cr IS NOT NULL THEN p_cr * v_rate ELSE base_currency_credit END,
            description = NVL(p_desc, description),
            updated_date = CURRENT_TIMESTAMP,
            updated_by = p_user
        WHERE document_number = p_doc_no;
        
        COMMIT;
        
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            LOG_ERROR('UPDATE_TXN', SQLERRM);
            RAISE;
    END UPDATE_TXN;
    
    PROCEDURE DELETE_TXN(
        p_doc_no IN VARCHAR2,
        p_user IN NUMBER DEFAULT 1
    ) IS
    BEGIN
        -- فحص وجود المعاملة
        IF EXISTS_TXN(p_doc_no) = 0 THEN
            RAISE_APPLICATION_ERROR(-20006, 'Transaction not found');
        END IF;
        
        -- حذف المعاملة
        DELETE FROM BALANCE_TRANSACTIONS
        WHERE document_number = p_doc_no;
        
        COMMIT;
        
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            LOG_ERROR('DELETE_TXN', SQLERRM);
            RAISE;
    END DELETE_TXN;
    
    END BT_PKG;
    """
    
    try:
        oracle.execute_update(package_body)
        print("✅ تم إنشاء Package Body بنجاح!")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء Package Body: {str(e)}")
        return False

def test_bt_pkg():
    """اختبار Package ترحيل الأرصدة"""
    
    oracle = OracleManager()
    
    print("\n🧪 اختبار Package BT_PKG...")
    print("=" * 70)
    
    test_cases = [
        {
            "name": "ترحيل معاملة حوالة للمورد",
            "procedure": "BT_PKG.POST_TXN",
            "params": {
                "p_ent_type": "SUPPLIER",
                "p_ent_id": 15,
                "p_doc_type": "TRANSFER",
                "p_doc_no": "TRF-BT-001",
                "p_curr": "USD",
                "p_dr": 8000,
                "p_cr": 0,
                "p_desc": "Test transfer with BT_PKG",
                "p_branch": 1,
                "p_user": 1
            }
        },
        {
            "name": "ترحيل معاملة خصم من الصراف",
            "procedure": "BT_PKG.POST_TXN", 
            "params": {
                "p_ent_type": "MONEY_CHANGER",
                "p_ent_id": 8,
                "p_doc_type": "TRANSFER",
                "p_doc_no": "TRF-BT-002",
                "p_curr": "USD",
                "p_dr": 0,
                "p_cr": 8000,
                "p_desc": "Test debit with BT_PKG",
                "p_branch": 1,
                "p_user": 1
            }
        },
        {
            "name": "ترحيل معاملة لمندوب مبيعات",
            "procedure": "BT_PKG.POST_TXN",
            "params": {
                "p_ent_type": "SALES_AGENT",
                "p_ent_id": 2,
                "p_doc_type": "COMMISSION",
                "p_doc_no": "COM-BT-001",
                "p_curr": "USD",
                "p_dr": 1200,
                "p_cr": 0,
                "p_desc": "Sales commission with BT_PKG",
                "p_branch": 1,
                "p_user": 1
            }
        }
    ]
    
    successful_tests = 0
    
    for test in test_cases:
        try:
            print(f"\n   🧪 {test['name']}:")
            
            # تنفيذ الاختبار
            call_query = f"""
            BEGIN
                {test['procedure']}(
                    p_ent_type => :p_ent_type,
                    p_ent_id => :p_ent_id,
                    p_doc_type => :p_doc_type,
                    p_doc_no => :p_doc_no,
                    p_curr => :p_curr,
                    p_dr => :p_dr,
                    p_cr => :p_cr,
                    p_desc => :p_desc,
                    p_branch => :p_branch,
                    p_user => :p_user
                );
            END;
            """
            
            oracle.execute_update(call_query, test['params'])
            print(f"      ✅ نجح الاختبار")
            successful_tests += 1
            
            # التحقق من الرصيد
            balance = oracle.execute_query("""
                SELECT BT_PKG.GET_BAL(:p_ent_type, :p_ent_id, :p_curr, :p_branch) FROM DUAL
            """, {
                "p_ent_type": test['params']['p_ent_type'],
                "p_ent_id": test['params']['p_ent_id'],
                "p_curr": test['params']['p_curr'],
                "p_branch": test['params']['p_branch']
            })
            
            if balance:
                print(f"      الرصيد الحالي: {balance[0][0]}")
            
        except Exception as e:
            if "already exists" in str(e).lower():
                print(f"      ⚠️ المعاملة موجودة مسبقاً")
                successful_tests += 1
            else:
                print(f"      ❌ فشل الاختبار: {str(e)}")
    
    print(f"\nملخص الاختبارات:")
    print(f"   نجح: {successful_tests}/{len(test_cases)}")
    
    return successful_tests == len(test_cases)

def verify_bt_package():
    """التحقق من Package BT_PKG"""
    
    oracle = OracleManager()
    
    print("\n✅ التحقق من Package BT_PKG...")
    print("=" * 70)
    
    # فحص وجود Package
    check_query = """
    SELECT object_name, object_type, status
    FROM user_objects
    WHERE object_name = 'BT_PKG'
    ORDER BY object_type
    """
    
    result = oracle.execute_query(check_query)
    if result:
        print("Package Objects:")
        for row in result:
            status_icon = "✅" if row[2] == "VALID" else "❌"
            print(f"   {status_icon} {row[0]} ({row[1]}): {row[2]}")
    
    # فحص الإجراءات والدوال
    procedures_query = """
    SELECT procedure_name, object_type
    FROM user_procedures
    WHERE object_name = 'BT_PKG'
    ORDER BY procedure_name
    """
    
    procedures = oracle.execute_query(procedures_query)
    if procedures:
        print("\nPackage Contents:")
        for row in procedures:
            obj_type = "إجراء" if row[1] == "PROCEDURE" else "دالة"
            print(f"   📦 {row[0]} ({obj_type})")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إنشاء Package ترحيل الأرصدة (BT_PKG)")
    print("=" * 80)
    
    try:
        # 1. إنشاء Package Specification
        if create_bt_pkg_spec():
            
            # 2. إنشاء Package Body
            if create_bt_pkg_body():
                
                # 3. التحقق من الإنشاء
                verify_bt_package()
                
                # 4. اختبار Package
                if test_bt_pkg():
                    
                    print("\n🎉 تم إكمال إنشاء Package BT_PKG بنجاح!")
                    print("✅ المهمة vfjYdVefTrjogB3FUSLh59 مكتملة!")
                    
                    return True
                else:
                    print("\n⚠️ Package تم إنشاؤه لكن بعض الاختبارات فشلت")
                    return True
            else:
                print("\n❌ فشل في إنشاء Package Body")
                return False
        else:
            print("\n❌ فشل في إنشاء Package Specification")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ عام: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "=" * 80)
        print("🏁 انتهى إنشاء Package ترحيل الأرصدة بنجاح!")
    else:
        print("\n" + "=" * 80)
        print("🚫 فشل في إنشاء Package - يرجى مراجعة الأخطاء")
