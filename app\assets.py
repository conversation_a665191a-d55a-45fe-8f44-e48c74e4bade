# -*- coding: utf-8 -*-
"""
إدارة الأصول والمكتبات الخارجية
Assets and External Libraries Management
"""

from flask_assets import Bundle, Environment
from flask import current_app
import os

def init_assets(app):
    """تهيئة نظام إدارة الأصول"""
    assets = Environment(app)
    
    # تكوين مجلد الأصول
    assets.directory = app.static_folder
    assets.url = app.static_url_path
    
    # CSS Bundles - حزم ملفات CSS
    css_netsuite = Bundle(
        'css/netsuite-theme.css',
        'css/netsuite-components.css',
        'css/netsuite-tables.css',
        'css/netsuite-forms.css',
        'css/netsuite-responsive.css',
        filters='cssmin',
        output='dist/netsuite-bundle.css'
    )
    
    css_vendor = Bundle(
        'vendor/css/tailwind.min.css',
        'vendor/css/material-icons.css',
        'vendor/css/heroicons.css',
        filters='cssmin',
        output='dist/vendor-css.css'
    )
    
    # JavaScript Bundles - حزم ملفات JavaScript
    js_netsuite = Bundle(
        'js/netsuite-core.js',
        'js/netsuite-components.js',
        'js/netsuite-tables.js',
        'js/netsuite-forms.js',
        'js/netsuite-charts.js',
        filters='jsmin',
        output='dist/netsuite-bundle.js'
    )
    
    js_vendor = Bundle(
        'vendor/js/alpine.min.js',
        'vendor/js/chart.min.js',
        'vendor/js/apexcharts.min.js',
        'vendor/js/datatables.min.js',
        filters='jsmin',
        output='dist/vendor-js.js'
    )
    
    # تسجيل الحزم
    assets.register('css_netsuite', css_netsuite)
    assets.register('css_vendor', css_vendor)
    assets.register('js_netsuite', js_netsuite)
    assets.register('js_vendor', js_vendor)
    
    return assets

def download_vendor_assets():
    """تحميل المكتبات الخارجية"""
    vendor_assets = {
        # CSS Libraries
        'css': {
            'tailwind.min.css': 'https://cdn.tailwindcss.com/3.3.0/tailwind.min.css',
            'material-icons.css': 'https://fonts.googleapis.com/icon?family=Material+Icons',
            'heroicons.css': 'https://cdn.jsdelivr.net/npm/heroicons@2.0.18/24/outline/index.css'
        },
        # JavaScript Libraries
        'js': {
            'alpine.min.js': 'https://cdn.jsdelivr.net/npm/alpinejs@3.13.0/dist/cdn.min.js',
            'chart.min.js': 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js',
            'apexcharts.min.js': 'https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js',
            'datatables.min.js': 'https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js'
        }
    }
    
    import requests
    import os
    
    # إنشاء مجلدات vendor
    vendor_css_dir = os.path.join(current_app.static_folder, 'vendor', 'css')
    vendor_js_dir = os.path.join(current_app.static_folder, 'vendor', 'js')
    
    os.makedirs(vendor_css_dir, exist_ok=True)
    os.makedirs(vendor_js_dir, exist_ok=True)
    
    # تحميل ملفات CSS
    for filename, url in vendor_assets['css'].items():
        try:
            response = requests.get(url)
            if response.status_code == 200:
                with open(os.path.join(vendor_css_dir, filename), 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"تم تحميل {filename}")
        except Exception as e:
            print(f"خطأ في تحميل {filename}: {e}")
    
    # تحميل ملفات JavaScript
    for filename, url in vendor_assets['js'].items():
        try:
            response = requests.get(url)
            if response.status_code == 200:
                with open(os.path.join(vendor_js_dir, filename), 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"تم تحميل {filename}")
        except Exception as e:
            print(f"خطأ في تحميل {filename}: {e}")

# CDN Links for NetSuite-style libraries
CDN_LINKS = {
    'css': [
        'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
        'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
        'https://cdn.jsdelivr.net/npm/@heroicons/react@2.0.18/24/outline/index.css',
        'https://cdn.jsdelivr.net/npm/material-design-icons@4.0.0/iconfont/material-icons.css'
    ],
    'js': [
        'https://cdn.jsdelivr.net/npm/alpinejs@3.13.0/dist/cdn.min.js',
        'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js',
        'https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js',
        'https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js',
        'https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js',
        'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js',
        'https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/scripts/choices.min.js'
    ]
}
