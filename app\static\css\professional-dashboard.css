/* ===== تصميم لوحة الشحنات الاحترافية ===== */

/* === الإعدادات العامة === */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --white: #ffffff;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* === تصميم الجسم الرئيسي === */
body.professional-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: '<PERSON><PERSON><PERSON>', <PERSON>homa, Geneva, Verdana, sans-serif;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* === الحاوي الرئيسي === */
.professional-dashboard .container-fluid {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    margin: 20px;
    padding: 30px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 40px);
}

/* === الوضع الداكن للحاوي الرئيسي === */
body.dark-mode.professional-dashboard .container-fluid,
html.dark-mode .professional-dashboard .container-fluid,
.dark-mode.professional-dashboard .container-fluid {
    background: rgba(30, 30, 30, 0.95) !important;
    color: #ffffff !important;
}

/* === رأس الصفحة === */
.professional-dashboard .page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 25px 30px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    box-shadow: var(--box-shadow);
}

.professional-dashboard .page-header h1 {
    margin: 0;
    font-size: 2.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 15px;
}

.professional-dashboard .page-header .subtitle {
    margin-top: 8px;
    opacity: 0.9;
    font-size: 1rem;
    font-weight: 300;
}

/* === بطاقات الإحصائيات === */
.professional-dashboard .stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--box-shadow);
    border: none;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.professional-dashboard .stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--accent-color);
}

.professional-dashboard .stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.professional-dashboard .stats-card.border-left-primary::before {
    background: var(--primary-color);
}

.professional-dashboard .stats-card.border-left-success::before {
    background: var(--success-color);
}

.professional-dashboard .stats-card.border-left-info::before {
    background: var(--info-color);
}

.professional-dashboard .stats-card.border-left-warning::before {
    background: var(--warning-color);
}

.professional-dashboard .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 10px 0;
    position: relative;
    z-index: 2;
}

.professional-dashboard .stats-label {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
    margin-bottom: 5px;
    position: relative;
    z-index: 2;
}

.professional-dashboard .stats-card .text-muted {
    position: relative;
    z-index: 2;
}

.professional-dashboard .stats-icon {
    font-size: 3rem;
    color: #e9ecef;
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 0;
    opacity: 0.3;
    pointer-events: none;
}

/* === بطاقات المحتوى === */
.professional-dashboard .content-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    margin-bottom: 25px;
    overflow: hidden;
    transition: var(--transition);
}

.professional-dashboard .content-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.professional-dashboard .content-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    padding: 20px 25px;
    border-radius: 0;
}

.professional-dashboard .content-card .card-header h6 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.professional-dashboard .content-card .card-body {
    padding: 25px;
}

/* === الأزرار المحسنة === */
.professional-dashboard .btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.professional-dashboard .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.professional-dashboard .btn:hover::before {
    left: 100%;
}

.professional-dashboard .btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: 0 4px 15px rgba(52, 73, 94, 0.3);
}

.professional-dashboard .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 73, 94, 0.4);
}

.professional-dashboard .btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.professional-dashboard .btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #3498db 100%);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.professional-dashboard .btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.professional-dashboard .btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* === الجداول المحسنة === */
.professional-dashboard .table-responsive {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.professional-dashboard .table {
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.professional-dashboard .table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
    padding: 15px 12px;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
}

.professional-dashboard .table tbody tr {
    transition: var(--transition);
}

.professional-dashboard .table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
    transform: scale(1.01);
}

.professional-dashboard .table tbody td {
    padding: 15px 12px;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

/* === شارات الحالة === */
.professional-dashboard .status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    position: relative;
    overflow: hidden;
}

.professional-dashboard .status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.professional-dashboard .status-badge:hover::before {
    left: 100%;
}

/* === التصفية القابلة للطي === */
.professional-dashboard .filters-section {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 25px;
    overflow: hidden;
}

.professional-dashboard .filters-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    border-bottom: 2px solid #dee2e6;
    cursor: pointer;
    transition: var(--transition);
}

.professional-dashboard .filters-header:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.professional-dashboard .filters-body {
    padding: 25px;
    background: white;
}

/* === الشريط العائم === */
.professional-dashboard .floating-toolbar {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: white;
    border-radius: 50px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    padding: 15px 25px;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

/* === الرسوم المتحركة === */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.professional-dashboard .animate-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

/* === التجاوب مع الشاشات === */
@media (max-width: 768px) {
    .professional-dashboard .container-fluid {
        margin: 10px;
        padding: 20px;
        border-radius: 15px;
    }
    
    .professional-dashboard .page-header {
        padding: 20px;
        text-align: center;
    }
    
    .professional-dashboard .page-header h1 {
        font-size: 1.8rem;
    }
    
    .professional-dashboard .stats-card {
        margin-bottom: 20px;
    }
    
    .professional-dashboard .floating-toolbar {
        bottom: 20px;
        right: 20px;
        left: 20px;
        border-radius: 15px;
    }
}

/* === إصلاح أزرار الإجراءات === */
.professional-dashboard .btn-group {
    display: flex !important;
    flex-wrap: nowrap !important;
    gap: 3px !important;
    align-items: center !important;
}

.professional-dashboard .btn-group .btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 32px !important;
    height: 32px !important;
    padding: 6px 8px !important;
    font-size: 0.8rem !important;
    border-radius: 6px !important;
    flex-shrink: 0 !important;
    white-space: nowrap !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.professional-dashboard .btn-group .btn i {
    font-size: 0.8rem !important;
    margin: 0 !important;
}

.professional-dashboard .btn-group .btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* تأكيد ظهور عمود الإجراءات */
.professional-dashboard table td:last-child,
.professional-dashboard table th:last-child {
    min-width: 300px !important;
    white-space: nowrap !important;
    padding: 12px 15px !important;
}

.professional-dashboard table td:last-child .btn-group {
    justify-content: flex-start !important;
}

/* === تحسينات إضافية === */
.professional-dashboard .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.professional-dashboard .pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
