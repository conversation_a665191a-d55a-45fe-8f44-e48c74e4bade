"""
Routes لإدارة جهات الاتصال للإشعارات
Notification Contacts Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
import logging
from .contacts_manager import NotificationContactsManager

logger = logging.getLogger(__name__)

# إنشاء Blueprint
contacts_bp = Blueprint('notification_contacts', __name__, url_prefix='/notifications/contacts')

# إنشاء instance من المدير
contacts_manager = NotificationContactsManager()

@contacts_bp.route('/')
@login_required
def index():
    """صفحة إدارة جهات الاتصال الرئيسية"""
    try:
        logger.info("🔍 بدء تحميل صفحة إدارة جهات الاتصال")

        # جلب جميع جهات الاتصال
        logger.info("📋 جلب جميع جهات الاتصال...")
        contacts = contacts_manager.get_all_contacts()
        logger.info(f"✅ تم جلب {len(contacts)} جهة اتصال")

        # جلب الإحصائيات
        logger.info("📊 جلب الإحصائيات...")
        stats = contacts_manager.get_contact_statistics()
        logger.info(f"✅ تم جلب الإحصائيات: {stats}")

        return render_template('notifications/contacts/index.html',
                             contacts=contacts,
                             stats=stats)

    except Exception as e:
        logger.error(f"❌ خطأ في صفحة جهات الاتصال: {e}")
        logger.error(f"❌ تفاصيل الخطأ: {str(e)}")
        import traceback
        logger.error(f"❌ Stack trace: {traceback.format_exc()}")
        flash('حدث خطأ في تحميل جهات الاتصال', 'error')
        return redirect(url_for('main.dashboard'))

@contacts_bp.route('/add')
@login_required
def add_contact_page():
    """صفحة إضافة جهة اتصال جديدة"""
    return render_template('notifications/contacts/add.html')

@contacts_bp.route('/edit')
@login_required
def edit_contact_page():
    """صفحة تعديل جهة اتصال"""
    return render_template('notifications/contacts/edit.html')

@contacts_bp.route('/view')
@login_required
def view_contact_page():
    """صفحة عرض جهة اتصال"""
    return render_template('notifications/contacts/view.html')

@contacts_bp.route('/api/contacts', methods=['GET'])
@login_required
def get_contacts():
    """API لجلب جهات الاتصال"""
    try:
        contact_type = request.args.get('type')
        search_term = request.args.get('search')
        
        if search_term:
            contacts = contacts_manager.search_contacts(search_term)
        else:
            contacts = contacts_manager.get_all_contacts(contact_type=contact_type)
        
        return jsonify({
            'success': True,
            'contacts': contacts
        })
        
    except Exception as e:
        logger.error(f"Error getting contacts: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في جلب جهات الاتصال'
        })

@contacts_bp.route('/api/contacts', methods=['POST'])
@login_required
def create_contact():
    """API لإنشاء جهة اتصال جديدة"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        if not data.get('contact_name'):
            return jsonify({
                'success': False,
                'message': 'اسم جهة الاتصال مطلوب'
            })
        
        # إضافة معرف المستخدم الحالي
        data['created_by'] = current_user.id
        
        # إنشاء جهة الاتصال
        success = contacts_manager.create_contact(data)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'تم إنشاء جهة الاتصال بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في إنشاء جهة الاتصال'
            })
        
    except Exception as e:
        logger.error(f"Error creating contact: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في إنشاء جهة الاتصال'
        })

@contacts_bp.route('/api/contacts/<int:contact_id>', methods=['GET'])
@login_required
def get_contact(contact_id):
    """API لجلب جهة اتصال محددة"""
    try:
        logger.info(f"🔍 محاولة جلب جهة الاتصال رقم: {contact_id}")

        contact = contacts_manager.get_contact_by_id(contact_id)

        logger.info(f"📋 نتيجة البحث: {contact is not None}")

        if contact:
            logger.info(f"✅ تم العثور على جهة الاتصال: {contact.get('contact_name', 'غير محدد')}")
            return jsonify({
                'success': True,
                'contact': contact
            })
        else:
            logger.warning(f"❌ لم يتم العثور على جهة الاتصال رقم: {contact_id}")
            return jsonify({
                'success': False,
                'message': 'جهة الاتصال غير موجودة'
            })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب جهة الاتصال {contact_id}: {e}")
        logger.error(f"❌ تفاصيل الخطأ: {str(e)}")
        import traceback
        logger.error(f"❌ Stack trace: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ في جلب جهة الاتصال: {str(e)}'
        })

@contacts_bp.route('/api/contacts/<int:contact_id>', methods=['PUT'])
@login_required
def update_contact(contact_id):
    """API لتحديث جهة اتصال"""
    try:
        data = request.get_json()
        
        # إضافة معرف المستخدم الحالي
        try:
            data['updated_by'] = current_user.id
        except:
            data['updated_by'] = 1  # معرف افتراضي للاختبار
        
        # تحديث جهة الاتصال
        success = contacts_manager.update_contact(contact_id, data)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'تم تحديث جهة الاتصال بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في تحديث جهة الاتصال'
            })
        
    except Exception as e:
        logger.error(f"Error updating contact {contact_id}: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في تحديث جهة الاتصال'
        })

@contacts_bp.route('/api/contacts/<int:contact_id>', methods=['DELETE'])
@login_required
def delete_contact(contact_id):
    """API لحذف جهة اتصال"""
    try:
        success = contacts_manager.delete_contact(contact_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'تم حذف جهة الاتصال بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في حذف جهة الاتصال'
            })
        
    except Exception as e:
        logger.error(f"Error deleting contact {contact_id}: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في حذف جهة الاتصال'
        })

@contacts_bp.route('/api/contacts/vip', methods=['GET'])
@login_required
def get_vip_contacts():
    """API لجلب جهات الاتصال المميزة"""
    try:
        contacts = contacts_manager.get_vip_contacts()
        
        return jsonify({
            'success': True,
            'contacts': contacts
        })
        
    except Exception as e:
        logger.error(f"Error getting VIP contacts: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في جلب جهات الاتصال المميزة'
        })

@contacts_bp.route('/api/contacts/types/<contact_type>', methods=['GET'])
@login_required
def get_contacts_by_type(contact_type):
    """API لجلب جهات الاتصال حسب النوع"""
    try:
        contacts = contacts_manager.get_contacts_by_type(contact_type)
        
        return jsonify({
            'success': True,
            'contacts': contacts
        })
        
    except Exception as e:
        logger.error(f"Error getting contacts by type {contact_type}: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في جلب جهات الاتصال'
        })

@contacts_bp.route('/api/contacts/search', methods=['POST'])
@login_required
def search_contacts():
    """API للبحث في جهات الاتصال"""
    try:
        data = request.get_json()
        search_term = data.get('search_term', '')
        
        if not search_term:
            return jsonify({
                'success': False,
                'message': 'مصطلح البحث مطلوب'
            })
        
        contacts = contacts_manager.search_contacts(search_term)
        
        return jsonify({
            'success': True,
            'contacts': contacts
        })
        
    except Exception as e:
        logger.error(f"Error searching contacts: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في البحث'
        })

@contacts_bp.route('/api/stats', methods=['GET'])
@login_required
def get_statistics():
    """API لجلب إحصائيات جهات الاتصال"""
    try:
        stats = contacts_manager.get_contact_statistics()
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting contact statistics: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في جلب الإحصائيات'
        })

@contacts_bp.route('/import')
@login_required
def import_contacts():
    """صفحة استيراد جهات الاتصال"""
    return render_template('notifications/contacts/import.html')

@contacts_bp.route('/export')
@login_required
def export_contacts():
    """تصدير جهات الاتصال"""
    try:
        contacts = contacts_manager.get_all_contacts()

        # يمكن إضافة منطق التصدير هنا (CSV, Excel, etc.)

        return jsonify({
            'success': True,
            'message': 'تم تصدير جهات الاتصال بنجاح',
            'contacts': contacts
        })

    except Exception as e:
        logger.error(f"Error exporting contacts: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في تصدير جهات الاتصال'
        })

# ===== API للأتمتة =====

@contacts_bp.route('/api/automation/contacts', methods=['GET'])
@login_required
def get_contacts_for_automation():
    """API لجلب جهات الاتصال للاستخدام في قواعد الأتمتة"""
    try:
        contact_type = request.args.get('type')  # CUSTOMER, DRIVER, AGENT, MANAGER
        is_vip = request.args.get('vip')  # true/false
        min_priority = request.args.get('min_priority', type=int)  # 1-10

        # جلب جهات الاتصال مع التصفية
        contacts = contacts_manager.get_all_contacts(contact_type=contact_type)

        # تطبيق فلاتر إضافية
        filtered_contacts = []
        for contact in contacts:
            # فلتر VIP
            if is_vip and is_vip.lower() == 'true' and not contact.get('is_vip'):
                continue

            # فلتر الأولوية
            if min_priority and contact.get('priority_level', 5) < min_priority:
                continue

            # تبسيط البيانات للأتمتة
            simplified_contact = {
                'id': contact['id'],
                'name': contact['contact_name'],
                'type': contact['contact_type'],
                'phone': contact.get('phone_number'),
                'email': contact.get('email_address'),
                'whatsapp': contact.get('whatsapp_number'),
                'preferred_channels': contact.get('preferred_channels', '').split(','),
                'is_vip': contact.get('is_vip', False),
                'priority': contact.get('priority_level', 5),
                'company': contact.get('company_name'),
                'position': contact.get('position')
            }
            filtered_contacts.append(simplified_contact)

        # ترتيب حسب الأولوية
        filtered_contacts.sort(key=lambda x: x['priority'], reverse=True)

        return jsonify({
            'success': True,
            'contacts': filtered_contacts,
            'total': len(filtered_contacts)
        })

    except Exception as e:
        logger.error(f"Error getting contacts for automation: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في جلب جهات الاتصال للأتمتة'
        })

@contacts_bp.route('/api/automation/contact-groups', methods=['GET'])
@login_required
def get_contact_groups_for_automation():
    """API لجلب مجموعات جهات الاتصال للأتمتة"""
    try:
        # مجموعات افتراضية حسب النوع
        groups = [
            {
                'id': 'all_managers',
                'name': 'جميع المديرين',
                'description': 'جميع جهات الاتصال من نوع مدير',
                'type': 'MANAGER',
                'icon': 'fas fa-user-tie'
            },
            {
                'id': 'all_customers',
                'name': 'جميع العملاء',
                'description': 'جميع جهات الاتصال من نوع عميل',
                'type': 'CUSTOMER',
                'icon': 'fas fa-users'
            },
            {
                'id': 'all_drivers',
                'name': 'جميع السائقين',
                'description': 'جميع جهات الاتصال من نوع سائق',
                'type': 'DRIVER',
                'icon': 'fas fa-truck'
            },
            {
                'id': 'all_agents',
                'name': 'جميع المخلصين',
                'description': 'جميع جهات الاتصال من نوع مخلص جمركي',
                'type': 'AGENT',
                'icon': 'fas fa-clipboard-check'
            },
            {
                'id': 'vip_contacts',
                'name': 'جهات الاتصال المميزة',
                'description': 'جميع جهات الاتصال المميزة (VIP)',
                'type': 'VIP',
                'icon': 'fas fa-star'
            },
            {
                'id': 'high_priority',
                'name': 'الأولوية العالية',
                'description': 'جهات الاتصال ذات الأولوية العالية (8+)',
                'type': 'HIGH_PRIORITY',
                'icon': 'fas fa-exclamation-triangle'
            }
        ]

        return jsonify({
            'success': True,
            'groups': groups
        })

    except Exception as e:
        logger.error(f"Error getting contact groups for automation: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في جلب مجموعات جهات الاتصال'
        })

@contacts_bp.route('/automation/example')
@login_required
def automation_example():
    """صفحة مثال لاستخدام جهات الاتصال في الأتمتة"""
    return render_template('automation/notification_rule_example.html')
