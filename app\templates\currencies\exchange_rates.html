{% extends "base.html" %}

{% block title %}أسعار الصرف{% endblock %}

{% block extra_css %}
<style>
/* تحسينات عامة لصفحة أسعار الصرف */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* محول العملات */
.converter-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.converter-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    pointer-events: none;
}

.converter-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.converter-form .form-label {
    color: rgba(255,255,255,0.9);
    font-weight: 600;
    margin-bottom: 8px;
}

.converter-form .form-control,
.converter-form .form-select {
    background: rgba(255,255,255,0.15);
    border: 2px solid rgba(255,255,255,0.2);
    color: white;
    border-radius: 10px;
    padding: 12px 15px;
    backdrop-filter: blur(10px);
}

.converter-form .form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.converter-form .form-control:focus,
.converter-form .form-select:focus {
    background: rgba(255,255,255,0.25);
    border-color: rgba(255,255,255,0.5);
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
    color: white;
}

.converter-result {
    background: rgba(255,255,255,0.15);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.converter-result .h4 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
}

/* بطاقات أسعار الصرف */
.rates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.rate-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.rate-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.rate-card:hover::before {
    transform: scaleX(1);
}

.rate-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.base-currency-card {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    border: none;
}

.base-currency-card::before {
    background: rgba(255,255,255,0.3);
    transform: scaleX(1);
}

.base-currency-card .currency-pair,
.base-currency-card .exchange-rate,
.base-currency-card .rate-info {
    color: white;
}

.currency-pair {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.currency-icon {
    font-size: 1.1rem;
    color: #667eea;
}

.exchange-rate {
    font-size: 1.6rem;
    color: #27ae60;
    font-weight: 700;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.rate-icon {
    font-size: 1.2rem;
    color: #27ae60;
}

.rate-info {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 15px;
}

.update-form {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 18px;
    margin-top: 15px;
    border: 1px solid #e9ecef;
}

.update-form .form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 8px 12px;
}

.update-form .btn {
    border-radius: 8px;
    font-weight: 600;
}

/* جدول التاريخ */
.history-table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
}

.history-table .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 20px 25px;
}

.history-table .card-header h5 {
    margin: 0;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

.history-table .table {
    margin: 0;
}

.history-table .table th {
    background: #f8f9fa;
    border: none;
    padding: 15px 20px;
    font-weight: 600;
    color: #2c3e50;
}

.history-table .table td {
    padding: 15px 20px;
    border: none;
    border-bottom: 1px solid #f0f0f0;
}

.history-table .table tbody tr:hover {
    background: #f8f9fa;
}

/* أزرار الإجراءات */
.main-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.btn-main {
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-main:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .rates-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        font-size: 2rem;
    }

    .converter-card {
        padding: 20px;
    }

    .main-actions {
        flex-direction: column;
    }

    .btn-main {
        justify-content: center;
    }
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.rate-card {
    animation: fadeIn 0.6s ease-in;
}

.rate-card:nth-child(1) { animation-delay: 0.1s; }
.rate-card:nth-child(2) { animation-delay: 0.2s; }
.rate-card:nth-child(3) { animation-delay: 0.3s; }
.rate-card:nth-child(4) { animation-delay: 0.4s; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي -->
    <div class="page-header fade-in">
        <div class="d-flex justify-content-between align-items-center flex-wrap">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-exchange-alt me-3"></i>أسعار الصرف
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb" style="background: transparent; padding: 0; margin: 0;">
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('currencies.index') }}" style="color: rgba(255,255,255,0.8);">إدارة العملات</a>
                        </li>
                        <li class="breadcrumb-item active" style="color: white;">أسعار الصرف</li>
                    </ol>
                </nav>
            </div>
            <div class="main-actions">
                <button class="btn btn-success btn-main" onclick="showUpdateAllRatesModal()">
                    <i class="fas fa-sync"></i>تحديث جميع الأسعار
                </button>
                <a href="{{ url_for('currencies.index') }}" class="btn btn-secondary btn-main">
                    <i class="fas fa-arrow-right"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- محول العملات -->
        <div class="col-md-4">
            <div class="converter-card fade-in">
                <h5 class="converter-title">
                    <i class="fas fa-calculator"></i>محول العملات
                </h5>
                <div class="converter-form">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-money-bill me-2"></i>المبلغ
                        </label>
                        <input type="number" class="form-control" id="convertAmount"
                               value="1" step="0.01" onchange="convertCurrency()"
                               placeholder="أدخل المبلغ">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-arrow-up me-2"></i>من
                        </label>
                        <select class="form-select" id="fromCurrency" onchange="convertCurrency()">
                            {% for currency in currencies %}
                            <option value="{{ currency.code }}">{{ currency.code }} - {{ currency.name_ar }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-arrow-down me-2"></i>إلى
                        </label>
                        <select class="form-select" id="toCurrency" onchange="convertCurrency()">
                            {% for currency in currencies %}
                            <option value="{{ currency.code }}">{{ currency.code }} - {{ currency.name_ar }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="converter-result" id="converterResult">
                        <div class="text-center">
                            <div class="h4" id="convertedAmount">--</div>
                            <div class="small" style="color: rgba(255,255,255,0.8);">النتيجة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أسعار الصرف -->
        <div class="col-md-8">
            <div class="rates-grid">
                {% for currency in currencies %}
                <div class="rate-card {% if currency.is_base_currency %}base-currency-card{% endif %}">
                    <div class="currency-pair">
                        <i class="fas fa-coins currency-icon"></i>
                        {{ currency.code }}
                        {% if not currency.is_base_currency %}
                            {% set base_currency = currency.__class__.get_base_currency() %}
                            {% if base_currency %}
                                / {{ base_currency.code }}
                            {% endif %}
                        {% endif %}
                    </div>

                    <div class="exchange-rate">
                        <i class="fas fa-chart-line rate-icon"></i>
                        {% if currency.is_base_currency %}
                            1.000000 (عملة أساسية)
                        {% else %}
                            {{ "%.6f"|format(currency.exchange_rate) }}
                        {% endif %}
                    </div>

                    <div class="rate-info">
                        <div style="font-weight: 600; margin-bottom: 5px;">{{ currency.name_ar }}</div>
                        <div class="small">
                            {% if currency.is_base_currency %}
                                <i class="fas fa-star me-1"></i>العملة المرجعية
                            {% else %}
                                <i class="fas fa-clock me-1"></i>آخر تحديث:
                                {% set latest_rate = latest_rates | selectattr('currency_id', 'equalto', currency.id) | first %}
                                {% if latest_rate %}
                                    {{ latest_rate.created_at.strftime('%d/%m/%Y %H:%M') }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>

                    {% if not currency.is_base_currency %}
                    <div class="update-form">
                        <label class="form-label small" style="font-weight: 600; color: #495057;">
                            <i class="fas fa-edit me-1"></i>تحديث السعر
                        </label>
                        <div class="input-group input-group-sm mb-2">
                            <input type="number" class="form-control"
                                   id="rate_{{ currency.id }}"
                                   value="{{ currency.exchange_rate }}"
                                   step="0.000001"
                                   placeholder="سعر الصرف الجديد">
                            <button class="btn btn-primary"
                                    onclick="updateRate({{ currency.id }})"
                                    style="border-radius: 0 8px 8px 0;">
                                <i class="fas fa-save"></i>
                            </button>
                        </div>
                        <input type="text" class="form-control form-control-sm"
                               id="notes_{{ currency.id }}"
                               placeholder="ملاحظات (اختياري)"
                               style="font-size: 0.8rem;">
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- تاريخ أسعار الصرف -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history me-2"></i>تاريخ أسعار الصرف</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>العملة</th>
                                    <th>السعر</th>
                                    <th>التاريخ</th>
                                    <th>المصدر</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rate in latest_rates %}
                                <tr>
                                    <td>
                                        <strong>{{ rate.currency.code }}</strong>
                                        <br>
                                        <small class="text-muted">{{ rate.currency.name_ar }}</small>
                                    </td>
                                    <td>
                                        <span class="h6 text-success">{{ "%.6f"|format(rate.exchange_rate) }}</span>
                                    </td>
                                    <td>{{ rate.created_at.strftime('%d/%m/%Y %H:%M') if rate.created_at else '--' }}</td>
                                    <td>
                                        <span class="badge bg-{% if rate.source == 'manual' %}primary{% elif rate.source == 'api' %}success{% else %}secondary{% endif %}">
                                            {% if rate.source == 'manual' %}يدوي{% elif rate.source == 'api' %}تلقائي{% else %}{{ rate.source }}{% endif %}
                                        </span>
                                    </td>
                                    <td>{{ rate.notes or '--' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تحديث جميع الأسعار -->
<div class="modal fade" id="updateAllRatesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث جميع أسعار الصرف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    هذه الميزة قيد التطوير. يمكنك حالياً تحديث أسعار الصرف يدوياً لكل عملة.
                </div>
                <form id="updateAllRatesForm">
                    <div class="mb-3">
                        <label class="form-label">مصدر الأسعار</label>
                        <select class="form-select" name="source">
                            <option value="manual">تحديث يدوي</option>
                            <option value="api" disabled>API خارجي (قيد التطوير)</option>
                            <option value="bank" disabled>البنك المركزي (قيد التطوير)</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="3" 
                                  placeholder="ملاحظات حول التحديث"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="updateAllRates()">تحديث الأسعار</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateRate(currencyId) {
    const rate = document.getElementById(`rate_${currencyId}`).value;
    const notes = document.getElementById(`notes_${currencyId}`).value;
    
    if (!rate || rate <= 0) {
        showAlert('يرجى إدخال سعر صرف صحيح', 'error');
        return;
    }
    
    const data = {
        currency_id: currencyId,
        exchange_rate: rate,
        source: 'manual',
        notes: notes
    };
    
    fetch('{{ url_for("currencies.update_exchange_rate") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم تحديث سعر الصرف بنجاح', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('حدث خطأ في الاتصال', 'error');
    });
}

function convertCurrency() {
    const amount = document.getElementById('convertAmount').value;
    const fromCurrency = document.getElementById('fromCurrency').value;
    const toCurrency = document.getElementById('toCurrency').value;
    
    if (!amount || amount <= 0) {
        document.getElementById('convertedAmount').textContent = '--';
        return;
    }
    
    if (fromCurrency === toCurrency) {
        document.getElementById('convertedAmount').textContent = amount;
        return;
    }
    
    fetch(`{{ url_for("currencies.api_convert") }}?amount=${amount}&from_currency=${fromCurrency}&to_currency=${toCurrency}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('convertedAmount').textContent = data.formatted_amount;
        } else {
            document.getElementById('convertedAmount').textContent = 'خطأ';
        }
    })
    .catch(error => {
        document.getElementById('convertedAmount').textContent = 'خطأ';
    });
}

function showUpdateAllRatesModal() {
    new bootstrap.Modal(document.getElementById('updateAllRatesModal')).show();
}

function updateAllRates() {
    showAlert('هذه الميزة قيد التطوير', 'info');
}

function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'info' ? 'alert-info' : 'alert-warning';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
}

// تحديث المحول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    convertCurrency();
});
</script>
{% endblock %}
