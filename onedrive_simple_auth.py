#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مصادقة OneDrive المبسطة
Simple OneDrive Authentication
"""

import json
import requests
import webbrowser
from urllib.parse import urlencode

def main():
    """إعداد OneDrive بطريقة مبسطة"""
    print("🚀 إعداد OneDrive للنظام")
    print("=" * 40)
    
    # إعدادات OneDrive
    client_id = "bc80f97a-588b-4a29-bf5b-28f8bf8bdf0f"
    client_secret = "****************************************"
    redirect_uri = "https://localhost:5000/auth/onedrive/callback"  # تغيير للـ localhost
    scopes = ["Files.ReadWrite", "Files.ReadWrite.All"]
    
    # إنشاء رابط المصادقة
    auth_url = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize"
    params = {
        'client_id': client_id,
        'response_type': 'code',
        'redirect_uri': redirect_uri,
        'scope': ' '.join(scopes),
        'response_mode': 'query'
    }
    
    full_url = f"{auth_url}?{urlencode(params)}"
    
    print("🌐 رابط المصادقة:")
    print(full_url)
    print()
    
    # فتح الرابط
    try:
        webbrowser.open(full_url)
        print("✅ تم فتح رابط المصادقة في المتصفح")
    except:
        print("⚠️ انسخ الرابط أعلاه والصقه في المتصفح")
    
    print()
    print("📋 الخطوات:")
    print("1. سجل دخولك إلى حساب Microsoft")
    print("2. وافق على الصلاحيات")
    print("3. ستحصل على خطأ في الصفحة (هذا طبيعي)")
    print("4. انسخ الكود من URL (بعد code=)")
    print()
    
    # الحصول على الكود
    auth_code = input("🔑 الصق الكود هنا: ").strip()
    
    if not auth_code:
        print("❌ لم يتم إدخال كود")
        return
    
    # تبديل الكود بـ Token
    print("🔄 جاري الحصول على Access Token...")
    
    token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
    data = {
        'client_id': client_id,
        'client_secret': client_secret,
        'code': auth_code,
        'redirect_uri': redirect_uri,
        'grant_type': 'authorization_code'
    }
    
    try:
        response = requests.post(token_url, data=data)
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get('access_token')
            
            if access_token:
                print("✅ تم الحصول على Access Token!")
                
                # اختبار Token
                print("🧪 اختبار الاتصال...")
                headers = {'Authorization': f'Bearer {access_token}'}
                test_response = requests.get(
                    'https://graph.microsoft.com/v1.0/me/drive',
                    headers=headers
                )
                
                if test_response.status_code == 200:
                    drive_info = test_response.json()
                    print("🎉 نجح الاتصال بـ OneDrive!")
                    print(f"📁 المالك: {drive_info.get('owner', {}).get('user', {}).get('displayName', 'غير محدد')}")
                    
                    # حفظ Token
                    with open('onedrive_token.json', 'w', encoding='utf-8') as f:
                        json.dump(token_data, f, indent=2)
                    
                    print("💾 تم حفظ Token في onedrive_token.json")
                    print()
                    print("🎯 الخطوة التالية:")
                    print("قم بتشغيل: python update_cloud_config.py")
                    
                else:
                    print(f"❌ فشل اختبار Token: {test_response.status_code}")
                    print(test_response.text)
            else:
                print("❌ لم يتم الحصول على Access Token")
        else:
            print(f"❌ فشل في الحصول على Token: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
