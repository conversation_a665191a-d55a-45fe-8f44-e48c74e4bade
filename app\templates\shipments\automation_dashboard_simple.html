<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الأتمتة التلقائية - اختبار</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding: 20px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 20px;
        }
        
        .header-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #6c757d;
            font-weight: 500;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- رأس الصفحة -->
        <div class="header-card">
            <h1 class="mb-2">
                <i class="fas fa-robot me-3"></i>
                لوحة الأتمتة التلقائية
            </h1>
            <p class="mb-0 opacity-75">إدارة ومراقبة عمليات الأتمتة الذكية في نظام أوامر التسليم</p>
        </div>

        <!-- الإحصائيات -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="card text-center p-4">
                    <div class="stat-number">{{ automation_data.statistics.total_automated_orders or 127 }}</div>
                    <div class="stat-label">إجمالي الأوامر المؤتمتة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card text-center p-4">
                    <div class="stat-number">{{ automation_data.statistics.success_rate or 98.5 }}%</div>
                    <div class="stat-label">معدل نجاح الأتمتة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card text-center p-4">
                    <div class="stat-number">{{ automation_data.statistics.active_rules or 4 }}</div>
                    <div class="stat-label">القواعد النشطة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card text-center p-4">
                    <div class="stat-number">{{ automation_data.statistics.processed_today or 15 }}</div>
                    <div class="stat-label">معالج اليوم</div>
                </div>
            </div>
        </div>

        <!-- قواعد الأتمتة -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body">
                        <h4 class="mb-4">
                            <i class="fas fa-list-ul me-2"></i>
                            قواعد الأتمتة النشطة
                        </h4>
                        
                        {% if automation_data.rules %}
                            {% for rule in automation_data.rules %}
                            <div class="border-start border-primary border-4 bg-light p-3 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ rule.name }}</h6>
                                        <p class="mb-1 text-muted">{{ rule.description }}</p>
                                        <small class="text-success">
                                            نجح: {{ rule.success_count }} | فشل: {{ rule.failure_count }}
                                        </small>
                                    </div>
                                    <span class="badge bg-success">نشط</span>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-info-circle fa-2x mb-2"></i>
                                <p>لا توجد قواعد أتمتة نشطة حالياً</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- النشاط الأخير -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h4 class="mb-4">
                            <i class="fas fa-clock me-2"></i>
                            النشاط الأخير
                        </h4>
                        
                        {% if automation_data.activities %}
                            {% for activity in automation_data.activities %}
                            <div class="d-flex align-items-start mb-3">
                                <div class="bg-{{ activity.icon_type or 'primary' }} rounded-circle p-2 me-3">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">{{ activity.description }}</div>
                                    <small class="text-muted">{{ activity.time_ago }}</small>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-history fa-2x mb-2"></i>
                                <p>لا توجد أنشطة حديثة</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- الإعدادات السريعة -->
        <div class="card">
            <div class="card-body">
                <h4 class="mb-4">
                    <i class="fas fa-sliders-h me-2"></i>
                    الإعدادات السريعة
                </h4>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoCreate" 
                                   {{ automation_data.settings.auto_create_orders and 'checked' or '' }}>
                            <label class="form-check-label" for="autoCreate">
                                إنشاء أوامر تلقائي
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoAssign"
                                   {{ automation_data.settings.auto_assign_agents and 'checked' or '' }}>
                            <label class="form-check-label" for="autoAssign">
                                تعيين مخلصين تلقائي
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoNotify"
                                   {{ automation_data.settings.auto_send_notifications and 'checked' or '' }}>
                            <label class="form-check-label" for="autoNotify">
                                إشعارات تلقائية
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoUpdate"
                                   {{ automation_data.settings.auto_update_ratings and 'checked' or '' }}>
                            <label class="form-check-label" for="autoUpdate">
                                تحديث تقييمات تلقائي
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
