/**
 * Excel-like Table Component
 * مكون جدولي احترافي مشابه لـ Excel
 *
 * المتطلبات:
 * - jQuery 3.6+
 * - Handsontable 12.4+
 * - Bootstrap 5+ (اختياري للتنسيق)
 *
 * الاستخدام:
 * const table = new ExcelTableComponent('#tableContainer', options);
 */

class ExcelTableComponent {
    constructor(container, options = {}) {
        this.container = container;
        this.hot = null;
        this.itemsCache = {};
        
        // الإعدادات الافتراضية
        this.defaultOptions = {
            columns: [
                { key: 'code', title: 'رقم الصنف', type: 'autocomplete', width: 120 },
                { key: 'name', title: 'اسم الصنف', type: 'text', readOnly: true, width: 200 },
                { key: 'unit', title: 'الوحدة', type: 'text', readOnly: true, width: 80 },
                { key: 'quantity', title: 'الكمية', type: 'numeric', width: 100 },
                { key: 'price', title: 'السعر', type: 'numeric', width: 100 },
                { key: 'total', title: 'الإجمالي', type: 'numeric', readOnly: true, width: 120 },
                { key: 'production_date', title: 'تاريخ الإنتاج', type: 'date', width: 120 },
                { key: 'expiry_date', title: 'تاريخ الانتهاء', type: 'date', width: 120 },
                { key: 'notes', title: 'ملاحظات', type: 'text', width: 150 }
            ],
            initialRows: 20,
            height: 500,
            searchAPI: null,
            saveAPI: null,
            autoSave: true,
            autoSaveInterval: 30000,
            rtl: true,
            language: 'ar'
        };
        
        this.options = { ...this.defaultOptions, ...options };
        this.init();
    }
    
    /**
     * تهيئة المكون
     */
    init() {
        this.setupTable();
        this.bindEvents();
        
        if (this.options.autoSave) {
            this.setupAutoSave();
        }
        
        if (this.options.searchAPI) {
            this.loadItemsCache();
        }
    }
    
    /**
     * إعداد الجدول
     */
    setupTable() {
        const colHeaders = this.options.columns.map(col => col.title);
        const columns = this.options.columns.map((col, index) => {
            const columnConfig = {
                type: col.type,
                className: 'htMiddle htCenter'
            };
            
            if (col.readOnly) {
                columnConfig.readOnly = true;
                columnConfig.className += ' readonly-cell';
            }
            
            if (col.type === 'numeric') {
                columnConfig.numericFormat = {
                    pattern: '0,0.000'
                };
                
                if (col.key === 'total') {
                    columnConfig.className += ' calculated-cell';
                }
            }
            
            if (col.type === 'autocomplete') {
                columnConfig.source = (query, process) => {
                    this.searchItems(query, process);
                };
                columnConfig.strict = false;
                columnConfig.allowInvalid = true;
            }
            
            if (col.type === 'date') {
                columnConfig.dateFormat = 'YYYY-MM-DD';
                columnConfig.correctFormat = true;
            }
            
            return columnConfig;
        });
        
        const colWidths = this.options.columns.map(col => col.width);
        
        const tableConfig = {
            data: this.generateEmptyData(this.options.initialRows),
            colHeaders: colHeaders,
            columns: columns,
            rowHeaders: true,
            colWidths: colWidths,
            height: this.options.height,
            licenseKey: 'non-commercial-and-evaluation',
            
            selectionMode: 'multiple',
            fillHandle: {
                direction: 'vertical',
                autoInsertRow: true
            },
            
            contextMenu: {
                items: {
                    'row_above': { name: 'إدراج صف أعلى' },
                    'row_below': { name: 'إدراج صف أسفل' },
                    'remove_row': { name: 'حذف الصف' },
                    'separator1': '---------',
                    'copy': { name: 'نسخ' },
                    'cut': { name: 'قص' },
                    'paste': { name: 'لصق' },
                    'separator2': '---------',
                    'clear_column': { name: 'مسح العمود' }
                }
            },
            
            afterChange: (changes, source) => {
                if (source !== 'loadData' && changes) {
                    this.handleCellChanges(changes);
                }
            },
            
            beforeKeyDown: (event) => {
                this.handleKeyboardShortcuts(event);
            }
        };
        
        this.hot = new Handsontable(document.querySelector(this.container), tableConfig);
    }
    
    /**
     * ربط الأحداث
     */
    bindEvents() {
        // يمكن إضافة أحداث مخصصة هنا
    }
    
    /**
     * إعداد الحفظ التلقائي
     */
    setupAutoSave() {
        setInterval(() => {
            this.autoSave();
        }, this.options.autoSaveInterval);
    }
    
    /**
     * إنشاء بيانات فارغة
     */
    generateEmptyData(rows) {
        const data = [];
        const emptyRow = this.options.columns.map(col => {
            if (col.type === 'numeric') return 0;
            return '';
        });
        
        for (let i = 0; i < rows; i++) {
            data.push([...emptyRow]);
        }
        return data;
    }
    
    /**
     * تحميل كاش الأصناف
     */
    loadItemsCache() {
        if (!this.options.searchAPI) return;
        
        $.ajax({
            url: this.options.searchAPI,
            method: 'GET',
            data: { q: '' },
            success: (items) => {
                this.itemsCache = {};
                items.forEach(item => {
                    this.itemsCache[item.code] = item;
                });
            }
        });
    }
    
    /**
     * البحث في الأصناف
     */
    searchItems(query, process) {
        if (!query || query.length < 1) {
            process([]);
            return;
        }
        
        // البحث في الكاش أولاً
        const localResults = Object.keys(this.itemsCache).filter(code => 
            code.toLowerCase().includes(query.toLowerCase()) ||
            this.itemsCache[code].name.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 10);
        
        if (localResults.length > 0) {
            process(localResults);
            return;
        }
        
        // البحث في الخادم
        if (this.options.searchAPI) {
            $.ajax({
                url: this.options.searchAPI,
                method: 'GET',
                data: { q: query },
                success: (items) => {
                    const codes = items.map(item => {
                        this.itemsCache[item.code] = item;
                        return item.code;
                    });
                    process(codes);
                },
                error: () => {
                    process([]);
                }
            });
        }
    }
    
    /**
     * معالجة تغييرات الخلايا
     */
    handleCellChanges(changes) {
        changes.forEach(([row, prop, oldValue, newValue]) => {
            const column = this.options.columns[prop];
            
            if (column.key === 'code' && newValue && this.itemsCache[newValue]) {
                // تم تحديد صنف - ملء البيانات التلقائية
                const item = this.itemsCache[newValue];
                const updates = [];
                
                this.options.columns.forEach((col, index) => {
                    if (col.key === 'name') updates.push([row, index, item.name]);
                    if (col.key === 'unit') updates.push([row, index, item.unit]);
                    if (col.key === 'price') updates.push([row, index, item.price]);
                });
                
                this.hot.setDataAtCell(updates);
                this.calculateRowTotal(row);
            } else if (column.key === 'quantity' || column.key === 'price') {
                // تم تغيير الكمية أو السعر
                this.calculateRowTotal(row);
            }
        });
        
        this.updateStatistics();
        this.triggerEvent('dataChanged', { changes });
    }
    
    /**
     * حساب إجمالي الصف
     */
    calculateRowTotal(row) {
        const quantityIndex = this.getColumnIndex('quantity');
        const priceIndex = this.getColumnIndex('price');
        const totalIndex = this.getColumnIndex('total');
        
        if (quantityIndex === -1 || priceIndex === -1 || totalIndex === -1) return;
        
        const quantity = this.hot.getDataAtCell(row, quantityIndex) || 0;
        const price = this.hot.getDataAtCell(row, priceIndex) || 0;
        const total = quantity * price;
        
        this.hot.setDataAtCell(row, totalIndex, total);
    }
    
    /**
     * الحصول على فهرس العمود بالمفتاح
     */
    getColumnIndex(key) {
        return this.options.columns.findIndex(col => col.key === key);
    }
    
    /**
     * تحديث الإحصائيات
     */
    updateStatistics() {
        const data = this.hot.getData();
        const codeIndex = this.getColumnIndex('code');
        const nameIndex = this.getColumnIndex('name');
        const quantityIndex = this.getColumnIndex('quantity');
        const totalIndex = this.getColumnIndex('total');
        
        let totalRows = 0;
        let completedRows = 0;
        let totalQuantity = 0;
        let totalAmount = 0;
        
        data.forEach(row => {
            if (row[codeIndex]) {
                totalRows++;
                
                if (row[codeIndex] && row[nameIndex] && row[quantityIndex] > 0) {
                    completedRows++;
                }
                
                totalQuantity += parseFloat(row[quantityIndex]) || 0;
                totalAmount += parseFloat(row[totalIndex]) || 0;
            }
        });
        
        this.triggerEvent('statisticsUpdated', {
            totalRows,
            completedRows,
            totalQuantity,
            totalAmount
        });
    }
    
    /**
     * معالجة اختصارات لوحة المفاتيح
     */
    handleKeyboardShortcuts(event) {
        // Ctrl + S للحفظ
        if (event.ctrlKey && event.keyCode === 83) {
            event.preventDefault();
            this.save();
        }
        
        // Ctrl + Shift + A لإضافة 10 صفوف
        if (event.ctrlKey && event.shiftKey && event.keyCode === 65) {
            event.preventDefault();
            this.addRows(10);
        }
    }
    
    /**
     * إضافة صفوف
     */
    addRows(count) {
        const currentData = this.hot.getData();
        const newRows = this.generateEmptyData(count);
        this.hot.loadData([...currentData, ...newRows]);
        this.triggerEvent('rowsAdded', { count });
    }
    
    /**
     * مسح المحدد
     */
    clearSelected() {
        const selected = this.hot.getSelected();
        if (selected && selected.length > 0) {
            const [row1, col1, row2, col2] = selected[0];
            
            for (let row = row1; row <= row2; row++) {
                for (let col = col1; col <= col2; col++) {
                    this.hot.setDataAtCell(row, col, '');
                }
            }
            
            this.updateStatistics();
            this.triggerEvent('selectionCleared');
        }
    }
    
    /**
     * حذف الصفوف المحددة
     */
    deleteSelectedRows() {
        const selected = this.hot.getSelected();
        if (selected && selected.length > 0) {
            const [row1, , row2] = selected[0];
            const rowsToDelete = Math.abs(row2 - row1) + 1;
            
            this.hot.alter('remove_row', Math.min(row1, row2), rowsToDelete);
            this.updateStatistics();
            this.triggerEvent('rowsDeleted', { count: rowsToDelete });
        }
    }
    
    /**
     * حفظ البيانات
     */
    save() {
        const data = this.getValidData();
        
        if (data.length === 0) {
            this.triggerEvent('saveError', { message: 'لا توجد بيانات صحيحة للحفظ' });
            return;
        }
        
        if (this.options.saveAPI) {
            $.ajax({
                url: this.options.saveAPI,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ items: data }),
                success: (response) => {
                    this.triggerEvent('saveSuccess', response);
                },
                error: (error) => {
                    this.triggerEvent('saveError', error);
                }
            });
        } else {
            this.triggerEvent('saveSuccess', { items: data });
        }
    }
    
    /**
     * الحفظ التلقائي
     */
    autoSave() {
        const data = this.getValidData();
        if (data.length > 0) {
            localStorage.setItem('excel_table_draft', JSON.stringify({
                data: this.hot.getData(),
                timestamp: new Date().toISOString()
            }));
        }
    }
    
    /**
     * الحصول على البيانات الصحيحة
     */
    getValidData() {
        const data = this.hot.getData();
        const codeIndex = this.getColumnIndex('code');
        const nameIndex = this.getColumnIndex('name');
        const quantityIndex = this.getColumnIndex('quantity');
        
        return data.filter(row => 
            row[codeIndex] && row[nameIndex] && row[quantityIndex] > 0
        ).map(row => {
            const item = {};
            this.options.columns.forEach((col, index) => {
                item[col.key] = row[index];
            });
            return item;
        });
    }
    
    /**
     * تحميل البيانات
     */
    loadData(data) {
        const tableData = data.map(item => {
            return this.options.columns.map(col => item[col.key] || '');
        });
        
        this.hot.loadData(tableData);
        this.updateStatistics();
    }
    
    /**
     * إطلاق حدث مخصص
     */
    triggerEvent(eventName, data = {}) {
        const event = new CustomEvent(`excelTable:${eventName}`, {
            detail: { table: this, ...data }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * تدمير المكون
     */
    destroy() {
        if (this.hot) {
            this.hot.destroy();
        }
    }
}

// تصدير المكون للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExcelTableComponent;
} else if (typeof window !== 'undefined') {
    window.ExcelTableComponent = ExcelTableComponent;
}
