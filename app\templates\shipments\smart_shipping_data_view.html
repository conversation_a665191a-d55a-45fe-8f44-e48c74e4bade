{% extends "base.html" %}

{% block title %}البيانات المحفوظة - النظام الذكي لشركات الشحن{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-database me-2"></i>
                        البيانات المحفوظة في النظام الذكي
                    </h2>
                    <p class="text-muted mb-0">عرض جميع الشحنات والحاويات المحفوظة في النظام</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث البيانات
                    </button>
                    <a href="{{ url_for('shipments.smart_shipping_companies_enhanced') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للنظام الذكي
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-primary">
                <div class="info-icon">
                    <i class="fas fa-ship"></i>
                </div>
                <div class="info-details">
                    <h3 id="totalShipments">0</h3>
                    <p>إجمالي الشحنات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-success">
                <div class="info-icon">
                    <i class="fas fa-cube"></i>
                </div>
                <div class="info-details">
                    <h3 id="totalContainers">0</h3>
                    <p>إجمالي الحاويات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-warning">
                <div class="info-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="info-details">
                    <h3 id="detectedContainers">0</h3>
                    <p>حاويات مكتشفة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="info-card bg-info">
                <div class="info-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="info-details">
                    <h3 id="detectionRate">0%</h3>
                    <p>معدل الكشف</p>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الشحنات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        الشحنات المحفوظة
                        <span class="badge bg-secondary ms-2" id="shipmentsCount">0</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الشحنة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>عدد الحاويات</th>
                                    <th>الحاويات المكتشفة</th>
                                    <th>معدل الكشف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="shipmentsTable">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الحاويات المكتشفة مؤخراً -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        الحاويات المكتشفة مؤخراً
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الحاوية</th>
                                    <th>شركة الشحن</th>
                                    <th>رقم الشحنة</th>
                                    <th>دقة الكشف</th>
                                    <th>حالة التتبع</th>
                                    <th>آخر تحديث</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="recentContainersTable">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات شركات الشحن -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        إحصائيات شركات الشحن
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>كود الشركة</th>
                                    <th>اسم الشركة</th>
                                    <th>عدد الحاويات</th>
                                    <th>متوسط دقة الكشف</th>
                                    <th>النسبة المئوية</th>
                                </tr>
                            </thead>
                            <tbody id="companiesStatsTable">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-card {
    background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark, #0056b3));
    color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card.bg-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.info-card.bg-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.info-card.bg-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.info-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.info-details h3 {
    margin: 0;
    font-weight: 700;
    font-size: 2rem;
}

.info-details p {
    margin: 0;
    opacity: 0.9;
}

.confidence-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.confidence-high {
    background: #d4edda;
    color: #155724;
}

.confidence-medium {
    background: #fff3cd;
    color: #856404;
}

.confidence-low {
    background: #f8d7da;
    color: #721c24;
}

.detection-rate {
    font-weight: 600;
}

.detection-rate.high {
    color: #28a745;
}

.detection-rate.medium {
    color: #ffc107;
}

.detection-rate.low {
    color: #dc3545;
}
</style>

<script>
let allData = {};

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل صفحة البيانات المحفوظة...');
    loadAllData();
});

// تحميل جميع البيانات
function loadAllData() {
    Promise.all([
        loadShipmentsList(),
        loadContainersSummary()
    ]).then(() => {
        console.log('✅ تم تحميل جميع البيانات');
        displayAllData();
    }).catch(error => {
        console.error('❌ خطأ في تحميل البيانات:', error);
        showError('حدث خطأ في تحميل البيانات');
    });
}

// تحميل قائمة الشحنات
function loadShipmentsList() {
    return fetch('/shipments/api/shipments-list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allData.shipments = data.shipments;
                console.log(`✅ تم تحميل ${data.shipments.length} شحنة`);
            } else {
                throw new Error(data.message);
            }
        });
}

// تحميل ملخص الحاويات
function loadContainersSummary() {
    return fetch('/shipments/api/containers-summary')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allData.summary = data;
                console.log('✅ تم تحميل ملخص الحاويات');
            } else {
                throw new Error(data.message);
            }
        });
}

// عرض جميع البيانات
function displayAllData() {
    displayStats();
    displayShipments();
    displayRecentContainers();
    displayCompaniesStats();
}

// عرض الإحصائيات
function displayStats() {
    if (!allData.summary) return;
    
    const stats = allData.summary.stats;
    
    document.getElementById('totalShipments').textContent = allData.shipments ? allData.shipments.length : 0;
    document.getElementById('totalContainers').textContent = stats.total_containers;
    document.getElementById('detectedContainers').textContent = stats.detected_containers;
    document.getElementById('detectionRate').textContent = stats.detection_rate + '%';
}

// عرض الشحنات
function displayShipments() {
    const tbody = document.getElementById('shipmentsTable');
    const countElement = document.getElementById('shipmentsCount');
    
    if (!allData.shipments || allData.shipments.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد شحنات محفوظة
                </td>
            </tr>
        `;
        countElement.textContent = '0';
        return;
    }
    
    countElement.textContent = allData.shipments.length;
    
    tbody.innerHTML = allData.shipments.map(shipment => {
        const detectionRate = shipment.container_count > 0 ? 
            Math.round((shipment.detected_containers / shipment.container_count) * 100) : 0;
        
        const rateClass = detectionRate >= 80 ? 'high' : detectionRate >= 50 ? 'medium' : 'low';
        
        return `
            <tr>
                <td>
                    <a href="/shipments/cargo/edit/${shipment.id}" class="text-decoration-none">
                        ${shipment.shipment_number}
                    </a>
                </td>
                <td>
                    <span class="badge bg-primary">${shipment.status}</span>
                </td>
                <td>${shipment.created_at}</td>
                <td>
                    <span class="badge bg-secondary">${shipment.container_count}</span>
                </td>
                <td>
                    <span class="badge bg-success">${shipment.detected_containers}</span>
                </td>
                <td>
                    <span class="detection-rate ${rateClass}">${detectionRate}%</span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <a href="/shipments/cargo/edit/${shipment.id}" class="btn btn-outline-primary btn-sm" title="عرض/تعديل الشحنة">
                            <i class="fas fa-eye"></i>
                        </a>
                        <button class="btn btn-outline-success btn-sm" onclick="detectContainers(${shipment.id})">
                            <i class="fas fa-robot"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// عرض الحاويات المكتشفة مؤخراً
function displayRecentContainers() {
    const tbody = document.getElementById('recentContainersTable');
    
    if (!allData.summary || !allData.summary.recent_containers || allData.summary.recent_containers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد حاويات مكتشفة
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = allData.summary.recent_containers.map(container => {
        const confidenceClass = container.confidence >= 0.9 ? 'confidence-high' : 
                               container.confidence >= 0.7 ? 'confidence-medium' : 'confidence-low';
        
        return `
            <tr>
                <td>
                    <code class="bg-light px-2 py-1 rounded">${container.container_number}</code>
                </td>
                <td>
                    <strong>${container.shipping_company}</strong>
                    <br><small class="text-muted">${container.company_code}</small>
                </td>
                <td>${container.shipment_number}</td>
                <td>
                    <span class="confidence-badge ${confidenceClass}">
                        ${Math.round(container.confidence * 100)}%
                    </span>
                </td>
                <td>
                    <span class="badge bg-info">${container.tracking_status || 'غير محدد'}</span>
                </td>
                <td>
                    <small class="text-muted">${container.last_update}</small>
                </td>
                <td>
                    <button class="btn btn-outline-primary btn-sm" onclick="trackContainer('${container.container_number}')">
                        <i class="fas fa-external-link-alt"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

// عرض إحصائيات شركات الشحن
function displayCompaniesStats() {
    const tbody = document.getElementById('companiesStatsTable');
    
    if (!allData.summary || !allData.summary.companies_stats || allData.summary.companies_stats.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center text-muted py-4">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد إحصائيات شركات الشحن
                </td>
            </tr>
        `;
        return;
    }
    
    const totalContainers = allData.summary.companies_stats.reduce((sum, company) => sum + company.container_count, 0);
    
    tbody.innerHTML = allData.summary.companies_stats.map(company => {
        const percentage = totalContainers > 0 ? Math.round((company.container_count / totalContainers) * 100) : 0;
        
        return `
            <tr>
                <td>
                    <span class="badge bg-primary">${company.code}</span>
                </td>
                <td>${company.name}</td>
                <td>
                    <span class="badge bg-secondary">${company.container_count}</span>
                </td>
                <td>
                    <span class="confidence-badge confidence-high">
                        ${company.avg_confidence}%
                    </span>
                </td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" style="width: ${percentage}%">
                            ${percentage}%
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تحديث البيانات
function refreshData() {
    loadAllData();
}

// كشف الحاويات لشحنة معينة
function detectContainers(shipmentId) {
    // TODO: تنفيذ كشف الحاويات
    alert(`سيتم تنفيذ كشف الحاويات للشحنة ${shipmentId}`);
}

// تتبع حاوية
function trackContainer(containerNumber) {
    // TODO: فتح رابط التتبع
    alert(`سيتم فتح رابط تتبع الحاوية ${containerNumber}`);
}

// عرض خطأ
function showError(message) {
    console.error('❌ خطأ:', message);
    // TODO: عرض رسالة خطأ للمستخدم
}
</script>
{% endblock %}
