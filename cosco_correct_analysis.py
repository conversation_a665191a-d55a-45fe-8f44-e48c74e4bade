#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل صحيح لموقع COSCO لفهم آلية البحث الحقيقية
"""

import requests
import urllib3
import re
import json
import time

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def analyze_cosco_correctly():
    """تحليل صحيح لموقع COSCO"""
    
    print("🔍 تحليل صحيح لموقع COSCO")
    print("=" * 60)
    
    session = requests.Session()
    session.verify = False
    
    # Headers واقعية تماماً مثل المتصفح
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1'
    })
    
    try:
        # الخطوة 1: زيارة الصفحة الرئيسية
        print("📡 زيارة الصفحة الرئيسية...")
        url = "https://elines.coscoshipping.com/ebusiness/cargoTracking"
        
        response = session.get(url, timeout=20)
        print(f"✅ Status: {response.status_code}")
        print(f"🍪 Cookies: {len(session.cookies)}")
        
        if response.status_code == 200:
            # حفظ الصفحة الرئيسية
            with open('cosco_main_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("💾 تم حفظ الصفحة الرئيسية")
            
            # تحليل الصفحة
            analyze_page_structure(response.text)
            
            # الخطوة 2: محاولة البحث بطرق مختلفة
            print("\n" + "=" * 60)
            print("🔍 محاولة البحث بطرق مختلفة...")
            
            # طريقة 1: GET مع parameters
            test_get_search(session, url)
            
            # طريقة 2: تحليل JavaScript للعثور على الطريقة الصحيحة
            analyze_javascript(response.text, session, url)
            
        else:
            print(f"❌ فشل في الوصول: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

def analyze_page_structure(html):
    """تحليل بنية الصفحة"""
    print("\n🔍 تحليل بنية الصفحة...")
    
    # البحث عن div#app (Vue.js/React app)
    if '<div id="app">' in html:
        print("✅ وجد <div id='app'> - هذا Single Page Application")
    
    # البحث عن JavaScript files
    js_files = re.findall(r'src="([^"]*\.js[^"]*)"', html)
    print(f"📜 JavaScript files: {len(js_files)}")
    for js in js_files:
        print(f"  - {js}")
    
    # البحث عن CSS files
    css_files = re.findall(r'href="([^"]*\.css[^"]*)"', html)
    print(f"🎨 CSS files: {len(css_files)}")
    
    # البحث عن أي forms (لن نجد لأنه SPA)
    forms = re.findall(r'<form[^>]*>', html)
    print(f"📝 Forms found: {len(forms)}")
    
    # البحث عن inputs (لن نجد لأنه SPA)
    inputs = re.findall(r'<input[^>]*>', html)
    print(f"📋 Inputs found: {len(inputs)}")

def test_get_search(session, base_url):
    """اختبار البحث بـ GET"""
    print("\n📤 اختبار GET search...")
    
    # parameters مختلفة للاختبار
    test_params = [
        {'bookingNo': '6425375050', 'trackingType': '2'},
        {'blNo': '6425375050'},
        {'containerNo': '6425375050'},
        {'number': '6425375050', 'type': 'booking'},
        {'q': '6425375050'},
        {'search': '6425375050'}
    ]
    
    for params in test_params:
        try:
            print(f"  🔍 اختبار params: {params}")
            response = session.get(base_url, params=params, timeout=15)
            print(f"    Status: {response.status_code}")
            print(f"    URL: {response.url}")
            
            if response.status_code == 200:
                # فحص إذا كان المحتوى مختلف
                if '6425375050' in response.text:
                    print(f"    ✅ وجد الرقم في الاستجابة!")
                    
                    # حفظ النتيجة
                    filename = f"cosco_search_result_{list(params.keys())[0]}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print(f"    💾 تم حفظ النتيجة في {filename}")
                    
                    # تحليل النتيجة
                    analyze_search_result(response.text)
                else:
                    print(f"    ❌ لم يجد الرقم في الاستجابة")
            
        except Exception as e:
            print(f"    ❌ خطأ: {e}")

def analyze_javascript(html, session, base_url):
    """تحليل JavaScript للعثور على الطريقة الصحيحة"""
    print("\n🔧 تحليل JavaScript...")
    
    # استخراج main JavaScript file
    main_js_match = re.search(r'src="([^"]*main[^"]*\.js[^"]*)"', html)
    if main_js_match:
        js_url = main_js_match.group(1)
        if js_url.startswith('/'):
            js_url = f"https://elines.coscoshipping.com{js_url}"
        
        print(f"📜 تحليل main JS: {js_url}")
        
        try:
            js_response = session.get(js_url, timeout=20)
            if js_response.status_code == 200:
                analyze_js_content(js_response.text)
            else:
                print(f"❌ فشل في تحميل JS: {js_response.status_code}")
        except Exception as e:
            print(f"❌ خطأ في تحميل JS: {e}")

def analyze_js_content(js_content):
    """تحليل محتوى JavaScript"""
    print("🔍 تحليل محتوى JavaScript...")
    
    # البحث عن API endpoints
    api_patterns = [
        r'["\']([^"\']*api[^"\']*)["\']',
        r'["\']([^"\']*cargoTracking[^"\']*)["\']',
        r'url\s*:\s*["\']([^"\']+)["\']',
        r'baseURL\s*:\s*["\']([^"\']+)["\']',
        r'endpoint\s*:\s*["\']([^"\']+)["\']'
    ]
    
    found_endpoints = set()
    for pattern in api_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        for match in matches:
            if 'api' in match.lower() or 'tracking' in match.lower() or 'cargo' in match.lower():
                found_endpoints.add(match)
    
    print(f"🔗 API endpoints found: {len(found_endpoints)}")
    for endpoint in sorted(found_endpoints):
        print(f"  - {endpoint}")
    
    # البحث عن HTTP methods
    http_methods = re.findall(r'\.(get|post|put|delete)\s*\(', js_content, re.IGNORECASE)
    if http_methods:
        print(f"📤 HTTP methods: {set(http_methods)}")
    
    # البحث عن data structures
    data_patterns = [
        r'bookingNo\s*[:=]\s*([^,}]+)',
        r'trackingType\s*[:=]\s*([^,}]+)',
        r'containerNo\s*[:=]\s*([^,}]+)'
    ]
    
    for pattern in data_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        if matches:
            print(f"📊 Data pattern '{pattern}': {matches[:3]}")

def analyze_search_result(html):
    """تحليل نتيجة البحث"""
    print("    🔍 تحليل نتيجة البحث...")
    
    # البحث عن البيانات المطلوبة
    keywords = ['B/L', 'POL', 'POD', 'ETD', 'ETA', 'vessel', 'container', 'Shantou', 'Aden']
    found_keywords = []
    
    for keyword in keywords:
        if keyword.lower() in html.lower():
            found_keywords.append(keyword)
    
    if found_keywords:
        print(f"    ✅ وجد كلمات مفتاحية: {found_keywords}")
    else:
        print(f"    ❌ لم يجد كلمات مفتاحية")
    
    # البحث عن تواريخ
    dates = re.findall(r'\d{4}-\d{2}-\d{2}', html)
    if dates:
        print(f"    📅 وجد تواريخ: {dates}")
    
    # البحث عن JSON data
    json_patterns = [
        r'trackingData\s*[=:]\s*({[^;]+})',
        r'cargoData\s*[=:]\s*({[^;]+})'
    ]
    
    for pattern in json_patterns:
        matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
        if matches:
            print(f"    ✅ وجد JSON data!")
            try:
                data = json.loads(matches[0])
                print(f"    📊 JSON content: {str(data)[:200]}...")
            except:
                print(f"    ⚠️ JSON غير صالح")

def main():
    """الاختبار الرئيسي"""
    
    print("=" * 80)
    print("🧪 تحليل صحيح لموقع COSCO")
    print("=" * 80)
    
    analyze_cosco_correctly()
    
    print(f"\n" + "=" * 80)
    print("✅ انتهى التحليل الصحيح")
    print("=" * 80)

if __name__ == "__main__":
    main()
