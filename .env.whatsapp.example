# ===================================================================
# إعدادات WhatsApp Business API
# WhatsApp Business API Configuration
# ===================================================================

# الإعدادات الأساسية - Basic Settings
# ===================================================================

# Access Token من Facebook Developer Console
# يجب الحصول عليه من https://developers.facebook.com/
WHATSAPP_ACCESS_TOKEN=your_access_token_here

# معرف رقم الهاتف من WhatsApp Business API
# Phone Number ID from WhatsApp Business API
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here

# معرف حساب الأعمال (اختياري)
# Business Account ID (optional)
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id_here

# رابط Facebook Graph API
# Facebook Graph API URL
WHATSAPP_API_URL=https://graph.facebook.com/v18.0

# ===================================================================
# إعدادات التشغيل - Operation Settings
# ===================================================================

# وضع الاختبار (true = لا يرسل رسائل حقيقية)
# Test mode (true = doesn't send real messages)
WHATSAPP_TEST_MODE=true

# مهلة الاتصال بالثواني
# Connection timeout in seconds
WHATSAPP_MESSAGE_TIMEOUT=30

# عدد المحاولات عند فشل الإرسال
# Number of retries on send failure
WHATSAPP_MAX_RETRIES=3

# ===================================================================
# إعدادات Webhook (للتطوير المتقدم)
# Webhook Settings (for advanced development)
# ===================================================================

# رمز التحقق من Webhook
# Webhook verification token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token_here

# رابط Webhook لاستقبال التحديثات
# Webhook URL for receiving updates
WHATSAPP_WEBHOOK_URL=https://yourdomain.com/webhooks/whatsapp

# ===================================================================
# إعدادات إضافية - Additional Settings
# ===================================================================

# الرابط الأساسي للنظام (لإنشاء روابط في الرسائل)
# Base URL for the system (for creating links in messages)
BASE_URL=http://localhost:5000

# لغة الرسائل الافتراضية
# Default message language
WHATSAPP_DEFAULT_LANGUAGE=ar

# ===================================================================
# ملاحظات مهمة - Important Notes
# ===================================================================

# 1. للحصول على Access Token:
#    - اذهب إلى https://developers.facebook.com/
#    - أنشئ تطبيق جديد
#    - أضف منتج WhatsApp Business API
#    - احصل على Access Token من قسم API Setup

# 2. للحصول على Phone Number ID:
#    - في لوحة تحكم Facebook Developer
#    - اذهب إلى WhatsApp > API Setup
#    - ستجد Phone Number ID في قسم Phone Numbers

# 3. وضع الاختبار:
#    - عندما يكون WHATSAPP_TEST_MODE=true
#    - لن يتم إرسال رسائل حقيقية
#    - سيتم تسجيل الرسائل في السجل فقط

# 4. الأمان:
#    - لا تشارك Access Token مع أحد
#    - استخدم HTTPS في الإنتاج
#    - قم بتجديد Token بانتظام

# ===================================================================
# مثال للإعدادات الكاملة - Complete Settings Example
# ===================================================================

# WHATSAPP_ACCESS_TOKEN=EAAG1234567890abcdef...
# WHATSAPP_PHONE_NUMBER_ID=***************
# WHATSAPP_BUSINESS_ACCOUNT_ID=***************
# WHATSAPP_API_URL=https://graph.facebook.com/v18.0
# WHATSAPP_TEST_MODE=false
# WHATSAPP_MESSAGE_TIMEOUT=30
# WHATSAPP_MAX_RETRIES=3
# WHATSAPP_WEBHOOK_VERIFY_TOKEN=my_secure_verify_token
# WHATSAPP_WEBHOOK_URL=https://myapp.com/webhooks/whatsapp
# BASE_URL=https://myapp.com
