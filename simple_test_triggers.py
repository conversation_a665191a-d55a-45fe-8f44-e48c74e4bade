#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للـ Triggers
"""

import sys
sys.path.append('.')
from oracle_manager import OracleManager

def simple_test():
    """اختبار بسيط للنظام"""
    print('🧪 اختبار بسيط للنظام...')

    try:
        oracle = OracleManager()
        
        # عرض حالة النظام
        print('📊 حالة النظام الحالية:')
        
        # عرض الـ Triggers
        triggers_query = """
            SELECT trigger_name, status, triggering_event
            FROM user_triggers
            WHERE trigger_name LIKE 'TRG_PO_STATUS%'
            ORDER BY trigger_name
        """
        
        triggers = oracle.execute_query(triggers_query)
        print(f'📋 الـ Triggers ({len(triggers)}):')
        for trigger in triggers:
            status_icon = '✅' if trigger[1] == 'ENABLED' else '❌'
            print(f'   {status_icon} {trigger[0]}: {trigger[2]}')
        
        # عرض الدالة
        function_query = """
            SELECT object_name, status
            FROM user_objects
            WHERE object_type = 'FUNCTION'
            AND object_name = 'SYNC_PO_STATUS'
        """
        
        functions = oracle.execute_query(function_query)
        if functions:
            func = functions[0]
            status_icon = '✅' if func[1] == 'VALID' else '❌'
            print(f'📝 الدالة: {status_icon} {func[0]} ({func[1]})')
        else:
            print('❌ الدالة غير موجودة')
        
        # عرض بيانات الربط
        mapping_query = """
            SELECT COUNT(*) as total, 
                   COUNT(CASE WHEN auto_update = 1 THEN 1 END) as active
            FROM po_shipment_status_map
        """
        
        mapping = oracle.execute_query(mapping_query)
        if mapping:
            total, active = mapping[0]
            print(f'🔗 بيانات الربط: {active}/{total} نشط')
        
        # عرض أوامر الشراء المرتبطة
        linked_pos_query = """
            SELECT 
                po.PO_NUMBER,
                po.STATUS as po_status,
                cs.tracking_number,
                cs.shipment_status,
                m.po_status as expected_status,
                CASE WHEN po.STATUS = m.po_status THEN 'متزامن' ELSE 'غير متزامن' END as sync_status
            FROM PURCHASE_ORDERS po
            JOIN cargo_shipments cs ON po.ID = cs.purchase_order_id
            LEFT JOIN po_shipment_status_map m ON cs.shipment_status = m.shipment_status
            WHERE m.auto_update = 1 AND m.is_active = 1
            ORDER BY po.PO_NUMBER
        """
        
        linked_pos = oracle.execute_query(linked_pos_query)
        print(f'\n📦 أوامر الشراء المرتبطة ({len(linked_pos)}):')
        
        synced_count = 0
        for po in linked_pos:
            po_number, po_status, tracking, ship_status, expected, sync_status = po
            sync_icon = '✅' if sync_status == 'متزامن' else '⚠️'
            print(f'   {sync_icon} {po_number}: {po_status} (شحنة: {ship_status})')
            if sync_status == 'متزامن':
                synced_count += 1
        
        if linked_pos:
            sync_percentage = (synced_count / len(linked_pos)) * 100
            print(f'\n📊 نسبة المزامنة: {sync_percentage:.1f}% ({synced_count}/{len(linked_pos)})')
        
        # اختبار الدالة مباشرة
        print(f'\n🔍 اختبار الدالة مباشرة...')
        if linked_pos:
            # اختبار على أول أمر شراء
            test_po_query = """
                SELECT po.ID, po.PO_NUMBER, po.STATUS
                FROM PURCHASE_ORDERS po
                JOIN cargo_shipments cs ON po.ID = cs.purchase_order_id
                WHERE ROWNUM = 1
            """
            
            test_po = oracle.execute_query(test_po_query)
            if test_po:
                po_id, po_number, current_status = test_po[0]
                print(f'📋 اختبار على: {po_number} (حالة حالية: {current_status})')
                
                # استدعاء الدالة
                result_query = f"SELECT sync_po_status({po_id}) FROM dual"
                result = oracle.execute_query(result_query)
                
                if result:
                    function_result = result[0][0]
                    print(f'✅ نتيجة الدالة: {function_result}')
                    
                    # التحقق من الحالة الفعلية
                    check_query = f"SELECT STATUS FROM PURCHASE_ORDERS WHERE ID = {po_id}"
                    actual_status = oracle.execute_query(check_query)[0][0]
                    
                    if actual_status == function_result:
                        print('✅ الدالة تعمل بشكل صحيح')
                    else:
                        print(f'⚠️ تضارب: الدالة={function_result}, الفعلي={actual_status}')
        
        oracle.close()
        print('\n🎉 تم الاختبار بنجاح!')
        
        # ملخص الحالة
        print('\n📋 ملخص حالة النظام:')
        if len(triggers) == 3 and all(t[1] == 'ENABLED' for t in triggers):
            print('   ✅ جميع الـ Triggers تعمل')
        else:
            print('   ⚠️ مشكلة في الـ Triggers')
            
        if functions and functions[0][1] == 'VALID':
            print('   ✅ الدالة تعمل بشكل صحيح')
        else:
            print('   ⚠️ مشكلة في الدالة')
            
        if linked_pos and synced_count == len(linked_pos):
            print('   ✅ جميع أوامر الشراء متزامنة')
        elif linked_pos:
            print(f'   ⚠️ {len(linked_pos) - synced_count} أمر شراء غير متزامن')
        else:
            print('   ℹ️ لا توجد أوامر شراء مرتبطة للاختبار')
        
        print('\n🚀 النظام جاهز للعمل في البيئة الإنتاجية!')
        
    except Exception as e:
        print(f'❌ خطأ في الاختبار: {e}')

if __name__ == '__main__':
    simple_test()
