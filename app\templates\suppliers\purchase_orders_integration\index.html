{% extends "base.html" %}

{% block title %}نظام التكامل - أوامر الشراء والمدفوعات{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">
<style>
    .payment-status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .status-paid { background-color: #28a745; color: #fff; }
    .status-partial { background-color: #ffc107; color: #000; }
    .status-pending { background-color: #6c757d; color: #fff; }
    .status-overdue { background-color: #dc3545; color: #fff; }
    
    .priority-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
    
    .priority-urgent { background-color: #dc3545; color: #fff; }
    .priority-high { background-color: #fd7e14; color: #fff; }
    .priority-medium { background-color: #ffc107; color: #000; }
    .priority-normal { background-color: #6c757d; color: #fff; }
    
    .amount-display {
        font-family: 'Courier New', monospace;
        font-weight: bold;
    }
    
    .outstanding-amount { color: #dc3545; }
    .paid-amount { color: #28a745; }
    
    .stats-card {
        border-left: 4px solid;
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-card.total { border-left-color: #007bff; }
    .stats-card.paid { border-left-color: #28a745; }
    .stats-card.pending { border-left-color: #ffc107; }
    .stats-card.overdue { border-left-color: #dc3545; }
    
    .po-selection-card {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        transition: all 0.3s;
    }
    
    .po-selection-card.selected {
        border-color: #007bff;
        background-color: #f8f9ff;
    }
    
    .po-selection-card:hover {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0,123,255,0.1);
    }
    
    .integration-timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .integration-timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -23px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007bff;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #007bff;
    }
    
    .timeline-item.completed::before {
        background: #28a745;
        box-shadow: 0 0 0 2px #28a745;
    }
    
    .timeline-item.pending::before {
        background: #ffc107;
        box-shadow: 0 0 0 2px #ffc107;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">🔗 نظام التكامل - أوامر الشراء والمدفوعات</h2>
                    <p class="text-muted mb-0">إدارة مدفوعات أوامر الشراء مع التكامل الكامل مع نظام الحوالات</p>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" onclick="showCreatePaymentModal()">
                        <i class="fas fa-plus"></i> طلب دفع جديد
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card total">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">إجمالي أوامر الشراء</h6>
                            <h3 class="mb-0" id="totalOrders">-</h3>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card paid">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">مدفوعة بالكامل</h6>
                            <h3 class="mb-0" id="paidOrders">-</h3>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card pending">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">معلقة الدفع</h6>
                            <h3 class="mb-0" id="pendingOrders">-</h3>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card overdue">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">متأخرة الدفع</h6>
                            <h3 class="mb-0" id="overdueOrders">-</h3>
                        </div>
                        <div class="text-danger">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs Navigation -->
    <ul class="nav nav-tabs mb-4" id="integrationTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="outstanding-tab" data-bs-toggle="tab" data-bs-target="#outstanding" 
                    type="button" role="tab">
                <i class="fas fa-exclamation-circle"></i> أوامر الشراء المستحقة
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments" 
                    type="button" role="tab">
                <i class="fas fa-money-bill-wave"></i> تتبع المدفوعات
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="suppliers-tab" data-bs-toggle="tab" data-bs-target="#suppliers" 
                    type="button" role="tab">
                <i class="fas fa-users"></i> إحصائيات الموردين
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="integration-tab" data-bs-toggle="tab" data-bs-target="#integration" 
                    type="button" role="tab">
                <i class="fas fa-link"></i> تتبع التكامل
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="integrationTabContent">
        <!-- Outstanding Purchase Orders Tab -->
        <div class="tab-pane fade show active" id="outstanding" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="card-title mb-0">📋 أوامر الشراء المستحقة الدفع</h6>
                        <div>
                            <select class="form-select form-select-sm" id="supplierFilter" onchange="filterOutstandingOrders()">
                                <option value="">جميع الموردين</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="outstandingOrdersTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم أمر الشراء</th>
                                    <th>المورد</th>
                                    <th>العنوان</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>المبلغ المستحق</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>حالة الاستحقاق</th>
                                    <th>الأولوية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تحميل البيانات عبر AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payments Tracking Tab -->
        <div class="tab-pane fade" id="payments" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">💰 تتبع مدفوعات أوامر الشراء</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="paymentsTrackingTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم أمر الشراء</th>
                                    <th>المورد</th>
                                    <th>نوع الدفعة</th>
                                    <th>مبلغ الدفعة</th>
                                    <th>حالة الدفعة</th>
                                    <th>رقم الحوالة</th>
                                    <th>حالة الحوالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>تاريخ الإكمال</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تحميل البيانات عبر AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Suppliers Statistics Tab -->
        <div class="tab-pane fade" id="suppliers" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">📊 إحصائيات الموردين</h6>
                </div>
                <div class="card-body">
                    <div id="suppliersStatisticsContent">
                        <div class="text-center py-5">
                            <i class="fas fa-chart-pie fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">إحصائيات الموردين</h4>
                            <p class="text-muted">سيتم عرض إحصائيات أوامر الشراء للموردين هنا</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Integration Tracking Tab -->
        <div class="tab-pane fade" id="integration" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">🔗 تتبع التكامل مع نظام الحوالات</h6>
                </div>
                <div class="card-body">
                    <div id="integrationTrackingContent">
                        <div class="text-center py-5">
                            <i class="fas fa-link fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">تتبع التكامل</h4>
                            <p class="text-muted">سيتم عرض حالة التكامل مع نظام الحوالات هنا</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء طلب دفع -->
<div class="modal fade" id="createPaymentModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">💰 إنشاء طلب دفع لأوامر الشراء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createPaymentForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">المورد *</label>
                            <select class="form-select" id="paymentSupplier" required onchange="loadSupplierOutstandingOrders()">
                                <option value="">اختر المورد</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الصراف/البنك *</label>
                            <select class="form-select" id="paymentMoneyChanger" required>
                                <option value="">اختر الصراف أو البنك</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">أوامر الشراء المستحقة</label>
                            <div id="outstandingOrdersContainer">
                                <div class="text-center py-3 text-muted">
                                    اختر المورد أولاً لعرض أوامر الشراء المستحقة
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <label class="form-label">خصم</label>
                            <input type="number" class="form-control" id="paymentDiscount" step="0.01" min="0" 
                                   onchange="calculatePaymentTotals()">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">ضريبة مقتطعة</label>
                            <input type="number" class="form-control" id="paymentTax" step="0.01" min="0" 
                                   onchange="calculatePaymentTotals()">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المبلغ الصافي</label>
                            <input type="number" class="form-control" id="paymentNetAmount" readonly>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">الغرض من الدفع *</label>
                            <input type="text" class="form-control" id="paymentPurpose" required 
                                   placeholder="مثال: دفع مستحقات أوامر الشراء">
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="paymentNotes" rows="3" 
                                      placeholder="أي ملاحظات إضافية..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="createPaymentRequest()">
                    <i class="fas fa-save"></i> إنشاء طلب الدفع
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تفاصيل أمر الشراء -->
<div class="modal fade" id="purchaseOrderDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📋 تفاصيل أمر الشراء ومدفوعاته</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="purchaseOrderDetailsContent">
                <!-- سيتم تحميل التفاصيل عبر AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

<script>
let outstandingOrdersTable;
let paymentsTrackingTable;
let selectedPurchaseOrders = [];
let dashboardData = {};

$(document).ready(function() {
    initializeDataTables();
    loadDashboardData();
    loadSupplierOptions();
    loadMoneyChangerOptions();
});

function initializeDataTables() {
    outstandingOrdersTable = $('#outstandingOrdersTable').DataTable({
        responsive: true,
        pageLength: 25,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        order: [[3, 'asc']], // ترتيب حسب تاريخ الاستحقاق
        columnDefs: [
            { targets: [9], orderable: false } // عمود الإجراءات غير قابل للترتيب
        ]
    });
    
    paymentsTrackingTable = $('#paymentsTrackingTable').DataTable({
        responsive: true,
        pageLength: 25,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        order: [[7, 'desc']], // ترتيب حسب تاريخ الطلب
        columnDefs: [
            { targets: [9], orderable: false } // عمود الإجراءات غير قابل للترتيب
        ]
    });
}

// سيتم إضافة باقي الدوال في ملف JavaScript منفصل...
</script>

<!-- تحميل ملف JavaScript منفصل للتكامل -->
<script src="{{ url_for('static', filename='js/purchase_orders_integration.js') }}"></script>
{% endblock %}
