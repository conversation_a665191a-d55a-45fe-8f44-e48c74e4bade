#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مساعد تحسين رسائل الخطأ للمستخدمين
User-Friendly Error Message Helper
"""

from flask import current_app
from flask_login import current_user
import logging

logger = logging.getLogger(__name__)

def get_user_friendly_error_message(error, context="general"):
    """
    تحويل رسائل الخطأ التقنية إلى رسائل واضحة ومفيدة للمستخدم
    
    Args:
        error: الخطأ الأصلي (Exception أو string)
        context: السياق (database, email, file, network, etc.)
    
    Returns:
        dict: {'message': رسالة للمستخدم, 'technical_details': تفاصيل تقنية}
    """
    
    error_message = str(error)
    user_friendly_message = "حدث خطأ غير متوقع"
    
    # رسائل خطأ قاعدة البيانات Oracle
    if "ORA-" in error_message:
        user_friendly_message = _handle_oracle_errors(error_message)
    
    # رسائل خطأ الشبكة والاتصال
    elif any(keyword in error_message.lower() for keyword in ["connection", "network", "timeout", "unreachable"]):
        user_friendly_message = _handle_network_errors(error_message)
    
    # رسائل خطأ البريد الإلكتروني
    elif context == "email":
        user_friendly_message = _handle_email_errors(error_message)
    
    # رسائل خطأ الملفات
    elif context == "file":
        user_friendly_message = _handle_file_errors(error_message)
    
    # رسائل خطأ المصادقة
    elif any(keyword in error_message.lower() for keyword in ["authentication", "unauthorized", "forbidden", "login"]):
        user_friendly_message = _handle_auth_errors(error_message)
    
    # رسائل خطأ عامة أخرى
    else:
        user_friendly_message = _handle_general_errors(error_message)
    
    # تحديد ما إذا كان يجب عرض التفاصيل التقنية
    show_technical_details = False
    try:
        if current_user.is_authenticated and hasattr(current_user, 'is_admin') and current_user.is_admin:
            show_technical_details = True
    except:
        pass
    
    return {
        'message': user_friendly_message,
        'technical_details': error_message if show_technical_details else None
    }

def _handle_oracle_errors(error_message):
    """معالجة أخطاء Oracle Database"""
    
    if "ORA-02292" in error_message:
        return "لا يمكن حذف هذا العنصر لأنه مرتبط ببيانات أخرى في النظام. يرجى حذف البيانات المرتبطة أولاً أو الاتصال بالدعم الفني."
    
    elif "ORA-02291" in error_message:
        return "لا يمكن إضافة هذا العنصر بسبب بيانات مرجعية مفقودة. يرجى التحقق من البيانات المطلوبة."
    
    elif "ORA-00001" in error_message:
        return "هذا العنصر موجود مسبقاً في النظام. يرجى استخدام قيم مختلفة أو تحديث العنصر الموجود."
    
    elif "ORA-01400" in error_message:
        return "يوجد حقل مطلوب فارغ. يرجى ملء جميع الحقول المطلوبة."
    
    elif "ORA-00942" in error_message:
        return "جدول البيانات غير موجود أو غير متاح. يرجى الاتصال بالدعم الفني."
    
    elif "ORA-01017" in error_message:
        return "خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة مرة أخرى."
    
    elif "ORA-12154" in error_message:
        return "مشكلة في إعدادات قاعدة البيانات. يرجى الاتصال بالدعم الفني."
    
    elif "ORA-01843" in error_message:
        return "تنسيق التاريخ غير صحيح. يرجى استخدام التنسيق الصحيح للتاريخ."
    
    elif "ORA-01722" in error_message:
        return "قيمة رقمية غير صحيحة. يرجى التحقق من الأرقام المدخلة."
    
    else:
        return "حدث خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني."

def _handle_network_errors(error_message):
    """معالجة أخطاء الشبكة والاتصال"""
    
    if "timeout" in error_message.lower():
        return "انتهت مهلة الاتصال. يرجى التحقق من الإنترنت والمحاولة مرة أخرى."
    
    elif "connection refused" in error_message.lower():
        return "تم رفض الاتصال. الخدمة قد تكون غير متاحة مؤقتاً."
    
    elif "unreachable" in error_message.lower():
        return "لا يمكن الوصول للخادم. يرجى التحقق من الإنترنت."
    
    else:
        return "مشكلة في الاتصال بالشبكة. يرجى المحاولة مرة أخرى."

def _handle_email_errors(error_message):
    """معالجة أخطاء البريد الإلكتروني"""
    
    if "authentication" in error_message.lower():
        return "خطأ في المصادقة. يرجى التحقق من اسم المستخدم وكلمة المرور."
    
    elif "quota" in error_message.lower() or "storage" in error_message.lower():
        return "مساحة التخزين ممتلئة. يرجى حذف بعض الرسائل والمحاولة مرة أخرى."
    
    elif "size" in error_message.lower() or "large" in error_message.lower():
        return "حجم الرسالة أو المرفقات كبير جداً. يرجى تقليل حجم المرفقات."
    
    elif "smtp" in error_message.lower():
        return "مشكلة في خادم الإرسال. يرجى المحاولة مرة أخرى."
    
    else:
        return "حدث خطأ في البريد الإلكتروني. يرجى المحاولة مرة أخرى."

def _handle_file_errors(error_message):
    """معالجة أخطاء الملفات"""
    
    if "permission" in error_message.lower() or "access" in error_message.lower():
        return "ليس لديك صلاحية للوصول لهذا الملف."
    
    elif "not found" in error_message.lower():
        return "الملف غير موجود أو تم حذفه."
    
    elif "disk" in error_message.lower() or "space" in error_message.lower():
        return "مساحة التخزين ممتلئة. يرجى حذف بعض الملفات."
    
    elif "format" in error_message.lower() or "corrupt" in error_message.lower():
        return "الملف تالف أو بتنسيق غير مدعوم."
    
    else:
        return "حدث خطأ في التعامل مع الملف."

def _handle_auth_errors(error_message):
    """معالجة أخطاء المصادقة والصلاحيات"""
    
    if "unauthorized" in error_message.lower():
        return "ليس لديك صلاحية للوصول لهذه الصفحة."
    
    elif "forbidden" in error_message.lower():
        return "تم منع الوصول. يرجى الاتصال بالمدير."
    
    elif "expired" in error_message.lower():
        return "انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى."
    
    else:
        return "خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى."

def _handle_general_errors(error_message):
    """معالجة الأخطاء العامة"""
    
    if "memory" in error_message.lower():
        return "نفدت ذاكرة النظام. يرجى المحاولة مرة أخرى."
    
    elif "syntax" in error_message.lower():
        return "خطأ في تنسيق البيانات المدخلة."
    
    elif "validation" in error_message.lower():
        return "البيانات المدخلة غير صحيحة. يرجى المراجعة والمحاولة مرة أخرى."
    
    else:
        return "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني."

def log_error_with_context(error, context, user_id=None, additional_info=None):
    """
    تسجيل الخطأ مع معلومات السياق
    
    Args:
        error: الخطأ
        context: السياق
        user_id: معرف المستخدم
        additional_info: معلومات إضافية
    """
    
    try:
        error_info = {
            'error': str(error),
            'context': context,
            'user_id': user_id or (current_user.id if current_user.is_authenticated else None),
            'additional_info': additional_info
        }
        
        logger.error(f"❌ خطأ في {context}: {error_info}")
        
    except Exception as log_error:
        logger.error(f"❌ خطأ في تسجيل الخطأ: {log_error}")
