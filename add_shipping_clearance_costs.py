#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة حقلي أجور الشحن وأجور التخليص إلى جدول أوامر الشراء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import OracleManager
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_shipping_clearance_columns():
    """إضافة عمودي أجور الشحن وأجور التخليص إلى جدول PURCHASE_ORDERS"""
    
    oracle_manager = None
    try:
        # الاتصال بقاعدة البيانات
        oracle_manager = OracleManager()
        oracle_manager.connect()
        
        logger.info("🔗 تم الاتصال بقاعدة البيانات بنجاح")
        
        # فحص وجود العمودين أولاً
        check_columns_query = """
        SELECT COLUMN_NAME 
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = 'PURCHASE_ORDERS' 
        AND COLUMN_NAME IN ('SHIPPING_COST', 'CLEARANCE_COST')
        ORDER BY COLUMN_NAME
        """
        
        result = oracle_manager.execute_query(check_columns_query)
        existing_columns = [row[0] for row in result] if result else []
        
        logger.info(f"📋 الأعمدة الموجودة: {existing_columns}")
        
        # إضافة عمود أجور الشحن إذا لم يكن موجوداً
        if 'SHIPPING_COST' not in existing_columns:
            logger.info("📝 إضافة عمود SHIPPING_COST...")
            
            add_shipping_query = """
            ALTER TABLE PURCHASE_ORDERS 
            ADD SHIPPING_COST NUMBER(15,2) DEFAULT 0
            """
            
            oracle_manager.execute_update(add_shipping_query)
            logger.info("✅ تم إضافة عمود SHIPPING_COST بنجاح")
            
            # إضافة تعليق على العمود
            shipping_comment_query = """
            COMMENT ON COLUMN PURCHASE_ORDERS.SHIPPING_COST 
            IS 'أجور الشحن - تكلفة شحن البضائع من المورد'
            """
            
            oracle_manager.execute_update(shipping_comment_query)
            logger.info("📝 تم إضافة تعليق على عمود SHIPPING_COST")
        else:
            logger.info("✅ عمود SHIPPING_COST موجود بالفعل")
        
        # إضافة عمود أجور التخليص إذا لم يكن موجوداً
        if 'CLEARANCE_COST' not in existing_columns:
            logger.info("📝 إضافة عمود CLEARANCE_COST...")
            
            add_clearance_query = """
            ALTER TABLE PURCHASE_ORDERS 
            ADD CLEARANCE_COST NUMBER(15,2) DEFAULT 0
            """
            
            oracle_manager.execute_update(add_clearance_query)
            logger.info("✅ تم إضافة عمود CLEARANCE_COST بنجاح")
            
            # إضافة تعليق على العمود
            clearance_comment_query = """
            COMMENT ON COLUMN PURCHASE_ORDERS.CLEARANCE_COST 
            IS 'أجور التخليص - رسوم التخليص الجمركي والإجراءات'
            """
            
            oracle_manager.execute_update(clearance_comment_query)
            logger.info("📝 تم إضافة تعليق على عمود CLEARANCE_COST")
        else:
            logger.info("✅ عمود CLEARANCE_COST موجود بالفعل")
        
        # التحقق من إضافة الأعمدة
        verify_query = """
        SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE, 
               NULLABLE, DATA_DEFAULT
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = 'PURCHASE_ORDERS' 
        AND COLUMN_NAME IN ('SHIPPING_COST', 'CLEARANCE_COST')
        ORDER BY COLUMN_NAME
        """
        
        verify_result = oracle_manager.execute_query(verify_query)
        
        if verify_result:
            logger.info("✅ تم التحقق من الأعمدة الجديدة:")
            for column_info in verify_result:
                logger.info(f"   📋 {column_info[0]}:")
                logger.info(f"      نوع البيانات: {column_info[1]}")
                logger.info(f"      الدقة: {column_info[3]}")
                logger.info(f"      المقياس: {column_info[4]}")
                logger.info(f"      يقبل NULL: {column_info[5]}")
                logger.info(f"      القيمة الافتراضية: {column_info[6] or 'لا توجد'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إضافة أعمدة أجور الشحن والتخليص: {e}")
        return False
        
    finally:
        if oracle_manager:
            oracle_manager.disconnect()
            logger.info("🔌 تم قطع الاتصال بقاعدة البيانات")

def test_shipping_clearance_functionality():
    """اختبار وظائف أجور الشحن والتخليص"""
    
    oracle_manager = None
    try:
        oracle_manager = OracleManager()
        oracle_manager.connect()
        
        logger.info("🧪 اختبار وظائف أجور الشحن والتخليص...")
        
        # اختبار إدراج قيم تجريبية
        test_po_number = "TEST-SHIPPING-CLEARANCE-001"
        test_shipping_cost = 150.75
        test_clearance_cost = 85.50
        
        # حذف أي بيانات تجريبية سابقة
        delete_test_query = "DELETE FROM PURCHASE_ORDERS WHERE PO_NUMBER = :1"
        oracle_manager.execute_update(delete_test_query, [test_po_number])
        
        # إدراج أمر شراء تجريبي مع أجور الشحن والتخليص
        insert_test_query = """
        INSERT INTO PURCHASE_ORDERS (
            PO_NUMBER, SUPPLIER_NAME, TITLE, PO_DATE, 
            SHIPPING_COST, CLEARANCE_COST, STATUS, CREATED_BY
        ) VALUES (
            :1, :2, :3, SYSDATE, :4, :5, :6, :7
        )
        """
        
        test_params = [
            test_po_number,
            "مورد تجريبي",
            "أمر شراء تجريبي لاختبار أجور الشحن والتخليص",
            test_shipping_cost,
            test_clearance_cost,
            "مسودة",
            "SYSTEM"
        ]
        
        oracle_manager.execute_update(insert_test_query, test_params)
        
        logger.info("✅ تم إدراج أمر شراء تجريبي مع أجور الشحن والتخليص")
        
        # استعلام للتحقق من البيانات
        select_test_query = """
        SELECT PO_NUMBER, SUPPLIER_NAME, SHIPPING_COST, CLEARANCE_COST,
               (SHIPPING_COST + CLEARANCE_COST) AS TOTAL_ADDITIONAL_COSTS
        FROM PURCHASE_ORDERS 
        WHERE PO_NUMBER = :1
        """
        
        result = oracle_manager.execute_query(select_test_query, [test_po_number])
        
        if result:
            po_data = result[0]
            logger.info(f"📋 بيانات أمر الشراء التجريبي:")
            logger.info(f"   رقم أمر الشراء: {po_data[0]}")
            logger.info(f"   اسم المورد: {po_data[1]}")
            logger.info(f"   أجور الشحن: {po_data[2]} ريال")
            logger.info(f"   أجور التخليص: {po_data[3]} ريال")
            logger.info(f"   إجمالي التكاليف الإضافية: {po_data[4]} ريال")
        
        # تحديث أجور الشحن والتخليص
        update_test_query = """
        UPDATE PURCHASE_ORDERS 
        SET SHIPPING_COST = :1, CLEARANCE_COST = :2 
        WHERE PO_NUMBER = :3
        """
        
        new_shipping_cost = 200.00
        new_clearance_cost = 100.00
        oracle_manager.execute_update(update_test_query, [new_shipping_cost, new_clearance_cost, test_po_number])
        
        logger.info("✅ تم تحديث أجور الشحن والتخليص")
        
        # التحقق من التحديث
        updated_result = oracle_manager.execute_query(select_test_query, [test_po_number])
        
        if updated_result:
            updated_data = updated_result[0]
            logger.info(f"📋 بيانات أمر الشراء بعد التحديث:")
            logger.info(f"   أجور الشحن الجديدة: {updated_data[2]} ريال")
            logger.info(f"   أجور التخليص الجديدة: {updated_data[3]} ريال")
            logger.info(f"   إجمالي التكاليف الإضافية الجديد: {updated_data[4]} ريال")
        
        # اختبار تعيين قيم صفر
        oracle_manager.execute_update(update_test_query, [0, 0, test_po_number])
        
        zero_result = oracle_manager.execute_query(select_test_query, [test_po_number])
        
        if zero_result and zero_result[0][2] == 0 and zero_result[0][3] == 0:
            logger.info("✅ تم تعيين أجور الشحن والتخليص إلى صفر بنجاح")
        else:
            logger.error("❌ فشل في تعيين أجور الشحن والتخليص إلى صفر")
            return False
        
        # حذف البيانات التجريبية
        oracle_manager.execute_update(delete_test_query, [test_po_number])
        
        logger.info("🗑️ تم حذف البيانات التجريبية")
        logger.info("✅ اختبار وظائف أجور الشحن والتخليص مكتمل بنجاح")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار وظائف أجور الشحن والتخليص: {e}")
        return False
        
    finally:
        if oracle_manager:
            oracle_manager.disconnect()

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n" + "="*70)
    print("📋 تعليمات استخدام حقلي أجور الشحن وأجور التخليص")
    print("="*70)
    
    print("\n🎯 الغرض من الحقلين:")
    print("• أجور الشحن: تسجيل تكلفة شحن البضائع من المورد")
    print("• أجور التخليص: تسجيل رسوم التخليص الجمركي والإجراءات")
    print("• حساب التكلفة الإجمالية لأمر الشراء بدقة")
    print("• تحسين إدارة التكاليف والميزانية")
    
    print("\n📝 كيفية الاستخدام:")
    print("1. في نافذة إنشاء أمر شراء جديد:")
    print("   • ابحث عن الصف السادس: 'أجور الشحن + أجور التخليص'")
    print("   • أدخل قيمة أجور الشحن (اختياري)")
    print("   • أدخل قيمة أجور التخليص (اختياري)")
    print("   • سيتم حساب الإجمالي تلقائياً")
    
    print("\n2. في نافذة تحديث أمر الشراء:")
    print("   • يمكن تحديث القيم في أي وقت")
    print("   • الحقلان اختياريان ولا يؤثران على حفظ أمر الشراء")
    
    print("\n3. في صفحة عرض تفاصيل أمر الشراء:")
    print("   • قسم 'التكاليف الإضافية': يعرض أجور الشحن والتخليص")
    print("   • قسم 'ملخص التكاليف': يعرض الإجمالي الشامل")
    
    print("\n✅ المزايا:")
    print("• حساب دقيق للتكلفة الإجمالية")
    print("• تتبع أفضل للتكاليف الإضافية")
    print("• تحسين التخطيط المالي")
    print("• شفافية في عرض التكاليف")
    print("• دعم العملات المختلفة")

if __name__ == "__main__":
    print("🚀 بدء إضافة حقلي أجور الشحن وأجور التخليص إلى نظام أوامر الشراء")
    print("="*80)
    
    # إضافة الأعمدة
    success = add_shipping_clearance_columns()
    
    if success:
        print("\n✅ تم إضافة الأعمدة بنجاح!")
        
        # اختبار الوظائف
        test_success = test_shipping_clearance_functionality()
        
        if test_success:
            print("\n🎉 تم إنجاز جميع العمليات بنجاح!")
            
            # عرض تعليمات الاستخدام
            show_usage_instructions()
            
            print("\n🎯 الخطوات التالية:")
            print("1. أعد تشغيل الخادم")
            print("2. اذهب إلى صفحة إنشاء أمر شراء جديد")
            print("3. ستجد حقلي 'أجور الشحن' و 'أجور التخليص' الجديدين")
            print("4. جرب إنشاء أمر شراء مع قيم مختلفة")
            print("5. تحقق من عرض التكاليف في صفحة التفاصيل")
            
        else:
            print("\n❌ فشل في اختبار الوظائف")
    else:
        print("\n❌ فشل في إضافة الأعمدة")
        print("💡 تحقق من اتصال قاعدة البيانات والأذونات")
