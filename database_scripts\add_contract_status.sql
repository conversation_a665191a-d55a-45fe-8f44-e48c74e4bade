-- إضا<PERSON>ة عمود حالة العقد
-- تاريخ الإنشاء: 2025-08-03
-- الوصف: إضا<PERSON>ة عمود CONTRACT_STATUS لتتبع حالة العقد (مسودة، معتمد، منفذ جزئياً، منفذ كلياً)

-- <PERSON><PERSON><PERSON><PERSON><PERSON> العمود الجديد
ALTER TABLE CONTRACTS ADD (
    CONTRACT_STATUS VARCHAR2(20) DEFAULT 'DRAFT' NOT NULL
);

-- إض<PERSON><PERSON><PERSON> قيد التحقق للقيم المسموحة
ALTER TABLE CONTRACTS ADD CONSTRAINT CHK_CONTRACT_STATUS 
CHECK (CONTRACT_STATUS IN ('DRAFT', 'APPROVED', 'PARTIALLY_EXECUTED', 'FULLY_EXECUTED'));

-- إضافة فهرس للأداء
CREATE INDEX IDX_CONTRACTS_STATUS ON CONTRACTS(CONTRACT_STATUS);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> تعليق للعمود
COMMENT ON COLUMN CONTRACTS.CONTRACT_STATUS IS 'حالة العقد: DRAFT=مسودة, APPROVED=معتمد, PARTIALLY_EXECUTED=منفذ جزئياً, FULLY_EXECUTED=منفذ كلياً';

-- تحديث العقود الموجودة بناءً على حالة الاستخدام
-- العقود المستخدمة تصبح معتمدة، غير المستخدمة تبقى مسودة
UPDATE CONTRACTS 
SET CONTRACT_STATUS = CASE 
    WHEN IS_USED = 1 THEN 'APPROVED'
    ELSE 'DRAFT'
END;

-- عرض النتائج
SELECT 
    CONTRACT_ID,
    CONTRACT_NUMBER,
    CONTRACT_STATUS,
    IS_USED,
    CASE CONTRACT_STATUS
        WHEN 'DRAFT' THEN 'مسودة'
        WHEN 'APPROVED' THEN 'معتمد'
        WHEN 'PARTIALLY_EXECUTED' THEN 'منفذ جزئياً'
        WHEN 'FULLY_EXECUTED' THEN 'منفذ كلياً'
    END AS STATUS_ARABIC
FROM CONTRACTS
ORDER BY CONTRACT_ID;

COMMIT;
