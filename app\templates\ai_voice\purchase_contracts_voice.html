{% extends "base.html" %}

{% block title %}التحكم الصوتي - عقود الشراء{% endblock %}

{% block extra_css %}
<style>
    .voice-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 30px;
        color: white;
        margin-bottom: 30px;
    }
    
    .voice-button {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: none;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        font-size: 24px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        margin: 20px;
    }
    
    .voice-button:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.4);
    }
    
    .voice-button.listening {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .voice-status {
        background: rgba(255,255,255,0.1);
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        backdrop-filter: blur(10px);
    }
    
    .contract-form {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .voice-suggestions {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 15px;
        margin-top: 20px;
    }
    
    .suggestion-item {
        background: rgba(255,255,255,0.2);
        border-radius: 8px;
        padding: 10px;
        margin: 5px 0;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .suggestion-item:hover {
        background: rgba(255,255,255,0.3);
        transform: translateX(5px);
    }
    
    .speech-feedback {
        background: rgba(255,255,255,0.9);
        color: #333;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        border-left: 4px solid #667eea;
    }
    
    .error-message {
        background: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
        border: 1px solid rgba(231, 76, 60, 0.3);
        border-radius: 8px;
        padding: 12px;
        margin: 10px 0;
    }
    
    .success-message {
        background: rgba(46, 204, 113, 0.1);
        color: #2ecc71;
        border: 1px solid rgba(46, 204, 113, 0.3);
        border-radius: 8px;
        padding: 12px;
        margin: 10px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">🎙️ التحكم الصوتي الذكي - عقود الشراء</h1>
        </div>
    </div>
    
    <div class="row">
        <!-- لوحة التحكم الصوتي -->
        <div class="col-md-4">
            <div class="voice-container">
                <h3 class="text-center mb-4">🤖 المساعد الصوتي</h3>
                
                <div class="text-center">
                    <button id="voiceButton" class="voice-button" onclick="toggleVoiceListening()">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <p class="mt-3">اضغط للبدء في التحكم الصوتي</p>

                    <button class="btn btn-outline-light btn-sm mt-2" onclick="testVoiceSystem()">
                        <i class="fas fa-vial"></i> اختبار النظام
                    </button>
                </div>
                
                <div class="voice-status" id="voiceStatus">
                    <h6><i class="fas fa-info-circle"></i> الحالة:</h6>
                    <p id="statusText">جاهز للاستماع</p>
                </div>
                
                <div class="speech-feedback" id="speechFeedback" style="display: none;">
                    <h6><i class="fas fa-comment"></i> ما سمعته:</h6>
                    <p id="speechText"></p>
                </div>
                
                <div class="voice-suggestions">
                    <h6><i class="fas fa-lightbulb"></i> أمثلة على الأوامر:</h6>
                    <div id="suggestionsList">
                        <div class="suggestion-item" onclick="speakExample('رقم العقد 12345')">
                            "رقم العقد 12345"
                        </div>
                        <div class="suggestion-item" onclick="speakExample('اسم المورد شركة الأمل')">
                            "اسم المورد شركة الأمل"
                        </div>
                        <div class="suggestion-item" onclick="speakExample('تاريخ العقد اليوم')">
                            "تاريخ العقد اليوم"
                        </div>
                        <div class="suggestion-item" onclick="speakExample('المبلغ 50000 ريال')">
                            "المبلغ 50000 ريال"
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- نموذج عقد الشراء -->
        <div class="col-md-8">
            <div class="contract-form">
                <h3 class="mb-4">📋 بيانات عقد الشراء</h3>
                
                <form id="contractForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="contractNumber">رقم العقد</label>
                                <input type="text" class="form-control" id="contractNumber" name="contract_number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="contractDate">تاريخ العقد</label>
                                <input type="date" class="form-control" id="contractDate" name="contract_date">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="supplierName">اسم المورد</label>
                                <input type="text" class="form-control" id="supplierName" name="supplier_name">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="currency">العملة</label>
                                <select class="form-control" id="currency" name="currency">
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                    <option value="EUR">يورو</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="totalAmount">المبلغ الإجمالي</label>
                                <input type="number" class="form-control" id="totalAmount" name="total_amount" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="deliveryDate">تاريخ التسليم</label>
                                <input type="date" class="form-control" id="deliveryDate" name="delivery_date">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="paymentTerms">شروط الدفع</label>
                        <textarea class="form-control" id="paymentTerms" name="payment_terms" rows="2"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">وصف العقد</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                    
                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-success btn-lg me-3" onclick="saveContract()">
                            <i class="fas fa-save"></i> حفظ العقد
                        </button>
                        <button type="button" class="btn btn-secondary btn-lg" onclick="clearForm()">
                            <i class="fas fa-eraser"></i> مسح النموذج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- رسائل التنبيه -->
<div id="alertContainer" style="position: fixed; top: 20px; right: 20px; z-index: 1000;"></div>
{% endblock %}

{% block extra_js %}
<script>
let isListening = false;
let voiceSession = null;

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkVoiceStatus();

    // إضافة معلومات تشخيصية
    console.log('🎙️ تم تحميل صفحة التحكم الصوتي');
    console.log('🔍 فحص دعم المتصفح للميكروفون...');

    // فحص دعم المتصفح
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        console.log('✅ المتصفح يدعم الوصول للميكروفون');
    } else {
        console.log('❌ المتصفح لا يدعم الوصول للميكروفون');
        showAlert('المتصفح لا يدعم الوصول للميكروفون', 'warning');
    }
});

// فحص حالة نظام التحكم الصوتي
async function checkVoiceStatus() {
    try {
        console.log('🔍 فحص حالة نظام التحكم الصوتي...');
        const response = await fetch('/ai-voice/api/voice-status');
        const data = await response.json();

        console.log('📊 حالة النظام:', data);

        if (data.available) {
            console.log('✅ نظام التحكم الصوتي متاح');
            updateStatus('النظام جاهز - اضغط للبدء');
            showAlert('نظام التحكم الصوتي جاهز للاستخدام!', 'success');
        } else {
            console.log('❌ نظام التحكم الصوتي غير متاح');
            showAlert('تحذير: نظام التحكم الصوتي غير متاح. يرجى تثبيت المكتبات المطلوبة.', 'warning');
            document.getElementById('voiceButton').disabled = true;
            updateStatus('النظام غير متاح');
        }
    } catch (error) {
        console.error('❌ خطأ في فحص حالة النظام:', error);
        showAlert('خطأ في الاتصال بالخادم', 'error');
        updateStatus('خطأ في الاتصال');
    }
}

// تبديل حالة الاستماع
async function toggleVoiceListening() {
    if (!isListening) {
        await startVoiceSession();
    } else {
        await endVoiceSession();
    }
}

// بدء جلسة التحكم الصوتي
async function startVoiceSession() {
    try {
        const response = await fetch('/ai-voice/api/start-voice-session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            isListening = true;
            voiceSession = data.session_id;
            
            updateVoiceButton(true);
            updateStatus('جاري الاستماع... تحدث الآن');
            
            // بدء الاستماع المستمر
            startContinuousListening();
            
            showAlert('تم بدء جلسة التحكم الصوتي', 'success');
        } else {
            showAlert(data.message, 'error');
        }
    } catch (error) {
        console.error('Error starting voice session:', error);
        showAlert('خطأ في بدء جلسة التحكم الصوتي', 'error');
    }
}

// إنهاء جلسة التحكم الصوتي
async function endVoiceSession() {
    try {
        const response = await fetch('/ai-voice/api/end-voice-session', {
            method: 'POST'
        });
        
        const data = await response.json();
        
        isListening = false;
        voiceSession = null;
        
        updateVoiceButton(false);
        updateStatus('جاهز للاستماع');
        
        showAlert('تم إنهاء جلسة التحكم الصوتي', 'info');
    } catch (error) {
        console.error('Error ending voice session:', error);
    }
}

// الاستماع المستمر
async function startContinuousListening() {
    while (isListening) {
        try {
            const response = await fetch('/ai-voice/api/process-voice-command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type: 'listen' })
            });
            
            const data = await response.json();
            
            if (data.success && data.speech_text) {
                displaySpeechFeedback(data.speech_text);
                
                if (data.identified_field && data.extracted_data) {
                    fillFormField(data.identified_field, data.extracted_data);
                    showAlert(`تم ملء ${data.field_name_ar}: ${data.extracted_data}`, 'success');
                }
            }
            
            // توقف قصير قبل الاستماع مرة أخرى
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.error('Error in continuous listening:', error);
            break;
        }
    }
}

// تحديث زر التحكم الصوتي
function updateVoiceButton(listening) {
    const button = document.getElementById('voiceButton');
    const icon = button.querySelector('i');
    
    if (listening) {
        button.classList.add('listening');
        icon.className = 'fas fa-stop';
        button.innerHTML = '<i class="fas fa-stop"></i>';
    } else {
        button.classList.remove('listening');
        icon.className = 'fas fa-microphone';
        button.innerHTML = '<i class="fas fa-microphone"></i>';
    }
}

// تحديث نص الحالة
function updateStatus(text) {
    document.getElementById('statusText').textContent = text;
}

// عرض ما تم سماعه
function displaySpeechFeedback(text) {
    const feedback = document.getElementById('speechFeedback');
    const speechText = document.getElementById('speechText');
    
    speechText.textContent = text;
    feedback.style.display = 'block';
    
    // إخفاء الرسالة بعد 5 ثوان
    setTimeout(() => {
        feedback.style.display = 'none';
    }, 5000);
}

// ملء حقل في النموذج
function fillFormField(field, data) {
    const fieldMap = {
        'contract_number': 'contractNumber',
        'supplier_name': 'supplierName',
        'contract_date': 'contractDate',
        'delivery_date': 'deliveryDate',
        'total_amount': 'totalAmount',
        'currency': 'currency',
        'payment_terms': 'paymentTerms',
        'description': 'description',
        'notes': 'notes'
    };
    
    const elementId = fieldMap[field];
    if (elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.value = data;
            element.focus();
            
            // تأثير بصري
            element.style.backgroundColor = '#d4edda';
            setTimeout(() => {
                element.style.backgroundColor = '';
            }, 2000);
        }
    }
}

// نطق مثال
async function speakExample(text) {
    try {
        await fetch('/ai-voice/api/process-voice-command', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                type: 'speak', 
                text: `مثال: ${text}` 
            })
        });
    } catch (error) {
        console.error('Error speaking example:', error);
    }
}

// حفظ العقد
function saveContract() {
    const formData = new FormData(document.getElementById('contractForm'));
    const contractData = Object.fromEntries(formData);
    
    // هنا يمكن إرسال البيانات للخادم
    console.log('Contract data:', contractData);
    showAlert('تم حفظ بيانات العقد بنجاح!', 'success');
}

// مسح النموذج
function clearForm() {
    document.getElementById('contractForm').reset();
    showAlert('تم مسح النموذج', 'info');
}

// اختبار نظام التحكم الصوتي
async function testVoiceSystem() {
    try {
        console.log('🧪 بدء اختبار النظام...');
        updateStatus('جاري اختبار النظام...');

        const response = await fetch('/ai-voice/api/test-voice');
        const data = await response.json();

        if (data.success) {
            console.log('✅ اختبار النظام نجح:', data);
            showAlert('تم اختبار النظام بنجاح! النظام جاهز للاستخدام.', 'success');
            updateStatus('النظام يعمل بشكل مثالي');
        } else {
            console.log('❌ اختبار النظام فشل:', data);
            showAlert(`فشل اختبار النظام: ${data.message}`, 'error');
            updateStatus('النظام غير جاهز');
        }
    } catch (error) {
        console.error('❌ خطأ في اختبار النظام:', error);
        showAlert('خطأ في اختبار النظام', 'error');
        updateStatus('خطأ في الاختبار');
    }
}

// عرض رسالة تنبيه
function showAlert(message, type) {
    const alertContainer = document.getElementById('alertContainer');
    const alertDiv = document.createElement('div');

    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    alertContainer.appendChild(alertDiv);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
{% endblock %}
