<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صندوق الوارد - نظام البريد الإلكتروني المتقدم</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --card-bg: rgba(255, 255, 255, 0.95);
            --sidebar-bg: rgba(255, 255, 255, 0.9);
            
            --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-heavy: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-extreme: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .email-app {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .email-container {
            width: 100%;
            max-width: 1600px;
            height: 90vh;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-extreme);
            overflow: hidden;
            display: grid;
            grid-template-columns: 320px 1fr;
            animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* الشريط الجانبي */
        .email-sidebar {
            background: var(--sidebar-bg);
            backdrop-filter: blur(15px);
            padding: 32px 24px;
            border-right: 1px solid var(--glass-border);
            overflow-y: auto;
            position: relative;
        }

        .email-sidebar::after {
            content: '';
            position: absolute;
            top: 40px;
            bottom: 40px;
            right: 0;
            width: 2px;
            background: linear-gradient(to bottom, transparent, var(--primary-gradient), transparent);
            border-radius: 1px;
        }

        .compose-btn {
            width: 100%;
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 15px;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-medium);
            margin-bottom: 32px;
            position: relative;
            overflow: hidden;
        }

        .compose-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .compose-btn:hover::before {
            left: 100%;
        }

        .compose-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-heavy);
        }

        .stats-section {
            margin-bottom: 32px;
        }

        .stats-card {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 24px;
            box-shadow: var(--shadow-light);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stats-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .stat-item {
            text-align: center;
            padding: 16px 12px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 12px;
            transition: var(--transition);
            cursor: pointer;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .stat-item:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: var(--shadow-medium);
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 4px;
            display: block;
        }

        .stat-label {
            font-size: 11px;
            color: #6b7280;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .folders-section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .folder-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            margin-bottom: 4px;
            position: relative;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.5);
        }

        .folder-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--primary-gradient);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .folder-item:hover::before {
            transform: scaleY(1);
        }

        .folder-item:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateX(8px);
            box-shadow: var(--shadow-light);
        }

        /* تحسين مظهر روابط المجلدات */
        .folder-item a {
            text-decoration: none !important;
            color: inherit !important;
            display: block !important;
        }

        .folder-item a:hover {
            text-decoration: none !important;
            color: inherit !important;
        }

        .folder-item.active {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .folder-item.active::before {
            transform: scaleY(1);
            background: white;
        }

        .folder-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .folder-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .folder-name {
            font-weight: 500;
            font-size: 14px;
        }

        .folder-count {
            background: var(--danger-gradient);
            color: white;
            font-size: 11px;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 12px;
            min-width: 20px;
            text-align: center;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 12px rgba(245, 101, 101, 0.4);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .account-info {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow-light);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
        }

        .account-info h6 {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .account-info p {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .action-btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 4px 2px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            color: white;
        }

        .btn-primary { background: var(--primary-gradient); }
        .btn-success { background: var(--success-gradient); }
        .btn-warning { background: var(--warning-gradient); }
        .btn-info { background: var(--secondary-gradient); }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        /* المحتوى الرئيسي */
        .email-main {
            background: var(--card-bg);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .email-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 32px 40px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .email-title {
            font-size: 32px;
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .email-subtitle {
            color: #6b7280;
            font-size: 16px;
            font-weight: 500;
        }

        .email-list {
            flex: 1;
            overflow-y: auto;
            padding: 24px 40px;
        }

        .email-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            padding: 24px;
            margin-bottom: 16px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            animation: slideInUp 0.5s ease-out;
        }

        .email-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-gradient);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .email-item:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-heavy);
            border-color: #667eea;
        }

        .email-item:hover::before {
            transform: scaleY(1);
        }

        .email-item.unread {
            background: linear-gradient(135deg, #fef7ff 0%, #faf5ff 100%);
            border-color: #667eea;
            box-shadow: var(--shadow-light);
        }

        .email-item.unread::before {
            transform: scaleY(1);
        }

        .email-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 20px;
        }

        .email-info {
            flex: 1;
            min-width: 0;
        }

        .email-sender {
            font-weight: 700;
            font-size: 16px;
            color: #1f2937;
            margin-bottom: 6px;
        }

        .email-subject {
            font-size: 15px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .email-preview {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .email-date {
            font-size: 13px;
            color: #9ca3af;
            font-weight: 500;
        }

        .empty-state {
            text-align: center;
            padding: 80px 40px;
            color: #9ca3af;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 24px;
            color: #e5e7eb;
        }

        .empty-state h4 {
            font-size: 20px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
        }

        .empty-state p {
            font-size: 16px;
            color: #6b7280;
            max-width: 400px;
            margin: 0 auto;
            line-height: 1.6;
        }

        /* Modal لعرض محتوى الرسالة */
        .email-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .email-modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .email-modal-content {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-extreme);
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            animation: slideInUp 0.3s ease;
        }

        .email-modal-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 24px 32px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .email-modal-title {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
            margin: 0;
        }

        .email-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
            transition: var(--transition);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .email-modal-close:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .email-modal-body {
            padding: 32px;
            overflow-y: auto;
            max-height: 60vh;
        }

        .email-details {
            margin-bottom: 24px;
            padding-bottom: 24px;
            border-bottom: 1px solid #e5e7eb;
        }

        .email-detail-row {
            display: flex;
            margin-bottom: 12px;
        }

        .email-detail-label {
            font-weight: 600;
            color: #374151;
            width: 100px;
            flex-shrink: 0;
        }

        .email-detail-value {
            color: #6b7280;
            flex: 1;
        }

        .email-body {
            line-height: 1.6;
            color: #374151;
        }

        .email-body h1, .email-body h2, .email-body h3 {
            margin-bottom: 16px;
            color: #1f2937;
        }

        .email-body p {
            margin-bottom: 16px;
        }

        .email-body img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 16px 0;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* تحسين عرض الرسائل */
        .email-item {
            cursor: pointer;
        }

        .email-item:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-heavy);
            border-color: #667eea;
        }

        /* شريط التنقل العلوي */
        .top-navigation {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            box-sizing: border-box;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nav-back-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
        }

        .nav-back-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .nav-breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-link {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #6b7280;
            text-decoration: none;
            padding: 6px 12px;
            border-radius: 8px;
            transition: var(--transition);
        }

        .breadcrumb-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .breadcrumb-separator {
            color: #d1d5db;
            font-size: 12px;
        }

        .breadcrumb-current {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #374151;
            font-weight: 600;
            padding: 6px 12px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }

        .nav-action-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: rgba(255, 255, 255, 0.8);
            color: #374151;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
        }

        .nav-action-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
        }

        /* تعديل المساحة العلوية للمحتوى */
        .email-app {
            padding-top: 80px;
        }

        /* التصميم المتجاوب لشريط التنقل */
        @media (max-width: 768px) {
            .top-navigation {
                flex-direction: column;
                gap: 16px;
                padding: 16px;
            }

            .nav-left, .nav-right {
                width: 100%;
                justify-content: center;
            }

            .nav-breadcrumb {
                display: none;
            }

            .nav-back-btn {
                width: 100%;
                justify-content: center;
            }

            .email-app {
                padding-top: 140px;
            }
        }

        /* أزرار الإجراءات */
        .email-item:hover .email-actions {
            opacity: 1 !important;
        }

        .action-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .delete-btn:hover {
            background: #fecaca !important;
            color: #b91c1c !important;
        }

        .archive-btn:hover {
            background: #bae6fd !important;
            color: #0c4a6e !important;
        }

        .important-btn:hover {
            background: #fed7aa !important;
            color: #c2410c !important;
        }

        .reply-btn:hover {
            background: #bbf7d0 !important;
            color: #15803d !important;
        }

        .forward-btn:hover {
            background: #e9d5ff !important;
            color: #7c3aed !important;
        }

        /* تنسيق المرفقات */
        .attachments-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .attachment-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .attachment-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .attachment-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 18px;
            color: white;
        }

        .attachment-icon.pdf { background: #dc2626; }
        .attachment-icon.doc { background: #2563eb; }
        .attachment-icon.image { background: #059669; }
        .attachment-icon.excel { background: #16a34a; }
        .attachment-icon.zip { background: #7c3aed; }
        .attachment-icon.default { background: #6b7280; }

        .attachment-info {
            flex: 1;
        }

        .attachment-name {
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .attachment-size {
            font-size: 12px;
            color: #6b7280;
        }

        .attachment-download {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .attachment-download:hover {
            background: #2563eb;
        }

        /* تنسيق خيارات الترتيب */
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .header-info {
            flex: 1;
        }

        .sort-options {
            display: flex;
            align-items: center;
            gap: 10px;
            background: #f8fafc;
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .sort-options select {
            min-width: 200px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .sort-options select:hover {
            border-color: #3b82f6;
        }

        .sort-options select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                align-items: stretch;
            }

            .sort-options {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <div class="top-navigation">
        <div class="nav-left">
            <button class="nav-back-btn" onclick="goToDashboard()">
                <i class="fas fa-arrow-right"></i>
                العودة للوحة التحكم
            </button>
            <div class="nav-breadcrumb">
                <a href="/dashboard" class="breadcrumb-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
                <i class="fas fa-chevron-left breadcrumb-separator"></i>
                <span class="breadcrumb-current">
                    <i class="fas fa-envelope"></i>
                    البريد الإلكتروني
                </span>
            </div>
        </div>
        <div class="nav-right">
            <a href="/email/compose-new" class="nav-action-btn">
                <i class="fas fa-edit"></i>
                إنشاء رسالة
            </a>
            <a href="/email/settings" class="nav-action-btn">
                <i class="fas fa-cog"></i>
                الإعدادات
            </a>
            <a href="/email/analytics" class="nav-action-btn">
                <i class="fas fa-chart-bar"></i>
                التحليلات
            </a>
            <button class="nav-action-btn" onclick="goToDashboard()">
                <i class="fas fa-tachometer-alt"></i>
                لوحة التحكم
            </button>
        </div>
    </div>

    <div class="email-app">
        <div class="email-container">
            <!-- الشريط الجانبي -->
            <div class="email-sidebar">
                <!-- زر إنشاء رسالة -->
                <button class="compose-btn" onclick="window.location.href='/email/compose-new'">
                    <i class="fas fa-plus" style="margin-left: 8px;"></i>
                    إنشاء رسالة جديدة
                </button>

                <!-- إحصائيات سريعة -->
                <div class="stats-section">
                    <div class="stats-card">
                        <div class="stats-title">
                            <i class="fas fa-chart-bar"></i>
                            إحصائيات سريعة
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-number">{{ stats.total_emails if stats else 0 }}</span>
                                <span class="stat-label">إجمالي</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">{{ stats.unread_count if stats else 0 }}</span>
                                <span class="stat-label">غير مقروء</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">{{ emails|length if emails else 0 }}</span>
                                <span class="stat-label">معروض</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">{{ stats.important_count if stats else 0 }}</span>
                                <span class="stat-label">مهم</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المجلدات -->
                <div class="folders-section">
                    <div class="section-title">
                        <i class="fas fa-folder"></i>
                        المجلدات
                    </div>
                    {% if folders %}
                        {% for folder in folders %}
                        <a href="{% if folder.folder_type == 'inbox' %}/email/inbox{% elif folder.folder_type == 'sent' %}/email/sent-box{% elif folder.folder_type == 'drafts' %}/email/drafts-box{% elif folder.folder_type == 'trash' %}/email/trash-box{% else %}/email/folder/{{ folder.folder_type }}{% endif %}"
                           class="folder-item {% if current_folder == folder.folder_type or (not current_folder and folder.folder_type == 'inbox') %}active{% endif %}"
                           style="text-decoration: none; color: inherit; display: block;">
                            <div class="folder-info">
                                <div class="folder-icon">
                                    <i class="fas fa-{% if folder.folder_type == 'inbox' %}inbox{% elif folder.folder_type == 'sent' %}paper-plane{% elif folder.folder_type == 'archive' %}archive{% elif folder.folder_type == 'important' %}star{% elif folder.folder_type == 'unread' %}envelope{% elif folder.folder_type == 'drafts' %}file-alt{% elif folder.folder_type == 'trash' %}trash{% else %}folder{% endif %}"></i>
                                </div>
                                <span class="folder-name">{{ folder.name_arabic }}</span>
                                {% if folder.unread_count > 0 %}
                                <span class="folder-count">{{ folder.unread_count }}</span>
                                {% endif %}
                            </div>
                        </a>
                        {% endfor %}
                    {% else %}
                        <div class="folder-item active">
                            <div class="folder-info">
                                <div class="folder-icon">
                                    <i class="fas fa-inbox"></i>
                                </div>
                                <span class="folder-name">صندوق الوارد</span>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- معلومات الحساب -->
                <div class="account-info">
                    <h6>{{ account.display_name if account else 'المستخدم' }}</h6>
                    <p>{{ account.email_address if account else '<EMAIL>' }}</p>

                    <button class="action-btn btn-primary" onclick="fetchRealEmails()">
                        <i class="fas fa-download"></i>
                        جلب الرسائل
                    </button>
                    <button class="action-btn btn-success" onclick="syncAccount()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>

                    <button class="action-btn btn-warning" onclick="window.location.href='/email/settings'">
                        <i class="fas fa-cog"></i>
                        إعدادات الحساب
                    </button>
                    <button class="action-btn btn-info" onclick="window.location.href='/email/analytics'">
                        <i class="fas fa-chart-bar"></i>
                        التحليلات
                    </button>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="email-main">
                <!-- رأس الصفحة -->
                <div class="email-header">
                    <div class="header-content">
                        <div class="header-info">
                            <h1 class="email-title">صندوق الوارد</h1>
                            <p class="email-subtitle">{{ account.email_address if account else '<EMAIL>' }}</p>
                        </div>

                        <!-- خيارات الترتيب -->
                        <div class="sort-options">
                            <label for="sortSelect" style="margin-left: 10px; font-weight: 500; color: #374151;">ترتيب حسب:</label>
                            <select id="sortSelect" onchange="sortEmails(this.value)" style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; font-size: 14px;">
                                <option value="date_desc">التاريخ (الأحدث أولاً)</option>
                                <option value="date_asc">التاريخ (الأقدم أولاً)</option>
                                <option value="important_first">المهمة أولاً</option>
                                <option value="unread_first">غير المقروءة أولاً</option>
                                <option value="sender_asc">المرسل (أ-ي)</option>
                                <option value="sender_desc">المرسل (ي-أ)</option>
                                <option value="subject_asc">الموضوع (أ-ي)</option>
                                <option value="attachments_first">المرفقات أولاً</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- قائمة الرسائل -->
                <div class="email-list" id="emailList">
                    {% if emails %}
                        {% for email in emails %}
                        <div class="email-item {% if not email.is_read %}unread{% endif %}"
                             data-email-id="{{ email.id }}"
                             data-sender="{{ email.sender_name or email.sender_email }}"
                             data-subject="{{ email.subject or '(بدون موضوع)' }}"
                             data-body="{{ (email.body_html or email.body_text or 'لا يوجد محتوى')|e }}"
                             data-body-html="{{ (email.body_html or '')|e }}"
                             data-body-text="{{ (email.body_text or '')|e }}"
                             data-date="{% if email.received_at %}{{ email.received_at.strftime('%d/%m/%Y %H:%M') }}{% else %}{{ email.created_at.strftime('%d/%m/%Y %H:%M') }}{% endif %}"
                             data-has-attachments="{{ 'true' if email.has_attachments else 'false' }}"
                             onclick="openEmailModal(this)">

                            <div class="email-content">
                                <div class="email-info">
                                    <div class="email-sender">{{ email.sender_name or email.sender_email }}</div>
                                    <div class="email-subject">{{ email.subject or '(بدون موضوع)' }}</div>
                                    <div class="email-preview">
                                        {% if email.body_html %}
                                            {{ (email.body_html|striptags)[:150] }}...
                                        {% elif email.body_text %}
                                            {{ email.body_text[:150] }}...
                                        {% else %}
                                            لا يوجد محتوى
                                        {% endif %}
                                    </div>

                                    <!-- عرض المرفقات -->
                                    {% if email.has_attachments %}
                                    <div class="email-attachments" style="margin-top: 8px;">
                                        <span style="background: #e0f2fe; color: #0369a1; padding: 2px 8px; border-radius: 12px; font-size: 12px;">
                                            <i class="fas fa-paperclip"></i>
                                            {% if email.attachments %}
                                                {{ email.attachments|length }} مرفق
                                            {% else %}
                                                يحتوي على مرفقات
                                            {% endif %}
                                        </span>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="email-date">
                                    {% if email.received_at %}
                                        {{ email.received_at.strftime('%d/%m %H:%M') }}
                                    {% else %}
                                        {{ email.created_at.strftime('%d/%m %H:%M') }}
                                    {% endif %}
                                </div>
                            </div>

                            <!-- أزرار الإجراءات -->
                            <div class="email-actions" style="padding: 8px 15px; border-top: 1px solid #e5e7eb; background: #f8fafc; display: flex; gap: 6px; justify-content: flex-end; opacity: 0; transition: opacity 0.2s;">
                                <button class="action-btn delete-btn" onclick="event.stopPropagation(); deleteEmail({{ loop.index0 }})" title="حذف" style="background: #fee2e2; color: #dc2626; border: none; padding: 6px 8px; border-radius: 6px; cursor: pointer; font-size: 12px; transition: all 0.2s;">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <button class="action-btn archive-btn" onclick="event.stopPropagation(); archiveEmail({{ loop.index0 }})" title="أرشفة" style="background: #e0f2fe; color: #0369a1; border: none; padding: 6px 8px; border-radius: 6px; cursor: pointer; font-size: 12px; transition: all 0.2s;">
                                    <i class="fas fa-archive"></i>
                                </button>
                                <button class="action-btn important-btn" onclick="event.stopPropagation(); toggleImportant({{ loop.index0 }})" title="مهم" style="background: #fef3c7; color: #d97706; border: none; padding: 6px 8px; border-radius: 6px; cursor: pointer; font-size: 12px; transition: all 0.2s;">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button class="action-btn reply-btn" onclick="event.stopPropagation(); replyToEmailQuick({{ loop.index0 }})" title="رد سريع" style="background: #dcfce7; color: #16a34a; border: none; padding: 6px 8px; border-radius: 6px; cursor: pointer; font-size: 12px; transition: all 0.2s;">
                                    <i class="fas fa-reply"></i>
                                </button>
                                <button class="action-btn forward-btn" onclick="event.stopPropagation(); forwardEmail({{ loop.index0 }})" title="إعادة توجيه" style="background: #f3e8ff; color: #9333ea; border: none; padding: 6px 8px; border-radius: 6px; cursor: pointer; font-size: 12px; transition: all 0.2s;">
                                    <i class="fas fa-share"></i>
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <h4>لا توجد رسائل</h4>
                            <p>صندوق الوارد فارغ. جرب جلب الرسائل الحقيقية من الخادم.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لعرض محتوى الرسالة -->
    <div class="email-modal" id="emailModal">
        <div class="email-modal-content">
            <div class="email-modal-header">
                <h3 class="email-modal-title" id="modalTitle">تفاصيل الرسالة</h3>
                <button class="email-modal-close" onclick="closeEmailModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="email-modal-body">
                <div class="email-details">
                    <div class="email-detail-row">
                        <span class="email-detail-label">من:</span>
                        <span class="email-detail-value" id="modalSender"></span>
                    </div>
                    <div class="email-detail-row">
                        <span class="email-detail-label">الموضوع:</span>
                        <span class="email-detail-value" id="modalSubject"></span>
                    </div>
                    <div class="email-detail-row">
                        <span class="email-detail-label">التاريخ:</span>
                        <span class="email-detail-value" id="modalDate"></span>
                    </div>
                </div>
                <div class="email-body" id="modalBody">
                    <!-- محتوى الرسالة سيتم إدراجه هنا -->
                </div>

                <!-- قسم المرفقات -->
                <div id="attachmentsSection" style="display: none; padding: 20px; border-top: 1px solid #e5e7eb; background: #f8fafc;">
                    <h4 style="margin: 0 0 15px 0; color: #374151; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-paperclip"></i>
                        المرفقات
                    </h4>
                    <div id="attachmentsList" class="attachments-list">
                        <!-- المرفقات ستُضاف هنا -->
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div style="padding: 20px; border-top: 1px solid #e5e7eb; background: #f9fafb; display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="btn btn-secondary" onclick="closeEmailModal()">إغلاق</button>
                    <button type="button" class="btn btn-warning" onclick="debugEmail()" id="debugBtn">🔍 تشخيص</button>
                    <button type="button" class="btn btn-success" onclick="convertEmail()" id="convertBtn">🎨 تحويل لـ HTML</button>
                    <button type="button" class="btn btn-info" onclick="openInBrowser()" id="openBrowserBtn">🌐 فتح في المتصفح</button>
                    <button type="button" class="btn btn-primary" onclick="replyToEmail()">رد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة الرد السريع -->
    <div id="quickReplyModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000;">
        <div class="modal-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 15px; width: 90%; max-width: 600px; max-height: 80%; overflow-y: auto; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0;">
                <h3 style="margin: 0; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-reply"></i>
                    رد سريع
                </h3>
                <button onclick="closeQuickReply()" style="position: absolute; top: 15px; right: 15px; background: none; border: none; color: white; font-size: 20px; cursor: pointer;">×</button>
            </div>
            <div class="modal-body" style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">إلى:</label>
                    <input type="text" id="replyTo" readonly style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 8px; background: #f9fafb;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">الموضوع:</label>
                    <input type="text" id="replySubject" style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 8px;">
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">الرسالة:</label>
                    <textarea id="replyMessage" rows="8" style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 8px; resize: vertical;" placeholder="اكتب ردك هنا..."></textarea>
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="closeQuickReply()" style="background: #6b7280; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">إلغاء</button>
                    <button onclick="sendQuickReply()" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-paper-plane"></i> إرسال
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إعادة التوجيه -->
    <div id="forwardModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000;">
        <div class="modal-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 15px; width: 90%; max-width: 600px; max-height: 80%; overflow-y: auto; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0;">
                <h3 style="margin: 0; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-share"></i>
                    إعادة توجيه
                </h3>
                <button onclick="closeForward()" style="position: absolute; top: 15px; right: 15px; background: none; border: none; color: white; font-size: 20px; cursor: pointer;">×</button>
            </div>
            <div class="modal-body" style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">إلى:</label>
                    <input type="email" id="forwardTo" style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 8px;" placeholder="أدخل عنوان البريد الإلكتروني">
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">الموضوع:</label>
                    <input type="text" id="forwardSubject" style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 8px;">
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">رسالة إضافية (اختيارية):</label>
                    <textarea id="forwardMessage" rows="4" style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 8px; resize: vertical;" placeholder="أضف رسالة إضافية إذا أردت..."></textarea>
                </div>
                <div style="background: #f9fafb; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <h4 style="margin: 0 0 10px 0; color: #6b7280; font-size: 14px;">الرسالة الأصلية:</h4>
                    <div id="originalMessage" style="max-height: 200px; overflow-y: auto; font-size: 14px; color: #4b5563;"></div>
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="closeForward()" style="background: #6b7280; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">إلغاء</button>
                    <button onclick="sendForward()" style="background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%); color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-share"></i> إعادة توجيه
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // فتح modal لعرض محتوى الرسالة
        function openEmailModal(element) {
            // استخراج البيانات من العنصر
            const emailId = element.getAttribute('data-email-id');
            const sender = element.getAttribute('data-sender');
            const subject = element.getAttribute('data-subject');
            const body = element.getAttribute('data-body');
            const date = element.getAttribute('data-date');

            // عرض البيانات في الـ modal
            document.getElementById('modalSender').textContent = sender || 'غير محدد';
            document.getElementById('modalSubject').textContent = subject || '(بدون موضوع)';
            document.getElementById('modalDate').textContent = date || 'غير محدد';

            // تنظيف وعرض محتوى الرسالة مع إعطاء أولوية للـ HTML
            const bodyHtml = element.getAttribute('data-body-html');
            const bodyText = element.getAttribute('data-body-text');
            const modalBody = document.getElementById('modalBody');

            // حفظ المحتوى للاستخدام في فتح المتصفح
            window.currentEmailContent = {
                html: bodyHtml || '',
                text: bodyText || '',
                subject: subject || '',
                emailId: emailId || ''
            };

            // عرض المرفقات إذا وُجدت
            const hasAttachments = element.getAttribute('data-has-attachments') === 'true';
            const attachmentsSection = document.getElementById('attachmentsSection');

            if (hasAttachments) {
                // جلب المرفقات من الخادم
                loadAttachments(emailId);
                attachmentsSection.style.display = 'block';
            } else {
                attachmentsSection.style.display = 'none';
            }

            // تشخيص عميق في المتصفح
            console.log('🔍 تشخيص المحتوى:');
            console.log('📄 HTML Length:', bodyHtml ? bodyHtml.length : 0);
            console.log('📄 Text Length:', bodyText ? bodyText.length : 0);
            console.log('🌐 HTML Preview:', bodyHtml ? bodyHtml.substring(0, 300) : 'لا يوجد');
            console.log('📝 Text Preview:', bodyText ? bodyText.substring(0, 300) : 'لا يوجد');

            if (bodyHtml && bodyHtml.trim() !== '') {
                // عرض HTML مع تحسينات للعرض
                modalBody.innerHTML = bodyHtml;

                // إضافة CSS لتحسين العرض
                modalBody.style.cssText = `
                    max-width: 100%;
                    overflow-x: auto;
                    line-height: 1.6;
                    font-family: Arial, sans-serif;
                    padding: 20px;
                `;

                // تحسين الصور
                const images = modalBody.querySelectorAll('img');
                images.forEach(img => {
                    img.style.maxWidth = '100%';
                    img.style.height = 'auto';
                    img.style.borderRadius = '8px';
                });

                // تحسين الروابط
                const links = modalBody.querySelectorAll('a');
                links.forEach(link => {
                    link.style.color = '#3b82f6';
                    link.target = '_blank';
                });

            } else if (bodyText && bodyText.trim() !== '') {
                // عرض النص العادي مع تحسين الروابط
                let improvedText = bodyText;

                // تحسين الروابط في النص
                improvedText = improveLinksInText(improvedText);

                modalBody.innerHTML = '<div style="white-space: pre-wrap; font-family: Arial, sans-serif; line-height: 1.6; padding: 20px;">' +
                                     improvedText + '</div>';
            } else {
                modalBody.innerHTML = '<div style="text-align: center; padding: 40px; color: #9ca3af;"><i class="fas fa-envelope-open" style="font-size: 3rem; margin-bottom: 20px; display: block;"></i><h4>لا يوجد محتوى</h4><p>هذه الرسالة لا تحتوي على محتوى نصي.</p></div>';
            }

            // عرض الـ modal
            document.getElementById('emailModal').classList.add('show');

            // تحديد الرسالة كمقروءة
            if (emailId) {
                markAsRead(emailId);
            }
        }

        // إغلاق modal
        function closeEmailModal() {
            document.getElementById('emailModal').classList.remove('show');
        }

        // دالة تحسين الروابط في النص
        function improveLinksInText(text) {
            // تحويل الروابط الطويلة إلى أزرار جميلة

            // روابط YouTube
            text = text.replace(
                /\(\s*(https?:\/\/(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/)[^\s)]+)\s*\)/g,
                '<br><a href="$1" target="_blank" style="display: inline-block; background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; margin: 5px 0; font-weight: 500;">🎥 مشاهدة الفيديو</a><br>'
            );

            // روابط Augment
            text = text.replace(
                /\(\s*(https?:\/\/(?:www\.)?augmentcode\.com[^\s)]*)\s*\)/g,
                '<br><a href="$1" target="_blank" style="display: inline-block; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; margin: 5px 0; font-weight: 500;">🚀 موقع Augment</a><br>'
            );

            // روابط إعدادات البريد
            text = text.replace(
                /\(\s*(https?:\/\/[^\s)]*customer\.io[^\s)]*)\s*\)/g,
                '<br><a href="$1" target="_blank" style="display: inline-block; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333; padding: 8px 16px; border-radius: 20px; text-decoration: none; margin: 5px 0; font-weight: 500;">⚙️ إعدادات البريد</a><br>'
            );

            // روابط X (Twitter)
            text = text.replace(
                /\(\s*(https?:\/\/(?:www\.)?x\.com[^\s)]*)\s*\)/g,
                '<br><a href="$1" target="_blank" style="display: inline-block; background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; margin: 5px 0; font-weight: 500;">🐦 تويتر</a><br>'
            );

            // روابط LinkedIn
            text = text.replace(
                /\(\s*(https?:\/\/(?:www\.)?linkedin\.com[^\s)]*)\s*\)/g,
                '<br><a href="$1" target="_blank" style="display: inline-block; background: linear-gradient(135deg, #0077b5 0%, #005885 100%); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; margin: 5px 0; font-weight: 500;">💼 لينكد إن</a><br>'
            );

            // الروابط العامة الأخرى (مع الأقواس)
            text = text.replace(
                /\(\s*(https?:\/\/[^\s)]+)\s*\)/g,
                '<br><a href="$1" target="_blank" style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; margin: 5px 0; font-weight: 500;">🔗 رابط</a><br>'
            );

            // تحويل أسطر جديدة إلى <br>
            text = text.replace(/\n/g, '<br>');

            return text;
        }

        // فتح المحتوى في نافذة متصفح جديدة
        function openInBrowser() {
            const content = window.currentEmailContent;
            if (!content) {
                alert('لا يوجد محتوى لعرضه');
                return;
            }

            const htmlContent = content.html;
            const textContent = content.text;
            const subject = content.subject;

            if (htmlContent && htmlContent.trim() !== '') {
                // إنشاء HTML كامل للعرض
                const fullHtml = `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject || 'رسالة بريد إلكتروني'}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; background: #f5f5f5; }
        .email-container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 1000px; margin: 0 auto; }
        .email-header { border-bottom: 2px solid #e5e7eb; padding-bottom: 20px; margin-bottom: 30px; }
        .email-title { color: #1f2937; font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .email-content { line-height: 1.8; }
        img { max-width: 100% !important; height: auto !important; border-radius: 8px; }
        a { color: #3b82f6; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <div class="email-title">${subject || 'رسالة بريد إلكتروني'}</div>
            <div style="color: #6b7280; font-size: 14px;">📧 محتوى HTML - تم فتحه في المتصفح لعرض أفضل</div>
        </div>
        <div class="email-content">
            ${htmlContent}
        </div>
    </div>
</body>
</html>`;

                // فتح نافذة جديدة وكتابة المحتوى
                const newWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
                newWindow.document.write(fullHtml);
                newWindow.document.close();

            } else if (textContent && textContent.trim() !== '') {
                // إذا كان نص عادي فقط
                const textHtml = `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject || 'رسالة بريد إلكتروني'}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.8; margin: 20px; background: #f5f5f5; }
        .email-container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 800px; margin: 0 auto; }
        .email-header { border-bottom: 2px solid #e5e7eb; padding-bottom: 20px; margin-bottom: 30px; }
        .email-title { color: #1f2937; font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .email-content { white-space: pre-wrap; font-size: 16px; color: #374151; }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <div class="email-title">${subject || 'رسالة بريد إلكتروني'}</div>
            <div style="color: #6b7280; font-size: 14px;">📝 محتوى نصي</div>
        </div>
        <div class="email-content">${textContent}</div>
    </div>
</body>
</html>`;

                const newWindow = window.open('', '_blank', 'width=1000,height=700,scrollbars=yes,resizable=yes');
                newWindow.document.write(textHtml);
                newWindow.document.close();

            } else {
                alert('لا يوجد محتوى لعرضه');
            }
        }

        // فتح صفحة التشخيص
        function debugEmail() {
            const content = window.currentEmailContent;
            if (!content || !content.emailId) {
                alert('لا يوجد رسالة محددة للتشخيص');
                return;
            }

            // فتح صفحة التشخيص في نافذة جديدة
            const debugUrl = `/email/debug_email/${content.emailId}`;
            window.open(debugUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        }

        // تحويل الرسالة إلى HTML غني
        function convertEmail() {
            const content = window.currentEmailContent;
            if (!content || !content.emailId) {
                alert('لا يوجد رسالة محددة للتحويل');
                return;
            }

            if (confirm('هل تريد تحويل هذه الرسالة إلى HTML غني؟')) {
                // فتح صفحة التحويل في نافذة جديدة
                const convertUrl = `/email/convert_email/${content.emailId}`;
                window.open(convertUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
            }
        }

        // إجراءات الرسائل
        function deleteEmail(emailIndex) {
            console.log('🗑️ محاولة حذف الرسالة:', emailIndex);

            if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
                // الحصول على ID الرسالة من البيانات
                const emailItems = document.querySelectorAll('.email-item');
                const emailItem = emailItems[emailIndex];
                const emailId = emailItem.getAttribute('data-email-id');

                console.log('📧 معرف الرسالة:', emailId);

                if (!emailId || emailId === 'None') {
                    alert('لا يمكن العثور على معرف الرسالة');
                    return;
                }

                // إظهار مؤشر التحميل
                const deleteBtn = emailItem.querySelector('.delete-btn');
                const originalContent = deleteBtn.innerHTML;
                deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                deleteBtn.disabled = true;

                // إرسال طلب الحذف
                fetch(`/email/delete_email/${emailId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📊 بيانات الاستجابة:', data);

                    if (data.success) {
                        // إزالة الرسالة من الواجهة
                        emailItem.style.transition = 'all 0.3s ease';
                        emailItem.style.opacity = '0';
                        emailItem.style.transform = 'translateX(-100%)';
                        setTimeout(() => {
                            emailItem.remove();
                        }, 300);

                        showNotification('تم حذف الرسالة بنجاح', 'success');
                    } else {
                        // استعادة الزر
                        deleteBtn.innerHTML = originalContent;
                        deleteBtn.disabled = false;
                        showNotification('خطأ: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ:', error);
                    // استعادة الزر
                    deleteBtn.innerHTML = originalContent;
                    deleteBtn.disabled = false;
                    showNotification('حدث خطأ في حذف الرسالة', 'error');
                });
            }
        }

        function archiveEmail(emailIndex) {
            console.log('📦 محاولة أرشفة الرسالة:', emailIndex);

            const emailItems = document.querySelectorAll('.email-item');
            const emailItem = emailItems[emailIndex];
            const emailId = emailItem.getAttribute('data-email-id');

            console.log('📧 معرف الرسالة:', emailId);

            if (!emailId || emailId === 'None') {
                showNotification('لا يمكن العثور على معرف الرسالة', 'error');
                return;
            }

            // إظهار مؤشر التحميل
            const archiveBtn = emailItem.querySelector('.archive-btn');
            const originalContent = archiveBtn.innerHTML;
            archiveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            archiveBtn.disabled = true;

            // إرسال طلب الأرشفة
            fetch(`/email/archive_email/${emailId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                console.log('📡 استجابة الأرشفة:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('📊 بيانات الأرشفة:', data);

                if (data.success) {
                    // تغيير مظهر الرسالة لتبدو مؤرشفة
                    emailItem.style.opacity = '0.6';
                    emailItem.style.background = '#f3f4f6';

                    // إضافة علامة الأرشفة
                    const archiveLabel = document.createElement('span');
                    archiveLabel.innerHTML = '📦 مؤرشفة';
                    archiveLabel.style.cssText = 'background: #e5e7eb; color: #6b7280; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px;';
                    emailItem.querySelector('.email-subject').appendChild(archiveLabel);

                    showNotification('تم أرشفة الرسالة بنجاح', 'success');
                } else {
                    // استعادة الزر
                    archiveBtn.innerHTML = originalContent;
                    archiveBtn.disabled = false;
                    showNotification('خطأ: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('❌ خطأ في الأرشفة:', error);
                // استعادة الزر
                archiveBtn.innerHTML = originalContent;
                archiveBtn.disabled = false;
                showNotification('حدث خطأ في أرشفة الرسالة', 'error');
            });
        }

        function toggleImportant(emailIndex) {
            const emailItems = document.querySelectorAll('.email-item');
            const emailItem = emailItems[emailIndex];
            const emailId = emailItem.getAttribute('data-email-id');

            if (!emailId) {
                alert('لا يمكن العثور على معرف الرسالة');
                return;
            }

            // إرسال طلب تبديل الأهمية
            fetch(`/email/toggle_important/${emailId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث مظهر الرسالة
                    const importantBtn = emailItem.querySelector('.important-btn');
                    if (data.is_important) {
                        emailItem.style.borderLeft = '4px solid #f59e0b';
                        importantBtn.style.background = '#fed7aa';
                        importantBtn.style.color = '#c2410c';

                        // إضافة نجمة للموضوع
                        const starIcon = document.createElement('span');
                        starIcon.innerHTML = '⭐';
                        starIcon.className = 'star-icon';
                        starIcon.style.marginLeft = '5px';
                        emailItem.querySelector('.email-subject').appendChild(starIcon);
                    } else {
                        emailItem.style.borderLeft = 'none';
                        importantBtn.style.background = '#fef3c7';
                        importantBtn.style.color = '#d97706';

                        // إزالة النجمة
                        const starIcon = emailItem.querySelector('.star-icon');
                        if (starIcon) starIcon.remove();
                    }

                    showNotification(data.message, 'success');
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                alert('حدث خطأ في تحديث أهمية الرسالة');
            });
        }

        function replyToEmailQuick(emailIndex) {
            console.log('💬 فتح نافذة الرد السريع:', emailIndex);

            const emailItems = document.querySelectorAll('.email-item');
            const emailItem = emailItems[emailIndex];

            // الحصول على بيانات الرسالة
            const sender = emailItem.getAttribute('data-sender');
            const subject = emailItem.getAttribute('data-subject');
            const originalBody = emailItem.getAttribute('data-body-text') || emailItem.getAttribute('data-body');

            // ملء نافذة الرد
            document.getElementById('replyTo').value = sender;
            document.getElementById('replySubject').value = subject.startsWith('Re:') ? subject : 'Re: ' + subject;
            document.getElementById('replyMessage').value = `\n\n--- الرسالة الأصلية ---\nمن: ${sender}\nالموضوع: ${subject}\n\n${originalBody}`;

            // إظهار النافذة
            document.getElementById('quickReplyModal').style.display = 'block';
        }

        function forwardEmail(emailIndex) {
            console.log('📤 فتح نافذة إعادة التوجيه:', emailIndex);

            const emailItems = document.querySelectorAll('.email-item');
            const emailItem = emailItems[emailIndex];

            // الحصول على بيانات الرسالة
            const sender = emailItem.getAttribute('data-sender');
            const subject = emailItem.getAttribute('data-subject');
            const originalBody = emailItem.getAttribute('data-body-text') || emailItem.getAttribute('data-body');
            const date = emailItem.getAttribute('data-date');

            // ملء نافذة إعادة التوجيه
            document.getElementById('forwardSubject').value = subject.startsWith('Fwd:') ? subject : 'Fwd: ' + subject;
            document.getElementById('originalMessage').innerHTML = `
                <strong>من:</strong> ${sender}<br>
                <strong>التاريخ:</strong> ${date}<br>
                <strong>الموضوع:</strong> ${subject}<br><br>
                ${originalBody.replace(/\n/g, '<br>')}
            `;

            // إظهار النافذة
            document.getElementById('forwardModal').style.display = 'block';
        }

        // دوال إدارة النوافذ
        function closeQuickReply() {
            document.getElementById('quickReplyModal').style.display = 'none';
            // تنظيف الحقول
            document.getElementById('replyMessage').value = '';
        }

        function closeForward() {
            document.getElementById('forwardModal').style.display = 'none';
            // تنظيف الحقول
            document.getElementById('forwardTo').value = '';
            document.getElementById('forwardMessage').value = '';
        }

        function sendQuickReply() {
            const replyTo = document.getElementById('replyTo').value;
            const subject = document.getElementById('replySubject').value;
            const message = document.getElementById('replyMessage').value;

            if (!message.trim()) {
                alert('يرجى كتابة رسالة');
                return;
            }

            // إظهار مؤشر التحميل
            const sendBtn = event.target;
            const originalText = sendBtn.innerHTML;
            sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            sendBtn.disabled = true;

            // إرسال الرد (محاكاة - يمكن ربطه بـ API حقيقي)
            setTimeout(() => {
                showNotification('تم إرسال الرد بنجاح!', 'success');
                closeQuickReply();
                sendBtn.innerHTML = originalText;
                sendBtn.disabled = false;
            }, 2000);
        }

        function sendForward() {
            const forwardTo = document.getElementById('forwardTo').value;
            const subject = document.getElementById('forwardSubject').value;
            const message = document.getElementById('forwardMessage').value;

            if (!forwardTo.trim()) {
                alert('يرجى إدخال عنوان البريد الإلكتروني');
                return;
            }

            // التحقق من صحة البريد الإلكتروني
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(forwardTo)) {
                alert('يرجى إدخال عنوان بريد إلكتروني صحيح');
                return;
            }

            // إظهار مؤشر التحميل
            const sendBtn = event.target;
            const originalText = sendBtn.innerHTML;
            sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            sendBtn.disabled = true;

            // إرسال إعادة التوجيه (محاكاة - يمكن ربطه بـ API حقيقي)
            setTimeout(() => {
                showNotification(`تم إعادة توجيه الرسالة إلى ${forwardTo}`, 'success');
                closeForward();
                sendBtn.innerHTML = originalText;
                sendBtn.disabled = false;
            }, 2000);
        }

        // دالة إظهار الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-weight: 500;
                transition: all 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // إزالة الإشعار بعد 3 ثوان
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // إغلاق modal عند النقر خارجه
        document.getElementById('emailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEmailModal();
            }
        });

        // إغلاق modal بمفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeEmailModal();
            }
        });

        // تحديد رسالة كمقروءة
        function markAsRead(emailId) {
            fetch(`/email/api/mark-read/${emailId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const emailItem = document.querySelector(`[data-email-id="${emailId}"]`);
                    if (emailItem) {
                        emailItem.classList.remove('unread');
                    }
                }
            })
            .catch(error => {
                console.log('خطأ في تحديد الرسالة كمقروءة:', error);
            });
        }

        // جلب الرسائل الحقيقية
        function fetchRealEmails() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الجلب...';
            btn.disabled = true;

            fetch('/email/api/fetch-real-emails/{{ account.id if account else 1 }}', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('✅ ' + data.message, 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification('❌ فشل في جلب الرسائل: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('خطأ في جلب الرسائل:', error);
                showNotification('❌ حدث خطأ في الاتصال بالخادم', 'error');
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        // عرض الإشعارات بطريقة أفضل
        function showNotification(message, type = 'info') {
            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' : 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'};
                color: white;
                padding: 16px 24px;
                border-radius: 12px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                z-index: 10000;
                font-weight: 600;
                max-width: 400px;
                animation: slideInRight 0.3s ease;
            `;
            notification.textContent = message;

            // إضافة الإشعار للصفحة
            document.body.appendChild(notification);

            // إزالة الإشعار بعد 4 ثوان
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // إضافة CSS للتحريك
        const animationStyle = document.createElement('style');
        animationStyle.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(animationStyle);

        // مزامنة الحساب
        function syncAccount() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            btn.disabled = true;

            fetch('/email/api/sync-account/{{ account.id if account else 1 }}', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ ' + data.message);
                    setTimeout(() => location.reload(), 1000);
                } else {
                    alert('❌ فشل في التحديث: ' + data.message);
                }
            })
            .catch(error => {
                alert('❌ حدث خطأ في التحديث');
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        // دوال التنقل
        function goToDashboard() {
            console.log('🏠 الانتقال للوحة التحكم...');
            window.location.href = '/dashboard';
        }

        function goToCompose() {
            console.log('✏️ الانتقال لإنشاء رسالة...');
            window.location.href = '/email/compose-new';
        }

        // الانتقال لصفحة الإعدادات
        function goToSettings() {
            console.log('🔧 محاولة الانتقال لصفحة الإعدادات...');
            try {
                // محاولة 1: استخدام window.location
                window.location = '/email/settings';
            } catch (error) {
                console.error('خطأ في الانتقال:', error);
                // محاولة 2: استخدام window.open
                window.open('/email/settings', '_self');
            }
        }



        // إغلاق النوافذ عند النقر خارجها
        document.addEventListener('click', function(event) {
            const quickReplyModal = document.getElementById('quickReplyModal');
            const forwardModal = document.getElementById('forwardModal');

            if (event.target === quickReplyModal) {
                closeQuickReply();
            }
            if (event.target === forwardModal) {
                closeForward();
            }
        });

        // إغلاق النوافذ بمفتاح Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeQuickReply();
                closeForward();
            }
        });

        // التنقل بين المجلدات (احتياطي - الآن نستخدم روابط مباشرة)
        function navigateToFolder(folderType) {
            console.log('📁 التنقل إلى المجلد:', folderType);

            if (folderType === 'inbox') {
                window.location.href = '/email/inbox';
            } else {
                window.location.href = `/email/folder/${folderType}`;
            }
        }

        // تشخيص الروابط عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const folderLinks = document.querySelectorAll('.folder-item a');
            console.log('📁 عدد روابط المجلدات:', folderLinks.length);

            folderLinks.forEach((link, index) => {
                console.log(`📁 رابط ${index + 1}:`, link.href);

                // إضافة مستمع للنقر للتشخيص
                link.addEventListener('click', function(e) {
                    console.log('🖱️ تم النقر على المجلد:', this.href);
                });
            });
        });

        // جلب المرفقات من الخادم
        function loadAttachments(emailId) {
            if (!emailId) {
                console.error('❌ معرف الرسالة غير موجود');
                return;
            }

            console.log('📎 جلب مرفقات الرسالة:', emailId);

            fetch(`/email/attachments/${emailId}`)
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📋 بيانات المرفقات:', data);
                    if (data.success && data.attachments) {
                        console.log(`✅ تم جلب ${data.attachments.length} مرفق`);
                        displayAttachments(data.attachments);
                    } else {
                        console.warn('⚠️ لا توجد مرفقات أو خطأ:', data.message);
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في جلب المرفقات:', error);
                });
        }

        // عرض المرفقات
        function displayAttachments(attachments) {
            const attachmentsList = document.getElementById('attachmentsList');
            attachmentsList.innerHTML = '';

            attachments.forEach(attachment => {
                const attachmentDiv = document.createElement('div');
                attachmentDiv.className = 'attachment-item';

                // تحديد أيقونة المرفق حسب النوع
                let iconClass = 'default';
                let iconSymbol = '📄';

                if (attachment.content_type) {
                    if (attachment.content_type.includes('pdf')) {
                        iconClass = 'pdf';
                        iconSymbol = '📄';
                    } else if (attachment.content_type.includes('word') || attachment.content_type.includes('document')) {
                        iconClass = 'doc';
                        iconSymbol = '📝';
                    } else if (attachment.content_type.includes('image')) {
                        iconClass = 'image';
                        iconSymbol = '🖼️';
                    } else if (attachment.content_type.includes('excel') || attachment.content_type.includes('spreadsheet')) {
                        iconClass = 'excel';
                        iconSymbol = '📊';
                    } else if (attachment.content_type.includes('zip') || attachment.content_type.includes('archive')) {
                        iconClass = 'zip';
                        iconSymbol = '🗜️';
                    }
                }

                attachmentDiv.innerHTML = `
                    <div class="attachment-icon ${iconClass}">
                        ${iconSymbol}
                    </div>
                    <div class="attachment-info">
                        <div class="attachment-name">${attachment.original_filename}</div>
                        <div class="attachment-size">${attachment.size_formatted || formatFileSize(attachment.file_size)}</div>
                    </div>
                    <button class="attachment-download" onclick="downloadAttachment(${attachment.id})">
                        <i class="fas fa-download"></i> تحميل
                    </button>
                `;

                attachmentsList.appendChild(attachmentDiv);
            });
        }

        // تحميل مرفق
        function downloadAttachment(attachmentId) {
            console.log('📎 محاولة تحميل المرفق:', attachmentId);

            // إنشاء رابط مؤقت للتحميل
            const downloadUrl = `/email/download_attachment/${attachmentId}`;
            console.log('🔗 رابط التحميل:', downloadUrl);

            // إنشاء عنصر a مخفي للتحميل
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = ''; // سيتم تحديد الاسم من الخادم
            link.style.display = 'none';

            // إضافة الرابط للصفحة وتفعيله
            document.body.appendChild(link);
            link.click();

            // إزالة الرابط بعد ثانية
            setTimeout(() => {
                document.body.removeChild(link);
            }, 1000);

            console.log('✅ تم تفعيل التحميل');
        }

        // تنسيق حجم الملف
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // ترتيب الرسائل
        function sortEmails(sortType) {
            console.log('🔄 ترتيب الرسائل حسب:', sortType);

            const emailList = document.getElementById('emailList');
            const emails = Array.from(emailList.querySelectorAll('.email-item'));

            emails.sort((a, b) => {
                switch (sortType) {
                    case 'date_desc':
                        const dateA = new Date(a.getAttribute('data-date') || '1970-01-01');
                        const dateB = new Date(b.getAttribute('data-date') || '1970-01-01');
                        return dateB - dateA;

                    case 'date_asc':
                        const dateA2 = new Date(a.getAttribute('data-date') || '1970-01-01');
                        const dateB2 = new Date(b.getAttribute('data-date') || '1970-01-01');
                        return dateA2 - dateB2;

                    case 'important_first':
                        const importantA = a.classList.contains('important') ? 1 : 0;
                        const importantB = b.classList.contains('important') ? 1 : 0;
                        if (importantB !== importantA) return importantB - importantA;
                        const dateA3 = new Date(a.getAttribute('data-date') || '1970-01-01');
                        const dateB3 = new Date(b.getAttribute('data-date') || '1970-01-01');
                        return dateB3 - dateA3;

                    case 'unread_first':
                        const unreadA = a.classList.contains('unread') ? 1 : 0;
                        const unreadB = b.classList.contains('unread') ? 1 : 0;
                        if (unreadB !== unreadA) return unreadB - unreadA;
                        const dateA4 = new Date(a.getAttribute('data-date') || '1970-01-01');
                        const dateB4 = new Date(b.getAttribute('data-date') || '1970-01-01');
                        return dateB4 - dateA4;

                    default:
                        return 0;
                }
            });

            // إعادة ترتيب العناصر
            emails.forEach(email => emailList.appendChild(email));

            console.log('✅ تم ترتيب الرسائل');
        }

        // ترتيب الرسائل
        function sortEmails(sortType) {
            console.log('🔄 ترتيب الرسائل حسب:', sortType);

            const emailList = document.getElementById('emailList');
            const emails = Array.from(emailList.querySelectorAll('.email-item'));

            emails.sort((a, b) => {
                switch (sortType) {
                    case 'date_desc':
                        // التاريخ (الأحدث أولاً)
                        const dateA = new Date(a.getAttribute('data-date') || '1970-01-01');
                        const dateB = new Date(b.getAttribute('data-date') || '1970-01-01');
                        return dateB - dateA;

                    case 'date_asc':
                        // التاريخ (الأقدم أولاً)
                        const dateA2 = new Date(a.getAttribute('data-date') || '1970-01-01');
                        const dateB2 = new Date(b.getAttribute('data-date') || '1970-01-01');
                        return dateA2 - dateB2;

                    case 'important_first':
                        // المهمة أولاً
                        const importantA = a.classList.contains('important') ? 1 : 0;
                        const importantB = b.classList.contains('important') ? 1 : 0;
                        if (importantB !== importantA) return importantB - importantA;
                        // ثم حسب التاريخ
                        const dateA3 = new Date(a.getAttribute('data-date') || '1970-01-01');
                        const dateB3 = new Date(b.getAttribute('data-date') || '1970-01-01');
                        return dateB3 - dateA3;

                    case 'unread_first':
                        // غير المقروءة أولاً
                        const unreadA = a.classList.contains('unread') ? 1 : 0;
                        const unreadB = b.classList.contains('unread') ? 1 : 0;
                        if (unreadB !== unreadA) return unreadB - unreadA;
                        // ثم حسب التاريخ
                        const dateA4 = new Date(a.getAttribute('data-date') || '1970-01-01');
                        const dateB4 = new Date(b.getAttribute('data-date') || '1970-01-01');
                        return dateB4 - dateA4;

                    case 'sender_asc':
                        // المرسل (أ-ي)
                        const senderA = a.getAttribute('data-sender') || '';
                        const senderB = b.getAttribute('data-sender') || '';
                        return senderA.localeCompare(senderB, 'ar');

                    case 'sender_desc':
                        // المرسل (ي-أ)
                        const senderA2 = a.getAttribute('data-sender') || '';
                        const senderB2 = b.getAttribute('data-sender') || '';
                        return senderB2.localeCompare(senderA2, 'ar');

                    case 'subject_asc':
                        // الموضوع (أ-ي)
                        const subjectA = a.getAttribute('data-subject') || '';
                        const subjectB = b.getAttribute('data-subject') || '';
                        return subjectA.localeCompare(subjectB, 'ar');

                    case 'attachments_first':
                        // المرفقات أولاً
                        const attachA = a.getAttribute('data-has-attachments') === 'true' ? 1 : 0;
                        const attachB = b.getAttribute('data-has-attachments') === 'true' ? 1 : 0;
                        if (attachB !== attachA) return attachB - attachA;
                        // ثم حسب التاريخ
                        const dateA5 = new Date(a.getAttribute('data-date') || '1970-01-01');
                        const dateB5 = new Date(b.getAttribute('data-date') || '1970-01-01');
                        return dateB5 - dateA5;

                    default:
                        return 0;
                }
            });

            // إعادة ترتيب العناصر في DOM
            emails.forEach(email => emailList.appendChild(email));

            // حفظ تفضيل الترتيب
            localStorage.setItem('emailSortPreference', sortType);

            console.log('✅ تم ترتيب الرسائل');
        }

        // استعادة تفضيل الترتيب عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const savedSort = localStorage.getItem('emailSortPreference');
            if (savedSort) {
                const sortSelect = document.getElementById('sortSelect');
                if (sortSelect) {
                    sortSelect.value = savedSort;
                    sortEmails(savedSort);
                }
            }
        });
    </script>
</body>
</html>
