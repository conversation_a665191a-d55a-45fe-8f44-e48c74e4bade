"""
آلية تنفيذ الحوالات المحسنة
Enhanced Transfer Execution Workflow
"""

from database_manager import DatabaseManager
import logging
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class TransferExecutionManager:
    def __init__(self):
        self.db = DatabaseManager()
    
    def execute_transfer_with_suppliers(self, request_id, execution_data):
        """
        تنفيذ الحوالة مع موردين متعددين
        Execute transfer with multiple suppliers
        
        execution_data = {
            'reference': 'REF123456',
            'execution_date': '2025-01-15 10:30:00',
            'execution_method': 'mixed',
            'notes': 'ملاحظات عامة',
            'suppliers': [
                {
                    'supplier_id': 100,
                    'amount': 5000.00,
                    'exchange_rate': 1.0000,
                    'commission': 50.00,
                    'reference': 'SUP-REF-001',
                    'notes': 'ملاحظات المورد الأول'
                },
                {
                    'supplier_id': 101,
                    'amount': 6500.00,
                    'exchange_rate': 1.0000,
                    'commission': 65.00,
                    'reference': 'SUP-REF-002',
                    'notes': 'ملاحظات المورد الثاني'
                }
            ]
        }
        """
        
        try:
            # 1. التحقق من صحة البيانات
            validation_result = self._validate_execution_data(request_id, execution_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message']
                }
            
            # 2. بدء المعاملة
            self.db.begin_transaction()
            
            # 3. إنشاء سجل التحويل الرئيسي
            transfer_id = self._create_main_transfer(request_id, execution_data)
            
            # 4. إنشاء سجلات الموردين
            supplier_results = self._create_supplier_executions(transfer_id, execution_data['suppliers'])
            
            # 5. تحديث حالة الطلب
            self._update_request_status(request_id, 'executed')
            
            # 6. تأكيد المعاملة
            self.db.commit_transaction()
            
            return {
                'success': True,
                'message': 'تم تنفيذ الحوالة بنجاح',
                'transfer_id': transfer_id,
                'suppliers_count': len(execution_data['suppliers']),
                'total_amount': sum(s['amount'] for s in execution_data['suppliers'])
            }
            
        except Exception as e:
            # 7. التراجع في حالة الخطأ
            self.db.rollback_transaction()
            logger.error(f"خطأ في تنفيذ الحوالة {request_id}: {str(e)}")
            return {
                'success': False,
                'message': f'فشل في تنفيذ الحوالة: {str(e)}'
            }
    
    def _validate_execution_data(self, request_id, execution_data):
        """التحقق من صحة بيانات التنفيذ"""
        
        # التحقق من وجود الطلب
        request_query = "SELECT amount, currency, status FROM transfer_requests WHERE id = :1"
        request_result = self.db.execute_query(request_query, [request_id])
        
        if not request_result:
            return {'valid': False, 'message': 'الطلب غير موجود'}
        
        request_data = request_result[0]
        if request_data[2] != 'approved':
            return {'valid': False, 'message': 'الطلب غير معتمد'}
        
        # التحقق من مطابقة المبالغ
        original_amount = float(request_data[0])
        total_distribution = sum(float(s['amount']) for s in execution_data['suppliers'])
        
        if abs(original_amount - total_distribution) > 0.01:
            return {
                'valid': False, 
                'message': f'إجمالي التوزيع ({total_distribution}) لا يطابق المبلغ الأصلي ({original_amount})'
            }
        
        # التحقق من وجود الموردين
        for supplier in execution_data['suppliers']:
            supplier_query = "SELECT name, type FROM money_changers_banks WHERE id = :1 AND is_active = 1"
            supplier_result = self.db.execute_query(supplier_query, [supplier['supplier_id']])
            
            if not supplier_result:
                return {'valid': False, 'message': f'المورد رقم {supplier["supplier_id"]} غير موجود أو غير نشط'}
        
        return {'valid': True, 'message': 'البيانات صحيحة'}
    
    def _create_main_transfer(self, request_id, execution_data):
        """إنشاء سجل التحويل الرئيسي"""
        
        # جلب بيانات الطلب
        request_query = """
        SELECT tr.beneficiary_id, tr.amount, tr.currency, tr.purpose,
               b.beneficiary_name, b.bank_name, b.bank_account
        FROM transfer_requests tr
        JOIN beneficiaries b ON tr.beneficiary_id = b.id
        WHERE tr.id = :1
        """
        request_result = self.db.execute_query(request_query, [request_id])
        request_data = request_result[0]
        
        # إنشاء رقم التحويل
        transfer_number = f"TF{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # إدراج سجل التحويل
        insert_query = """
        INSERT INTO transfers (
            transfer_number, request_id, beneficiary_id, amount, currency,
            execution_reference, execution_date, execution_method, execution_notes,
            total_suppliers, execution_status, executed_by
        ) VALUES (
            :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, 'pending', :11
        ) RETURNING id INTO :12
        """
        
        transfer_id = self.db.execute_insert_returning(insert_query, [
            transfer_number, request_id, request_data[0], request_data[1], request_data[2],
            execution_data['reference'], execution_data['execution_date'], 
            execution_data['execution_method'], execution_data['notes'],
            len(execution_data['suppliers']), 1  # current_user_id
        ])
        
        return transfer_id
    
    def _create_supplier_executions(self, transfer_id, suppliers):
        """إنشاء سجلات تنفيذ الموردين"""
        
        results = []
        for i, supplier in enumerate(suppliers, 1):
            # جلب معلومات المورد للأرشفة
            supplier_query = "SELECT name, type FROM money_changers_banks WHERE id = :1"
            supplier_result = self.db.execute_query(supplier_query, [supplier['supplier_id']])
            supplier_data = supplier_result[0]
            
            # إدراج سجل المورد
            insert_query = """
            INSERT INTO transfer_execution_suppliers (
                transfer_id, supplier_id, supplier_name, supplier_type,
                amount, currency, exchange_rate, commission,
                supplier_reference, supplier_notes, execution_order,
                status, created_by
            ) VALUES (
                :1, :2, :3, :4, :5, 'USD', :6, :7, :8, :9, :10, 'completed', :11
            ) RETURNING id INTO :12
            """
            
            supplier_exec_id = self.db.execute_insert_returning(insert_query, [
                transfer_id, supplier['supplier_id'], supplier_data[0], supplier_data[1],
                supplier['amount'], supplier['exchange_rate'], supplier['commission'],
                supplier['reference'], supplier['notes'], i, 1  # current_user_id
            ])
            
            results.append(supplier_exec_id)
        
        return results
    
    def _update_request_status(self, request_id, status):
        """تحديث حالة الطلب"""
        update_query = """
        UPDATE transfer_requests 
        SET status = :1, updated_at = CURRENT_TIMESTAMP 
        WHERE id = :2
        """
        self.db.execute_query(update_query, [status, request_id])
    
    def get_execution_details(self, transfer_id):
        """جلب تفاصيل التنفيذ الكاملة"""
        
        query = """
        SELECT * FROM v_transfer_execution_details 
        WHERE transfer_id = :1
        ORDER BY execution_order
        """
        
        results = self.db.execute_query(query, [transfer_id])
        
        if not results:
            return None
        
        # تنظيم البيانات
        execution_data = {
            'transfer_info': {
                'transfer_id': results[0][0],
                'transfer_number': results[0][1],
                'execution_reference': results[0][2],
                'execution_date': results[0][3],
                'execution_method': results[0][4],
                'execution_notes': results[0][5],
                'total_suppliers': results[0][6],
                'execution_status': results[0][7]
            },
            'request_info': {
                'request_number': results[0][8],
                'original_amount': results[0][9],
                'original_currency': results[0][10]
            },
            'beneficiary_info': {
                'beneficiary_name': results[0][11],
                'bank_name': results[0][12],
                'bank_account': results[0][13],
                'bank_branch': results[0][14],
                'bank_country': results[0][15]
            },
            'suppliers': []
        }
        
        for row in results:
            if row[16]:  # supplier_execution_id
                execution_data['suppliers'].append({
                    'supplier_execution_id': row[16],
                    'supplier_name': row[17],
                    'supplier_type': row[18],
                    'amount': row[19],
                    'exchange_rate': row[20],
                    'commission': row[21],
                    'supplier_reference': row[22],
                    'supplier_notes': row[23],
                    'execution_order': row[24],
                    'status': row[25],
                    'executed_at': row[26]
                })
        
        return execution_data
