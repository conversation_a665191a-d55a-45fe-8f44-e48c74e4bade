# مقارنة التسميات - النظام المحاسبي الموحد
# Naming Conventions Comparison - Unified Accounting System

## 🎯 مشكلة التسميات الطويلة في Oracle

### حدود Oracle:
- **Oracle 11g وأقل**: 30 حرف كحد أقصى
- **Oracle 12c وأحدث**: 128 حرف كحد أقصى

---

## 📋 مقارنة التسميات

### **الأعمدة (Columns):**

| التسمية الأصلية | التسمية المختصرة | الطول | الوصف |
|-----------------|------------------|-------|--------|
| `BASE_CURRENCY_DEBIT` | `BAL` | 3 | رصيد موحد |
| `BASE_CURRENCY_CREDIT` | `BAL_F` | 5 | رصيد بالعملة الأساسية |
| `MONTH_NUMBER` | `MONTH_NO` | 8 | رقم الشهر |
| `YEAR_NUMBER` | `YEAR_NO` | 7 | رقم السنة |
| `BRANCH_ID` | `BRANCH_ID` | 9 | رقم الفرع (بقي كما هو) |

### **الـ Packages:**

| التسمية الأصلية | التسمية المختصرة | الطول | الوصف |
|-----------------|------------------|-------|--------|
| `OPENING_BALANCES_PKG` | `OB_PKG` | 6 | حزمة الأرصدة الافتتاحية |
| `BALANCE_TRANSACTIONS_PKG` | `BT_PKG` | 6 | حزمة ترحيل الأرصدة |

### **الإجراءات (Procedures):**

| التسمية الأصلية | التسمية المختصرة | الطول | الوصف |
|-----------------|------------------|-------|--------|
| `INSERT_OPENING_BALANCE` | `INSERT_BAL` | 10 | إدراج رصيد افتتاحي |
| `UPDATE_OPENING_BALANCE` | `UPDATE_BAL` | 10 | تعديل رصيد افتتاحي |
| `DELETE_OPENING_BALANCE` | `DELETE_BAL` | 10 | حذف رصيد افتتاحي |
| `GET_OPENING_BALANCE` | `GET_BAL` | 7 | الحصول على رصيد |
| `POST_TRANSACTION` | `POST_TXN` | 8 | ترحيل معاملة |
| `REVERSE_TRANSACTION` | `REVERSE_TXN` | 11 | عكس معاملة |
| `GET_CURRENT_BALANCE` | `GET_BAL` | 7 | الحصول على الرصيد الحالي |
| `GET_MONTHLY_BALANCE` | `GET_MONTH_BAL` | 13 | الحصول على رصيد شهري |
| `GET_BALANCE_HISTORY` | `GET_BAL_HIST` | 12 | الحصول على تاريخ الرصيد |

### **المعاملات (Parameters):**

| التسمية الأصلية | التسمية المختصرة | الطول | الوصف |
|-----------------|------------------|-------|--------|
| `p_entity_type_code` | `p_ent_type` | 10 | نوع الكيان |
| `p_entity_id` | `p_ent_id` | 8 | معرف الكيان |
| `p_currency_code` | `p_curr` | 6 | رمز العملة |
| `p_balance_amount` | `p_amount` | 8 | مبلغ الرصيد |
| `p_branch_id` | `p_branch` | 8 | رقم الفرع |
| `p_year_number` | `p_year` | 6 | رقم السنة |
| `p_user_id` | `p_user` | 6 | معرف المستخدم |
| `p_document_type_code` | `p_doc_type` | 10 | نوع المستند |
| `p_document_number` | `p_doc_no` | 8 | رقم المستند |
| `p_document_date` | `p_doc_date` | 10 | تاريخ المستند |
| `p_debit_amount` | `p_dr` | 4 | مبلغ مدين |
| `p_credit_amount` | `p_cr` | 4 | مبلغ دائن |
| `p_exchange_rate` | `p_rate` | 6 | سعر الصرف |
| `p_description` | `p_desc` | 6 | الوصف |

### **الـ Views:**

| التسمية الأصلية | التسمية المختصرة | الطول | الوصف |
|-----------------|------------------|-------|--------|
| `V_CURRENT_BALANCES` | `V_CURR_BAL` | 10 | عرض الأرصدة الحالية |
| `V_MONTHLY_BALANCES` | `V_MONTH_BAL` | 11 | عرض الأرصدة الشهرية |
| `V_ENTITY_SUMMARY` | `V_ENT_SUM` | 9 | ملخص الكيانات |

### **الفهارس (Indexes):**

| التسمية الأصلية | التسمية المختصرة | الطول | الوصف |
|-----------------|------------------|-------|--------|
| `IDX_BT_ENTITY_BALANCE` | `IDX_BT_ENT_BAL` | 14 | فهرس الكيان والرصيد |
| `IDX_BT_PERIOD` | `IDX_BT_PERIOD` | 13 | فهرس الفترة (بقي كما هو) |
| `IDX_BT_BRANCH` | `IDX_BT_BRANCH` | 13 | فهرس الفرع (بقي كما هو) |
| `IDX_BT_DOCUMENT` | `IDX_BT_DOC` | 10 | فهرس المستند |

---

## ✅ **مزايا التسميات المختصرة:**

### **1️⃣ التوافق مع Oracle:**
- جميع الأسماء أقل من 30 حرف
- متوافق مع جميع إصدارات Oracle
- لا مشاكل في الإنشاء أو الاستخدام

### **2️⃣ سهولة الكتابة:**
- أسماء قصيرة وسهلة التذكر
- تقليل الأخطاء الإملائية
- سرعة في الكتابة والتطوير

### **3️⃣ الوضوح:**
- اختصارات منطقية ومفهومة
- `BAL` = Balance
- `TXN` = Transaction
- `ENT` = Entity
- `CURR` = Currency

### **4️⃣ الاتساق:**
- نمط موحد في التسمية
- سهولة التنبؤ بالأسماء
- معايير ثابتة للتطوير

---

## 🎯 **التوصيات:**

### **استخدم التسميات المختصرة للأسباب التالية:**

1. **تجنب مشاكل Oracle** مع الأسماء الطويلة
2. **سهولة التطوير** والصيانة
3. **الأداء المحسن** في الاستعلامات
4. **التوافق المستقبلي** مع جميع الإصدارات
5. **معايير موحدة** للفريق

### **مثال على الاستخدام:**

```sql
-- بدلاً من:
SELECT BALANCE_TRANSACTIONS_PKG.GET_CURRENT_BALANCE(
    p_entity_type_code => 'SUPPLIER',
    p_entity_id => 1,
    p_currency_code => 'USD'
) FROM DUAL;

-- استخدم:
SELECT BT_PKG.GET_BAL(
    p_ent_type => 'SUPPLIER',
    p_ent_id => 1,
    p_curr => 'USD'
) FROM DUAL;
```

---

## 📝 **خلاصة:**

**التسميات المختصرة ضرورية لضمان:**
- ✅ التوافق مع Oracle
- ✅ سهولة التطوير
- ✅ الأداء المحسن
- ✅ الصيانة المبسطة

**النتيجة: نظام محاسبي موحد وفعال بأسماء متوافقة مع Oracle!** 🎯
