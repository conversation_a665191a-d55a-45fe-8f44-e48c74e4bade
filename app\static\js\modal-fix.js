/**
 * إصلاح نهائي ودائم لمشكلة المودالات المظللة
 * Final and permanent fix for grayed-out modal issue
 */

// تنظيف شامل للمودالات
function cleanupAllModals() {
    // إزالة جميع backdrop
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => backdrop.remove());
    
    // إزالة class modal-open من body
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
    
    // إخفاء جميع المودالات
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.display = 'none';
        modal.classList.remove('show');
        modal.setAttribute('aria-hidden', 'true');
        modal.removeAttribute('aria-modal');
        modal.removeAttribute('role');
    });
}

// فتح مودال بطريقة آمنة
function openModalSafe(modalId) {
    // تنظيف أولاً
    cleanupAllModals();
    
    setTimeout(() => {
        const modal = document.getElementById(modalId);
        if (modal) {
            // إعداد المودال
            modal.style.display = 'block';
            modal.classList.add('show');
            modal.setAttribute('aria-hidden', 'false');
            modal.setAttribute('aria-modal', 'true');
            modal.setAttribute('role', 'dialog');
            
            // إعداد body
            document.body.classList.add('modal-open');
            document.body.style.overflow = 'hidden';
            
            // التركيز على أول input
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }
        }
    }, 100);
}

// إغلاق مودال
function closeModalSafe(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
        modal.setAttribute('aria-hidden', 'true');
        modal.removeAttribute('aria-modal');
        modal.removeAttribute('role');
    }
    
    // تنظيف body
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
}

// إعداد مستمعي الأحداث
function setupModalEventListeners() {
    // أزرار فتح المودال
    document.addEventListener('click', function(e) {
        const trigger = e.target.closest('[data-bs-toggle="modal"]');
        if (trigger) {
            e.preventDefault();
            e.stopPropagation();
            
            const targetModal = trigger.getAttribute('data-bs-target');
            if (targetModal) {
                const modalId = targetModal.replace('#', '');
                openModalSafe(modalId);
            }
        }
    });
    
    // أزرار إغلاق المودال
    document.addEventListener('click', function(e) {
        const closeBtn = e.target.closest('[data-bs-dismiss="modal"]');
        if (closeBtn) {
            e.preventDefault();
            e.stopPropagation();
            
            const modal = closeBtn.closest('.modal');
            if (modal) {
                closeModalSafe(modal.id);
            }
        }
    });
    
    // النقر خارج المودال
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal') && e.target.classList.contains('show')) {
            closeModalSafe(e.target.id);
        }
    });
    
    // مفتاح Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModalSafe(openModal.id);
            }
        }
    });
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تنظيف أولي
    cleanupAllModals();
    
    // إعداد مستمعي الأحداث
    setupModalEventListeners();
    
    // إصلاح أي مودالات موجودة
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        // إزالة أي أحداث Bootstrap قديمة
        modal.removeEventListener('show.bs.modal', () => {});
        modal.removeEventListener('shown.bs.modal', () => {});
        modal.removeEventListener('hide.bs.modal', () => {});
        modal.removeEventListener('hidden.bs.modal', () => {});
    });
});

// تصدير الدوال للاستخدام العام
window.openModalSafe = openModalSafe;
window.closeModalSafe = closeModalSafe;
window.cleanupAllModals = cleanupAllModals;
