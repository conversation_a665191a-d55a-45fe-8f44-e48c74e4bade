#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إدارة أسعار الصرف
Exchange Rates Management
"""

from flask import render_template, request, jsonify
from flask_login import login_required, current_user
from . import transfers_bp
from database_manager import DatabaseManager
import logging

logger = logging.getLogger(__name__)

@transfers_bp.route('/exchange-rates')
@login_required
def exchange_rates():
    """صفحة إدارة أسعار الصرف"""
    return render_template('transfers/exchange_rates.html')
