-- =====================================================
-- نظام التكامل بين الموردين والحوالات
-- Supplier-Transfer Integration System
-- =====================================================

-- 1. تعديل جدول طلبات الحوالات لإضافة ربط الموردين
ALTER TABLE TRANSFER_REQUESTS ADD (
    supplier_id NUMBER,
    payment_type VARCHAR2(30) DEFAULT 'REGULAR_TRANSFER', -- SUPPLIER_PAYMENT, REGULAR_TRANSFER
    invoice_reference VARCHAR2(100),
    po_reference VARCHAR2(100),
    supplier_invoice_number VARCHAR2(100),
    payment_due_date DATE,
    discount_amount NUMBER(15,2) DEFAULT 0,
    tax_amount NUMBER(15,2) DEFAULT 0,
    net_payment_amount NUMBER(15,2)
);

-- إضافة المفتاح الخارجي للموردين
ALTER TABLE TRANSFER_REQUESTS ADD CONSTRAINT fk_transfer_requests_supplier 
    FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id);

-- 2. تعديل جدول الحوالات المنفذة لإضافة ربط الموردين
ALTER TABLE TRANSFERS ADD (
    supplier_id NUMBER,
    payment_type VARCHAR2(30),
    supplier_transaction_id NUMBER,
    payment_allocation_status VARCHAR2(20) DEFAULT 'PENDING' -- PENDING, ALLOCATED, COMPLETED
);

-- إضافة المفتاح الخارجي للموردين
ALTER TABLE TRANSFERS ADD CONSTRAINT fk_transfers_supplier 
    FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id);

-- 3. إنشاء جدول ربط مدفوعات الموردين بالحوالات
CREATE TABLE SUPPLIER_PAYMENT_TRANSFERS (
    id NUMBER PRIMARY KEY,
    supplier_id NUMBER NOT NULL,
    transfer_request_id NUMBER,
    transfer_id NUMBER,
    payment_amount NUMBER(15,2) NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    exchange_rate NUMBER(15,6) DEFAULT 1,
    base_currency_amount NUMBER(15,2),
    payment_purpose VARCHAR2(500),
    invoice_numbers CLOB, -- JSON array of invoice numbers
    payment_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, APPROVED, EXECUTED, COMPLETED, CANCELLED
    payment_method VARCHAR2(50), -- BANK_TRANSFER, MONEY_CHANGER, CASH
    
    -- تفاصيل إضافية
    discount_applied NUMBER(15,2) DEFAULT 0,
    tax_withheld NUMBER(15,2) DEFAULT 0,
    commission_charged NUMBER(15,2) DEFAULT 0,
    net_amount_transferred NUMBER(15,2),
    
    -- تواريخ مهمة
    requested_date DATE DEFAULT SYSDATE,
    approved_date DATE,
    executed_date DATE,
    completed_date DATE,
    
    -- معلومات التتبع
    approval_notes CLOB,
    execution_notes CLOB,
    completion_notes CLOB,
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_spt_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id),
    CONSTRAINT fk_spt_request FOREIGN KEY (transfer_request_id) REFERENCES TRANSFER_REQUESTS(id),
    CONSTRAINT fk_spt_transfer FOREIGN KEY (transfer_id) REFERENCES TRANSFERS(id),
    CONSTRAINT fk_spt_currency FOREIGN KEY (currency_code) REFERENCES CURRENCIES(code),
    CONSTRAINT fk_spt_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_spt_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id)
);

-- إنشاء sequence للجدول
CREATE SEQUENCE SUPPLIER_PAYMENT_TRANSFERS_SEQ START WITH 1 INCREMENT BY 1;

-- إنشاء trigger للـ auto increment
CREATE OR REPLACE TRIGGER supplier_payment_transfers_trigger
    BEFORE INSERT ON SUPPLIER_PAYMENT_TRANSFERS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_PAYMENT_TRANSFERS_SEQ.NEXTVAL;
    END IF;
    
    -- حساب المبلغ الصافي
    IF :NEW.net_amount_transferred IS NULL THEN
        :NEW.net_amount_transferred := :NEW.payment_amount - 
                                      NVL(:NEW.discount_applied, 0) - 
                                      NVL(:NEW.tax_withheld, 0) - 
                                      NVL(:NEW.commission_charged, 0);
    END IF;
    
    -- حساب المبلغ بالعملة الأساسية
    IF :NEW.base_currency_amount IS NULL THEN
        :NEW.base_currency_amount := :NEW.payment_amount * NVL(:NEW.exchange_rate, 1);
    END IF;
END;

-- 4. إنشاء جدول تخصيص المدفوعات للفواتير
CREATE TABLE SUPPLIER_PAYMENT_ALLOCATIONS (
    id NUMBER PRIMARY KEY,
    supplier_payment_transfer_id NUMBER NOT NULL,
    supplier_transaction_id NUMBER NOT NULL, -- ربط بجدول SUPPLIER_TRANSACTIONS
    allocated_amount NUMBER(15,2) NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    exchange_rate NUMBER(15,6) DEFAULT 1,
    base_currency_allocated NUMBER(15,2),
    allocation_type VARCHAR2(30) DEFAULT 'INVOICE', -- INVOICE, ADVANCE, CREDIT_NOTE, ADJUSTMENT
    allocation_date DATE DEFAULT SYSDATE,
    allocation_notes VARCHAR2(500),
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_spa_payment_transfer FOREIGN KEY (supplier_payment_transfer_id) 
        REFERENCES SUPPLIER_PAYMENT_TRANSFERS(id),
    CONSTRAINT fk_spa_transaction FOREIGN KEY (supplier_transaction_id) 
        REFERENCES SUPPLIER_TRANSACTIONS(transaction_id),
    CONSTRAINT fk_spa_currency FOREIGN KEY (currency_code) REFERENCES CURRENCIES(code),
    CONSTRAINT fk_spa_created_by FOREIGN KEY (created_by) REFERENCES USERS(id)
);

-- إنشاء sequence للجدول
CREATE SEQUENCE SUPPLIER_PAYMENT_ALLOCATIONS_SEQ START WITH 1 INCREMENT BY 1;

-- إنشاء trigger للـ auto increment
CREATE OR REPLACE TRIGGER supplier_payment_allocations_trigger
    BEFORE INSERT ON SUPPLIER_PAYMENT_ALLOCATIONS
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_PAYMENT_ALLOCATIONS_SEQ.NEXTVAL;
    END IF;
    
    -- حساب المبلغ بالعملة الأساسية
    IF :NEW.base_currency_allocated IS NULL THEN
        :NEW.base_currency_allocated := :NEW.allocated_amount * NVL(:NEW.exchange_rate, 1);
    END IF;
END;

-- 5. إنشاء جدول أرصدة الموردين متعدد العملات (إذا لم يكن موجوداً)
CREATE TABLE SUPPLIER_BALANCES (
    id NUMBER PRIMARY KEY,
    supplier_id NUMBER NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    opening_balance NUMBER(15,2) DEFAULT 0,
    debit_amount NUMBER(15,2) DEFAULT 0,
    credit_amount NUMBER(15,2) DEFAULT 0,
    current_balance NUMBER(15,2) DEFAULT 0,
    credit_limit NUMBER(15,2) DEFAULT 0,
    last_transaction_date DATE,
    last_payment_date DATE,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- إحصائيات إضافية
    total_invoices_count NUMBER DEFAULT 0,
    total_payments_count NUMBER DEFAULT 0,
    average_payment_days NUMBER(5,2) DEFAULT 0,
    
    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية والقيود
    CONSTRAINT fk_sb_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id),
    CONSTRAINT fk_sb_currency FOREIGN KEY (currency_code) REFERENCES CURRENCIES(code),
    CONSTRAINT uk_supplier_currency UNIQUE (supplier_id, currency_code)
);

-- إنشاء sequence للجدول
CREATE SEQUENCE SUPPLIER_BALANCES_SEQ START WITH 1 INCREMENT BY 1;

-- إنشاء trigger للـ auto increment
CREATE OR REPLACE TRIGGER supplier_balances_trigger
    BEFORE INSERT ON SUPPLIER_BALANCES
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_BALANCES_SEQ.NEXTVAL;
    END IF;
    
    -- حساب الرصيد الحالي
    :NEW.current_balance := NVL(:NEW.opening_balance, 0) + 
                           NVL(:NEW.debit_amount, 0) - 
                           NVL(:NEW.credit_amount, 0);
END;

-- 6. إنشاء جدول معاملات الموردين (إذا لم يكن موجوداً)
CREATE TABLE SUPPLIER_TRANSACTIONS (
    transaction_id NUMBER PRIMARY KEY,
    supplier_id NUMBER NOT NULL,
    transaction_type VARCHAR2(30) NOT NULL, -- INVOICE, PAYMENT, CREDIT_NOTE, DEBIT_NOTE, ADJUSTMENT
    reference_type VARCHAR2(30), -- PO, INVOICE, PAYMENT_VOUCHER, TRANSFER_REQUEST, etc.
    reference_id NUMBER,
    reference_number VARCHAR2(50),
    transaction_date DATE DEFAULT SYSDATE,
    due_date DATE,
    currency_code VARCHAR2(3) NOT NULL,
    original_amount NUMBER(15,2) NOT NULL,
    exchange_rate NUMBER(15,6) DEFAULT 1,
    base_currency_amount NUMBER(15,2), -- المبلغ بالعملة الأساسية
    debit_amount NUMBER(15,2) DEFAULT 0,
    credit_amount NUMBER(15,2) DEFAULT 0,
    balance_after_transaction NUMBER(15,2),
    description VARCHAR2(500),
    status VARCHAR2(20) DEFAULT 'ACTIVE', -- ACTIVE, CANCELLED, REVERSED
    
    -- معلومات إضافية
    tax_amount NUMBER(15,2) DEFAULT 0,
    discount_amount NUMBER(15,2) DEFAULT 0,
    payment_terms VARCHAR2(100),
    
    -- بيانات النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER,
    updated_by NUMBER,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_st_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id),
    CONSTRAINT fk_st_currency FOREIGN KEY (currency_code) REFERENCES CURRENCIES(code),
    CONSTRAINT fk_st_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_st_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id)
);

-- إنشاء sequence للجدول
CREATE SEQUENCE SUPPLIER_TRANSACTIONS_SEQ START WITH 1 INCREMENT BY 1;

-- إنشاء trigger للـ auto increment
CREATE OR REPLACE TRIGGER supplier_transactions_trigger
    BEFORE INSERT ON SUPPLIER_TRANSACTIONS
    FOR EACH ROW
BEGIN
    IF :NEW.transaction_id IS NULL THEN
        :NEW.transaction_id := SUPPLIER_TRANSACTIONS_SEQ.NEXTVAL;
    END IF;
    
    -- حساب المبلغ بالعملة الأساسية
    IF :NEW.base_currency_amount IS NULL THEN
        :NEW.base_currency_amount := :NEW.original_amount * NVL(:NEW.exchange_rate, 1);
    END IF;
END;

-- 7. إنشاء فهارس للأداء
CREATE INDEX idx_spt_supplier ON SUPPLIER_PAYMENT_TRANSFERS(supplier_id);
CREATE INDEX idx_spt_request ON SUPPLIER_PAYMENT_TRANSFERS(transfer_request_id);
CREATE INDEX idx_spt_transfer ON SUPPLIER_PAYMENT_TRANSFERS(transfer_id);
CREATE INDEX idx_spt_status ON SUPPLIER_PAYMENT_TRANSFERS(payment_status);
CREATE INDEX idx_spt_currency ON SUPPLIER_PAYMENT_TRANSFERS(currency_code);
CREATE INDEX idx_spt_date ON SUPPLIER_PAYMENT_TRANSFERS(requested_date);

CREATE INDEX idx_spa_payment_transfer ON SUPPLIER_PAYMENT_ALLOCATIONS(supplier_payment_transfer_id);
CREATE INDEX idx_spa_transaction ON SUPPLIER_PAYMENT_ALLOCATIONS(supplier_transaction_id);
CREATE INDEX idx_spa_type ON SUPPLIER_PAYMENT_ALLOCATIONS(allocation_type);

CREATE INDEX idx_sb_supplier ON SUPPLIER_BALANCES(supplier_id);
CREATE INDEX idx_sb_currency ON SUPPLIER_BALANCES(currency_code);
CREATE INDEX idx_sb_balance ON SUPPLIER_BALANCES(current_balance);

CREATE INDEX idx_st_supplier ON SUPPLIER_TRANSACTIONS(supplier_id);
CREATE INDEX idx_st_type ON SUPPLIER_TRANSACTIONS(transaction_type);
CREATE INDEX idx_st_reference ON SUPPLIER_TRANSACTIONS(reference_type, reference_id);
CREATE INDEX idx_st_date ON SUPPLIER_TRANSACTIONS(transaction_date);
CREATE INDEX idx_st_due_date ON SUPPLIER_TRANSACTIONS(due_date);
CREATE INDEX idx_st_status ON SUPPLIER_TRANSACTIONS(status);

-- 8. إنشاء تعليقات للجداول والأعمدة
COMMENT ON TABLE SUPPLIER_PAYMENT_TRANSFERS IS 'جدول ربط مدفوعات الموردين بنظام الحوالات';
COMMENT ON TABLE SUPPLIER_PAYMENT_ALLOCATIONS IS 'جدول تخصيص المدفوعات للفواتير المحددة';
COMMENT ON TABLE SUPPLIER_BALANCES IS 'جدول أرصدة الموردين متعدد العملات';
COMMENT ON TABLE SUPPLIER_TRANSACTIONS IS 'جدول معاملات الموردين التفصيلية';

-- 9. إنشاء جدول سجل تتبع حالات المدفوعات
CREATE TABLE SUPPLIER_PAYMENT_STATUS_LOG (
    id NUMBER PRIMARY KEY,
    payment_id NUMBER NOT NULL,
    old_status VARCHAR2(20),
    new_status VARCHAR2(20) NOT NULL,
    change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by NUMBER,
    notes VARCHAR2(500),

    -- بيانات النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- المفاتيح الخارجية
    CONSTRAINT fk_spsl_payment FOREIGN KEY (payment_id) REFERENCES SUPPLIER_PAYMENT_TRANSFERS(id),
    CONSTRAINT fk_spsl_user FOREIGN KEY (changed_by) REFERENCES USERS(id)
);

-- إنشاء sequence للجدول
CREATE SEQUENCE SUPPLIER_PAYMENT_STATUS_LOG_SEQ START WITH 1 INCREMENT BY 1;

-- إنشاء trigger للـ auto increment
CREATE OR REPLACE TRIGGER supplier_payment_status_log_trigger
    BEFORE INSERT ON SUPPLIER_PAYMENT_STATUS_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := SUPPLIER_PAYMENT_STATUS_LOG_SEQ.NEXTVAL;
    END IF;
END;

-- 10. إنشاء فهارس إضافية للأداء
CREATE INDEX idx_spsl_payment ON SUPPLIER_PAYMENT_STATUS_LOG(payment_id);
CREATE INDEX idx_spsl_status ON SUPPLIER_PAYMENT_STATUS_LOG(new_status);
CREATE INDEX idx_spsl_date ON SUPPLIER_PAYMENT_STATUS_LOG(change_date);

-- 11. إنشاء trigger لتتبع تغييرات حالة المدفوعات تلقائياً
CREATE OR REPLACE TRIGGER supplier_payment_status_change_trigger
    AFTER UPDATE OF payment_status ON SUPPLIER_PAYMENT_TRANSFERS
    FOR EACH ROW
    WHEN (OLD.payment_status != NEW.payment_status)
BEGIN
    INSERT INTO SUPPLIER_PAYMENT_STATUS_LOG (
        payment_id, old_status, new_status, change_date, notes
    ) VALUES (
        :NEW.id, :OLD.payment_status, :NEW.payment_status,
        CURRENT_TIMESTAMP, 'تغيير تلقائي للحالة'
    );
END;

-- تم إنشاء هيكل قاعدة البيانات للتكامل بنجاح
-- Database integration structure created successfully
