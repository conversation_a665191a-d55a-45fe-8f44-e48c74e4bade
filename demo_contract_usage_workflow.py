#!/usr/bin/env python3
"""
عرض توضيحي لسير عمل حالة الاستخدام في العقود
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import get_oracle_manager

def demo_contract_usage_workflow():
    """عرض توضيحي لسير عمل حالة الاستخدام"""
    
    oracle_manager = get_oracle_manager()
    
    try:
        print("🎬 عرض توضيحي: سير عمل حالة الاستخدام في العقود")
        print("=" * 70)
        
        # 1. عرض الحالة الحالية
        print("\n📊 الحالة الحالية للعقود:")
        print("-" * 50)
        
        current_query = """
        SELECT CONTRACT_ID, CONTRACT_NUMBER, SUPPLIER_NAME, IS_USED, CONTRACT_STATUS
        FROM contracts
        ORDER BY CONTRACT_ID
        """
        
        contracts = oracle_manager.execute_query(current_query, [])
        
        if contracts:
            print(f"{'ID':<5} {'رقم العقد':<15} {'المورد':<20} {'حالة الاستخدام':<15}")
            print("-" * 60)
            
            for contract in contracts:
                contract_id = contract[0]
                contract_number = contract[1] or f"عقد-{contract_id}"
                supplier_name = (contract[2][:19] if contract[2] else 'غير محدد')
                is_used = contract[3] or 0
                
                usage_status = "مُستخدم ✅" if is_used == 1 else "غير مُستخدم ❌"
                
                print(f"{contract_id:<5} {contract_number:<15} {supplier_name:<20} {usage_status:<15}")
        
        # 2. شرح السيناريوهات
        print("\n🎯 السيناريوهات المختلفة:")
        print("-" * 50)
        
        print("📋 السيناريو 1: إنشاء أمر شراء جديد")
        print("   1. المستخدم يذهب لصفحة أوامر الشراء")
        print("   2. يضغط على 'إنشاء أمر شراء جديد'")
        print("   3. يختار عقد من القائمة المنسدلة")
        print("   4. يملأ بيانات أمر الشراء ويحفظ")
        print("   5. النظام تلقائياً يحدث IS_USED = 1 للعقد المختار")
        print("   6. العقد يظهر الآن كـ 'مُستخدم' في جدول العقود")
        
        print("\n📋 السيناريو 2: حذف أمر شراء")
        print("   1. إذا تم حذف آخر أمر شراء مرتبط بعقد")
        print("   2. يجب تشغيل script إعادة التحديث")
        print("   3. العقد يعود إلى حالة 'غير مُستخدم'")
        
        print("\n📋 السيناريو 3: عقد بدون أوامر شراء")
        print("   1. العقد الجديد يبدأ بحالة IS_USED = 0")
        print("   2. يظهر كـ 'غير مُستخدم' في الجدول")
        print("   3. متاح للاختيار عند إنشاء أوامر شراء جديدة")
        
        # 3. عرض الكود المسؤول
        print("\n💻 الكود المسؤول عن التحديث:")
        print("-" * 50)
        
        print("📁 الملف: app/purchase_orders/routes.py")
        print("📍 السطر: ~374")
        print("🔧 الكود:")
        print("   if contract_id:")
        print("       update_contract_query = \"UPDATE CONTRACTS SET IS_USED = 1 WHERE CONTRACT_ID = :1\"")
        print("       oracle_manager.execute_update(update_contract_query, [contract_id])")
        
        # 4. فحص أوامر الشراء الموجودة
        print("\n🛒 أوامر الشراء الموجودة:")
        print("-" * 50)
        
        po_query = """
        SELECT COUNT(*) as TOTAL_PO,
               COUNT(CONTRACT_ID) as PO_WITH_CONTRACTS
        FROM PURCHASE_ORDERS
        """
        
        po_stats = oracle_manager.execute_query(po_query, [])
        
        if po_stats:
            total_po = po_stats[0][0]
            po_with_contracts = po_stats[0][1]
            po_without_contracts = total_po - po_with_contracts
            
            print(f"📄 إجمالي أوامر الشراء: {total_po}")
            print(f"🔗 أوامر شراء مرتبطة بعقود: {po_with_contracts}")
            print(f"🔓 أوامر شراء غير مرتبطة بعقود: {po_without_contracts}")
        
        # 5. اختبار عملي
        print("\n🧪 اختبار عملي:")
        print("-" * 50)
        
        print("لاختبار النظام:")
        print("1. اذهب لصفحة أوامر الشراء: https://sas.alfogehi.net:5000/purchase_orders")
        print("2. اضغط على 'إنشاء أمر شراء جديد'")
        print("3. اختر أحد العقود من القائمة (مثل CON000016)")
        print("4. املأ البيانات المطلوبة واحفظ")
        print("5. ارجع لصفحة العقود وحدث الصفحة")
        print("6. ستلاحظ أن العقد المختار أصبح 'مُستخدم'")
        
        # 6. نصائح مهمة
        print("\n💡 نصائح مهمة:")
        print("-" * 50)
        
        print("✅ العقد 'مُستخدم' يعني:")
        print("   - تم ربطه بأمر شراء واحد أو أكثر")
        print("   - لا يمكن حذفه بسهولة")
        print("   - يظهر في تقارير العقود المستخدمة")
        
        print("\n❌ العقد 'غير مُستخدم' يعني:")
        print("   - لم يتم ربطه بأي أمر شراء")
        print("   - يمكن تعديله أو حذفه بحرية")
        print("   - متاح للاختيار في أوامر شراء جديدة")
        
        print("\n🔄 التحديث التلقائي:")
        print("   - يحدث فوراً عند إنشاء أمر شراء")
        print("   - لا يحتاج تدخل يدوي")
        print("   - يضمن دقة البيانات")
        
        # 7. استعلامات مفيدة
        print("\n📝 استعلامات مفيدة:")
        print("-" * 50)
        
        print("🔍 العقود المستخدمة:")
        print("   SELECT * FROM contracts WHERE IS_USED = 1;")
        
        print("\n🔍 العقود غير المستخدمة:")
        print("   SELECT * FROM contracts WHERE IS_USED = 0;")
        
        print("\n🔍 العقود مع أوامر الشراء:")
        print("   SELECT c.*, COUNT(po.ID) as po_count")
        print("   FROM contracts c")
        print("   LEFT JOIN PURCHASE_ORDERS po ON c.CONTRACT_ID = po.CONTRACT_ID")
        print("   GROUP BY c.CONTRACT_ID, c.CONTRACT_NUMBER, c.IS_USED;")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء العرض التوضيحي...")
    success = demo_contract_usage_workflow()
    
    if success:
        print("\n✅ تم الانتهاء من العرض التوضيحي!")
        print("\n🎯 الخلاصة:")
        print("   • حالة الاستخدام تعتمد على وجود أوامر شراء مرتبطة")
        print("   • التحديث يحدث تلقائياً عند إنشاء أوامر شراء")
        print("   • جميع العقود الآن في الحالة الصحيحة")
    else:
        print("\n❌ فشل في العرض التوضيحي")
        sys.exit(1)
