<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح التصدير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- مكتبات التصدير -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-bug me-2"></i>
                    اختبار إصلاح مشكلة التصدير
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>المشكلة:</h6>
                    <p>عند ضغط زر Excel تظل النافذة تدور ولا يتم التصدير</p>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-tools me-2"></i>الحلول المطبقة:</h6>
                    <ul>
                        <li>فحص توفر مكتبات XLSX و jsPDF</li>
                        <li>إضافة معالجة أخطاء محسنة</li>
                        <li>إزالة showLoading/hideLoading المعطلة</li>
                        <li>تبسيط عملية التصدير</li>
                    </ul>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-success w-100 mb-3" onclick="testExcelExport()">
                            <i class="fas fa-file-excel me-2"></i>
                            اختبار تصدير Excel
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-danger w-100 mb-3" onclick="testPDFExport()">
                            <i class="fas fa-file-pdf me-2"></i>
                            اختبار تصدير PDF
                        </button>
                    </div>
                </div>
                
                <div id="testResults"></div>
                
                <!-- جدول تجريبي -->
                <table class="table table-striped mt-4">
                    <thead>
                        <tr>
                            <th>كود الصنف</th>
                            <th>اسم الصنف</th>
                            <th>المورد</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ITEM001</td>
                            <td>حلوى سي سي عملاق</td>
                            <td>شركة رايسن</td>
                            <td>3,000</td>
                            <td>¥115.00</td>
                        </tr>
                        <tr>
                            <td>ITEM002</td>
                            <td>علكة ثلاجة العائلة</td>
                            <td>شركة يونجي</td>
                            <td>2,111</td>
                            <td>¥162.00</td>
                        </tr>
                        <tr>
                            <td>ITEM003</td>
                            <td>علكة بوكر</td>
                            <td>شركة ياهوا</td>
                            <td>2,062</td>
                            <td>¥148.00</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
    // دالة تصدير Excel مبسطة
    function testExcelExport() {
        console.log('🚀 بدء اختبار تصدير Excel...');
        
        // فحص توفر مكتبة XLSX
        if (typeof XLSX === 'undefined') {
            showResult('danger', 'خطأ', 'مكتبة XLSX غير متوفرة');
            return;
        }
        
        try {
            showResult('info', 'جاري التصدير...', 'يرجى الانتظار');
            
            // بيانات تجريبية
            const data = [
                ['كود الصنف', 'اسم الصنف', 'المورد', 'الكمية', 'السعر'],
                ['ITEM001', 'حلوى سي سي عملاق', 'شركة رايسن', 3000, '¥115.00'],
                ['ITEM002', 'علكة ثلاجة العائلة', 'شركة يونجي', 2111, '¥162.00'],
                ['ITEM003', 'علكة بوكر', 'شركة ياهوا', 2062, '¥148.00']
            ];
            
            // إنشاء workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(data);
            
            // تنسيق العرض
            ws['!cols'] = [
                {width: 15}, {width: 25}, {width: 20}, {width: 12}, {width: 15}
            ];
            
            XLSX.utils.book_append_sheet(wb, ws, 'بيانات الاختبار');
            
            // تصدير الملف
            const fileName = `اختبار_Excel_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);
            
            showResult('success', 'نجح التصدير!', `تم إنشاء الملف: ${fileName}`);
            console.log('✅ تم تصدير Excel بنجاح');
            
        } catch (error) {
            showResult('danger', 'فشل التصدير', `خطأ: ${error.message}`);
            console.error('❌ خطأ في تصدير Excel:', error);
        }
    }
    
    // دالة تصدير PDF مبسطة
    function testPDFExport() {
        console.log('🚀 بدء اختبار تصدير PDF...');
        
        // فحص توفر مكتبة jsPDF
        if (typeof window.jspdf === 'undefined') {
            showResult('danger', 'خطأ', 'مكتبة jsPDF غير متوفرة');
            return;
        }
        
        try {
            showResult('info', 'جاري التصدير...', 'يرجى الانتظار');
            
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF('l', 'mm', 'a4');
            
            // العنوان
            doc.setFontSize(16);
            doc.text('اختبار تصدير PDF', 148, 20, { align: 'center' });
            
            // الجدول
            const tableData = [
                ['ITEM001', 'حلوى سي سي عملاق', 'شركة رايسن', '3,000', '¥115.00'],
                ['ITEM002', 'علكة ثلاجة العائلة', 'شركة يونجي', '2,111', '¥162.00'],
                ['ITEM003', 'علكة بوكر', 'شركة ياهوا', '2,062', '¥148.00']
            ];
            
            doc.autoTable({
                head: [['كود الصنف', 'اسم الصنف', 'المورد', 'الكمية', 'السعر']],
                body: tableData,
                startY: 30,
                styles: { fontSize: 10 },
                headStyles: { fillColor: [70, 110, 234] }
            });
            
            // حفظ الملف
            const fileName = `اختبار_PDF_${new Date().toISOString().split('T')[0]}.pdf`;
            doc.save(fileName);
            
            showResult('success', 'نجح التصدير!', `تم إنشاء الملف: ${fileName}`);
            console.log('✅ تم تصدير PDF بنجاح');
            
        } catch (error) {
            showResult('danger', 'فشل التصدير', `خطأ: ${error.message}`);
            console.error('❌ خطأ في تصدير PDF:', error);
        }
    }
    
    function showResult(type, title, message) {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'danger' ? 'alert-danger' : 'alert-info';
        const icon = type === 'success' ? 'fa-check-circle' : 
                    type === 'danger' ? 'fa-exclamation-triangle' : 'fa-info-circle';
        
        $('#testResults').html(`
            <div class="alert ${alertClass}">
                <h6><i class="fas ${icon} me-2"></i>${title}</h6>
                <p class="mb-0">${message}</p>
            </div>
        `);
    }
    
    // فحص المكتبات عند تحميل الصفحة
    $(document).ready(function() {
        console.log('🔍 فحص المكتبات...');
        
        let status = '<div class="alert alert-info"><h6>حالة المكتبات:</h6><ul>';
        
        if (typeof XLSX !== 'undefined') {
            status += '<li class="text-success">✅ مكتبة XLSX متوفرة</li>';
        } else {
            status += '<li class="text-danger">❌ مكتبة XLSX غير متوفرة</li>';
        }
        
        if (typeof window.jspdf !== 'undefined') {
            status += '<li class="text-success">✅ مكتبة jsPDF متوفرة</li>';
        } else {
            status += '<li class="text-danger">❌ مكتبة jsPDF غير متوفرة</li>';
        }
        
        status += '</ul></div>';
        $('#testResults').html(status);
    });
    </script>
</body>
</html>
