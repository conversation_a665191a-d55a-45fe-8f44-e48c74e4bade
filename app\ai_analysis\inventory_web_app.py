"""
تطبيق ويب بسيط لعرض أرصدة المخزون
Simple Web App for Inventory Balances
"""

from flask import Flask, render_template_string, jsonify
import pandas as pd
from datetime import datetime
import os
import glob

app = Flask(__name__)

# قالب HTML بسيط
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏪 أرصدة المخزون</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #2E8B57;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stat-card h3 {
            font-size: 2rem;
            margin: 0 0 10px 0;
        }
        .stat-card p {
            margin: 0;
            opacity: 0.9;
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        th {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .refresh-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        .refresh-btn:hover {
            transform: translateY(-2px);
        }
        .timestamp {
            text-align: center;
            color: #666;
            margin-top: 20px;
            font-style: italic;
        }
        .no-data {
            text-align: center;
            padding: 50px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 نظام أرصدة المخزون</h1>
            <p>تحليل شامل لأرصدة الأصناف من جدول ITEM_MOVEMENT</p>
        </div>
        
        <button class="refresh-btn" onclick="location.reload()">🔄 تحديث البيانات</button>
        
        {% if has_data %}
        <div class="stats-grid">
            <div class="stat-card">
                <h3>{{ total_items }}</h3>
                <p>إجمالي الأصناف</p>
            </div>
            <div class="stat-card">
                <h3>{{ items_with_stock }}</h3>
                <p>أصناف متوفرة</p>
            </div>
            <div class="stat-card">
                <h3>{{ total_value }}</h3>
                <p>إجمالي القيمة</p>
            </div>
            <div class="stat-card">
                <h3>{{ avg_value }}</h3>
                <p>متوسط قيمة الصنف</p>
            </div>
        </div>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>كود الصنف</th>
                        <th>اسم الصنف</th>
                        <th>الرصيد الحالي</th>
                        <th>تكلفة الوحدة</th>
                        <th>إجمالي القيمة</th>
                        <th>عدد الحركات</th>
                        <th>آخر حركة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr>
                        <td>{{ item.i_code }}</td>
                        <td>{{ item.i_desc }}</td>
                        <td>{{ "%.2f"|format(item.running_balance) }}</td>
                        <td>{{ "%.2f"|format(item.unit_cost) }}</td>
                        <td>{{ "%.2f"|format(item.total_value) }}</td>
                        <td>{{ item.movement_count }}</td>
                        <td>{{ item.last_movement }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="no-data">
            <h2>⚠️ لا توجد بيانات للعرض</h2>
            <p>يرجى تشغيل تحليل الأرصدة أولاً باستخدام الأمر:</p>
            <code>python run_inventory_analysis.py</code>
        </div>
        {% endif %}
        
        <div class="timestamp">
            آخر تحديث: {{ timestamp }}
        </div>
    </div>
</body>
</html>
"""

def load_latest_balance_data():
    """تحميل أحدث بيانات الأرصدة"""
    try:
        # البحث عن أحدث ملف Excel
        excel_files = glob.glob("inventory_balances_*.xlsx")
        
        if not excel_files:
            return None
        
        # ترتيب الملفات حسب التاريخ
        excel_files.sort(reverse=True)
        latest_file = excel_files[0]
        
        # قراءة البيانات
        df = pd.read_excel(latest_file, sheet_name='الأرصدة الحالية')
        
        return df
        
    except Exception as e:
        print(f"خطأ في تحميل البيانات: {str(e)}")
        return None

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    
    # تحميل البيانات
    df = load_latest_balance_data()
    
    if df is not None and not df.empty:
        # حساب الإحصائيات
        total_items = len(df)
        items_with_stock = len(df[df['running_balance'] > 0])
        total_value = df['total_value'].sum()
        avg_value = df['total_value'].mean()
        
        # تحضير البيانات للعرض
        items = df.head(50).to_dict('records')  # أول 50 صنف
        
        # تنسيق الأرقام
        for item in items:
            if pd.isna(item.get('i_desc')):
                item['i_desc'] = 'غير محدد'
            if pd.isna(item.get('last_movement')):
                item['last_movement'] = 'غير محدد'
        
        context = {
            'has_data': True,
            'total_items': f"{total_items:,}",
            'items_with_stock': f"{items_with_stock:,}",
            'total_value': f"{total_value:,.2f}",
            'avg_value': f"{avg_value:,.2f}",
            'items': items,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    else:
        context = {
            'has_data': False,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    return render_template_string(HTML_TEMPLATE, **context)

@app.route('/api/balances')
def api_balances():
    """API لإرجاع البيانات كـ JSON"""
    df = load_latest_balance_data()
    
    if df is not None and not df.empty:
        return jsonify({
            'success': True,
            'data': df.to_dict('records'),
            'total_items': len(df),
            'timestamp': datetime.now().isoformat()
        })
    else:
        return jsonify({
            'success': False,
            'message': 'لا توجد بيانات',
            'timestamp': datetime.now().isoformat()
        })

if __name__ == '__main__':
    print("🏪 تشغيل تطبيق أرصدة المخزون...")
    print("🌐 يمكنك الوصول للتطبيق على: http://localhost:8503")
    print("⚠️ تطبيق منفصل عن التطبيق الرئيسي")
    app.run(debug=True, host='0.0.0.0', port=8503)
