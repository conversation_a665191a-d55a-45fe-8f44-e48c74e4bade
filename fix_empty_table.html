<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح الجدول الفارغ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    إصلاح مشكلة الجدول الفارغ
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-bug me-2"></i>المشكلة:</h6>
                    <p>الجدول أصبح فارغاً ولا يعرض البيانات</p>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-tools me-2"></i>خطوات الإصلاح:</h6>
                    <ol>
                        <li>فحص تحميل مكتبة DataTables</li>
                        <li>فحص API البيانات</li>
                        <li>فحص دالة تهيئة الجدول</li>
                        <li>فحص دالة تحميل البيانات</li>
                    </ol>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-primary w-100 mb-3" onclick="checkLibraries()">
                            <i class="fas fa-check me-2"></i>
                            فحص المكتبات
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-success w-100 mb-3" onclick="testAPI()">
                            <i class="fas fa-database me-2"></i>
                            فحص API البيانات
                        </button>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-warning w-100 mb-3" onclick="testTable()">
                            <i class="fas fa-table me-2"></i>
                            اختبار الجدول
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-info w-100 mb-3" onclick="fixTable()">
                            <i class="fas fa-wrench me-2"></i>
                            إصلاح الجدول
                        </button>
                    </div>
                </div>
                
                <div id="results"></div>
                
                <!-- جدول اختبار -->
                <div class="mt-4">
                    <h6>جدول الاختبار:</h6>
                    <div class="table-responsive">
                        <table id="testTable" class="table table-striped">
                            <thead>
                                <tr>
                                    <th>كود الصنف</th>
                                    <th>اسم الصنف</th>
                                    <th>المورد</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملؤها بالبيانات -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    let testTableInstance = null;
    
    function showResult(type, title, message) {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'danger' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        const icon = type === 'success' ? 'fa-check-circle' : 
                    type === 'danger' ? 'fa-exclamation-triangle' : 
                    type === 'warning' ? 'fa-exclamation-circle' : 'fa-info-circle';
        
        $('#results').html(`
            <div class="alert ${alertClass}">
                <h6><i class="fas ${icon} me-2"></i>${title}</h6>
                <div>${message}</div>
            </div>
        `);
    }
    
    function checkLibraries() {
        console.log('🔍 فحص المكتبات...');
        
        let status = '<ul>';
        
        // فحص jQuery
        if (typeof $ !== 'undefined') {
            status += '<li class="text-success">✅ jQuery متوفر (الإصدار: ' + $.fn.jquery + ')</li>';
        } else {
            status += '<li class="text-danger">❌ jQuery غير متوفر</li>';
        }
        
        // فحص DataTables
        if (typeof $.fn.DataTable !== 'undefined') {
            status += '<li class="text-success">✅ DataTables متوفر</li>';
        } else {
            status += '<li class="text-danger">❌ DataTables غير متوفر</li>';
        }
        
        // فحص Bootstrap
        if (typeof bootstrap !== 'undefined') {
            status += '<li class="text-success">✅ Bootstrap متوفر</li>';
        } else {
            status += '<li class="text-warning">⚠️ Bootstrap غير متوفر</li>';
        }
        
        status += '</ul>';
        showResult('info', 'حالة المكتبات', status);
    }
    
    function testAPI() {
        console.log('🔍 فحص API البيانات...');
        showResult('info', 'جاري الفحص...', 'يرجى الانتظار');
        
        fetch('/purchase-orders/api/items/data')
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Data received:', data);
                
                if (data.success && data.data && data.data.length > 0) {
                    showResult('success', 'API يعمل بنجاح!', 
                        `تم جلب ${data.data.length} صنف من قاعدة البيانات<br>
                         عينة: ${data.data[0].item_name || 'غير محدد'}`);
                } else {
                    showResult('warning', 'API يعمل لكن لا توجد بيانات', 
                        data.error || 'لا توجد أصناف في قاعدة البيانات');
                }
            })
            .catch(error => {
                console.error('API Error:', error);
                showResult('danger', 'خطأ في API', 
                    `فشل في الاتصال بـ API: ${error.message}`);
            });
    }
    
    function testTable() {
        console.log('🔍 اختبار الجدول...');
        
        try {
            // تدمير الجدول إذا كان موجوداً
            if (testTableInstance) {
                testTableInstance.destroy();
                testTableInstance = null;
            }
            
            // إنشاء جدول جديد
            testTableInstance = $('#testTable').DataTable({
                destroy: true,
                language: {
                    processing: "جاري المعالجة...",
                    search: "بحث:",
                    lengthMenu: "أظهر _MENU_ مدخلات",
                    info: "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                    emptyTable: "لا توجد بيانات متاحة في الجدول",
                    zeroRecords: "لم يعثر على أية سجلات"
                },
                pageLength: 5
            });
            
            showResult('success', 'تم إنشاء الجدول بنجاح!', 
                'الجدول جاهز لاستقبال البيانات');
                
        } catch (error) {
            console.error('Table Error:', error);
            showResult('danger', 'خطأ في إنشاء الجدول', error.message);
        }
    }
    
    function fixTable() {
        console.log('🔧 إصلاح الجدول...');
        showResult('info', 'جاري الإصلاح...', 'يرجى الانتظار');
        
        // الخطوة 1: فحص المكتبات
        if (typeof $.fn.DataTable === 'undefined') {
            showResult('danger', 'فشل الإصلاح', 'مكتبة DataTables غير متوفرة');
            return;
        }
        
        // الخطوة 2: جلب البيانات من API
        fetch('/purchase-orders/api/items/data')
            .then(response => response.json())
            .then(data => {
                if (!data.success || !data.data || data.data.length === 0) {
                    showResult('warning', 'لا توجد بيانات', 'API يعمل لكن لا توجد أصناف');
                    return;
                }
                
                // الخطوة 3: تدمير الجدول القديم
                if (testTableInstance) {
                    testTableInstance.destroy();
                }
                
                // الخطوة 4: مسح البيانات القديمة
                $('#testTable tbody').empty();
                
                // الخطوة 5: إضافة البيانات الجديدة
                data.data.slice(0, 5).forEach(item => {
                    $('#testTable tbody').append(`
                        <tr>
                            <td>${item.item_code || ''}</td>
                            <td>${item.item_name || ''}</td>
                            <td>${item.supplier_name || 'غير محدد'}</td>
                            <td>${item.total_quantity || 0}</td>
                            <td>¥${(item.avg_price || 0).toFixed(2)}</td>
                        </tr>
                    `);
                });
                
                // الخطوة 6: إعادة تهيئة الجدول
                testTableInstance = $('#testTable').DataTable({
                    destroy: true,
                    language: {
                        processing: "جاري المعالجة...",
                        search: "بحث:",
                        lengthMenu: "أظهر _MENU_ مدخلات",
                        info: "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                        emptyTable: "لا توجد بيانات متاحة في الجدول"
                    },
                    pageLength: 5
                });
                
                showResult('success', 'تم إصلاح الجدول بنجاح!', 
                    `تم تحميل ${data.data.length} صنف في الجدول`);
                    
            })
            .catch(error => {
                showResult('danger', 'فشل الإصلاح', `خطأ: ${error.message}`);
            });
    }
    
    // فحص تلقائي عند تحميل الصفحة
    $(document).ready(function() {
        console.log('🚀 بدء فحص تلقائي...');
        setTimeout(checkLibraries, 1000);
    });
    </script>
</body>
</html>
