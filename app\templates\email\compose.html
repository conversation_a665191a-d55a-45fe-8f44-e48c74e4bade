{% extends "base.html" %}

{% block title %}إنشاء رسالة - البريد الإلكتروني{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/email.css') }}">
<style>
    /* متغيرات CSS للألوان والتأثيرات */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%);
        --bg-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        --glass-bg: rgba(255, 255, 255, 0.25);
        --glass-border: rgba(255, 255, 255, 0.18);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
        --shadow-medium: 0 15px 35px rgba(31, 38, 135, 0.2);
        --shadow-heavy: 0 25px 50px rgba(31, 38, 135, 0.15);
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* خلفية الصفحة */
    body {
        background: var(--bg-gradient);
        min-height: 100vh;
        position: relative;
        overflow-x: hidden;
    }

    /* دوائر الخلفية المتحركة */
    body::before {
        content: '';
        position: fixed;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
        animation: float 20s ease-in-out infinite;
        z-index: -1;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-30px) rotate(120deg); }
        66% { transform: translateY(-60px) rotate(240deg); }
    }

    .compose-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .compose-card {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 24px;
        box-shadow: var(--shadow-light);
        overflow: hidden;
        width: 100%;
        max-width: 1000px;
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(60px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* رأس النافذة */
    .compose-header {
        background: var(--primary-gradient);
        color: white;
        padding: 32px 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        overflow: hidden;
    }

    .compose-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
        z-index: 1;
    }

    .compose-title {
        font-size: 28px;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 16px;
        z-index: 2;
        position: relative;
    }

    .compose-title i {
        font-size: 32px;
        background: rgba(255, 255, 255, 0.2);
        padding: 12px;
        border-radius: 16px;
        backdrop-filter: blur(10px);
    }

    .header-actions {
        display: flex;
        gap: 12px;
        z-index: 2;
        position: relative;
    }

    .header-btn {
        padding: 12px 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .header-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
    }

    /* جسم النافذة */
    .compose-body {
        padding: 40px;
        background: rgba(255, 255, 255, 0.1);
    }

    .form-group {
        margin-bottom: 32px;
        position: relative;
    }

    .form-label {
        font-weight: 700;
        color: #374151;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
    }

    .form-label i {
        color: #667eea;
        font-size: 18px;
    }

    .form-control {
        width: 100%;
        border: 2px solid rgba(255, 255, 255, 0.3);
        background: rgba(255, 255, 255, 0.8);
        border-radius: 16px;
        padding: 16px 20px;
        font-size: 16px;
        transition: var(--transition);
        backdrop-filter: blur(10px);
        box-sizing: border-box;
    }

    .form-control:focus {
        border-color: #667eea;
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        outline: none;
        transform: translateY(-2px);
    }

    .form-control:hover {
        border-color: rgba(102, 126, 234, 0.5);
        background: rgba(255, 255, 255, 0.9);
    }

    /* تصميم علامات المستلمين */
    .recipient-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        min-height: 60px;
        padding: 16px 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        background: rgba(255, 255, 255, 0.8);
        border-radius: 16px;
        backdrop-filter: blur(10px);
        transition: var(--transition);
        align-items: center;
    }

    .recipient-tags:focus-within {
        border-color: #667eea;
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    }

    .recipient-tag {
        background: var(--primary-gradient);
        color: white;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        animation: slideInScale 0.3s ease-out;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    @keyframes slideInScale {
        from {
            opacity: 0;
            transform: scale(0.8);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    .recipient-tag .remove {
        cursor: pointer;
        font-weight: bold;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
    }

    .recipient-tag .remove:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    .recipient-input {
        border: none;
        outline: none;
        flex: 1;
        min-width: 200px;
        padding: 12px;
        background: transparent;
        font-size: 16px;
        color: #374151;
    }

    .recipient-input::placeholder {
        color: #9ca3af;
        font-style: italic;
    }

    /* شريط أدوات المحرر */
    .editor-toolbar {
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-bottom: none;
        border-radius: 16px 16px 0 0;
        padding: 20px 24px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(15px);
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
        align-items: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .toolbar-group {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 0 12px;
        border-right: 2px solid rgba(102, 126, 234, 0.2);
    }

    .toolbar-group:last-child {
        border-right: none;
    }

    .editor-btn {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        padding: 10px 14px;
        cursor: pointer;
        transition: var(--transition);
        font-weight: 600;
        color: #374151;
        display: flex;
        align-items: center;
        gap: 6px;
        backdrop-filter: blur(10px);
    }

    .editor-btn:hover {
        background: rgba(102, 126, 234, 0.1);
        border-color: #667eea;
        color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
    }

    .editor-btn.active {
        background: var(--primary-gradient);
        color: white;
        border-color: transparent;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .editor-btn i {
        font-size: 16px;
    }

    /* محرر النص */
    .email-editor {
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: none;
        border-radius: 0 0 16px 16px;
        min-height: 400px;
        padding: 24px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 16px;
        line-height: 1.8;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(15px);
        transition: var(--transition);
        resize: vertical;
    }

    .email-editor:focus {
        outline: none;
        border-color: #667eea;
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    }

    /* منطقة المرفقات */
    .attachments-area {
        border: 3px dashed rgba(102, 126, 234, 0.3);
        border-radius: 20px;
        padding: 40px 20px;
        text-align: center;
        transition: var(--transition);
        cursor: pointer;
        background: rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
    }

    .attachments-area::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s;
    }

    .attachments-area:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.05);
        transform: translateY(-4px);
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
    }

    .attachments-area:hover::before {
        left: 100%;
    }

    .attachments-icon {
        font-size: 48px;
        color: #667eea;
        margin-bottom: 16px;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    .attachments-text {
        font-size: 18px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
    }

    .attachments-hint {
        font-size: 14px;
        color: #6b7280;
    }

    /* قائمة المرفقات */
    .attachments-list {
        margin-top: 20px;
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .attachment-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 12px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
        transition: var(--transition);
    }

    .attachment-item:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateX(8px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .attachment-info {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .attachment-icon {
        font-size: 24px;
        color: #667eea;
    }

    .attachment-details {
        display: flex;
        flex-direction: column;
    }

    .attachment-name {
        font-weight: 600;
        color: #374151;
    }

    .attachment-size {
        font-size: 12px;
        color: #6b7280;
    }

    .attachment-remove {
        background: var(--danger-gradient);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 8px 12px;
        cursor: pointer;
        transition: var(--transition);
        font-weight: 600;
    }

    .attachment-remove:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
    }

    .attachments-area.dragover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }

    /* أزرار الإجراءات */
    .compose-actions {
        display: flex;
        gap: 16px;
        justify-content: space-between;
        align-items: center;
        padding: 32px 0;
        border-top: 2px solid rgba(255, 255, 255, 0.2);
        margin-top: 40px;
    }

    .actions-left {
        display: flex;
        gap: 12px;
    }

    .actions-right {
        display: flex;
        gap: 12px;
    }

    .btn {
        padding: 16px 32px;
        border: none;
        border-radius: 16px;
        font-weight: 700;
        font-size: 16px;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 10px;
        text-decoration: none;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-send {
        background: var(--primary-gradient);
        color: white;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .btn-send:hover {
        transform: translateY(-4px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    }

    .btn-send:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .btn-draft {
        background: var(--warning-gradient);
        color: white;
        box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
    }

    .btn-draft:hover {
        transform: translateY(-4px);
        box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
    }

    .btn-cancel {
        background: rgba(255, 255, 255, 0.8);
        color: #6b7280;
        border: 2px solid rgba(107, 114, 128, 0.3);
        backdrop-filter: blur(15px);
    }

    .btn-cancel:hover {
        background: rgba(107, 114, 128, 0.1);
        color: #374151;
        transform: translateY(-4px);
        box-shadow: 0 15px 35px rgba(107, 114, 128, 0.2);
    }

    /* التصميم المتجاوب */
    @media (max-width: 768px) {
        .compose-container {
            padding: 20px 10px;
        }

        .compose-header {
            padding: 24px 20px;
            flex-direction: column;
            gap: 16px;
            text-align: center;
        }

        .compose-title {
            font-size: 24px;
        }

        .compose-body {
            padding: 24px 20px;
        }

        .form-control {
            padding: 14px 16px;
            font-size: 16px;
        }

        .editor-toolbar {
            padding: 16px;
            gap: 8px;
        }

        .btn {
            padding: 14px 24px;
            font-size: 14px;
            width: 100%;
            justify-content: center;
        }
    }

.templates-dropdown {
    position: relative;
    display: inline-block;
}

.templates-list {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    min-width: 200px;
    z-index: 1000;
    display: none;
}

.template-item {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f1f3f4;
}

.template-item:hover {
    background: #f8f9fa;
}

.template-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}

{% block content %}
<div class="compose-container">
    <div class="compose-card">
        <!-- رأس الصفحة -->
        <div class="compose-header">
            <h1 class="compose-title">
                <i class="fas fa-feather-alt"></i>
                إنشاء رسالة جديدة
            </h1>
            <div class="header-actions">
                <button class="header-btn" onclick="saveDraft()">
                    <i class="fas fa-save"></i>
                    حفظ كمسودة
                </button>
                <button class="header-btn" onclick="goBack()">
                    <i class="fas fa-arrow-right"></i>
                    العودة
                </button>
            </div>
        </div>
        
        <!-- محتوى الرسالة -->
        <div class="compose-body">
            <form id="composeForm">
                <!-- المستقبلون -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user-friends"></i>
                        إلى:
                    </label>
                    <div class="recipient-tags" id="toRecipients" onclick="focusRecipientInput('to')">
                        <input type="text" class="recipient-input" id="toInput"
                               placeholder="أدخل عناوين البريد الإلكتروني..."
                               onkeydown="handleRecipientInput(event, 'to')"
                               onblur="addRecipient('to')">
                    </div>
                </div>
                
                <!-- نسخة كربونية -->
                <div class="form-group" id="ccGroup" style="display: none;">
                    <label class="form-label">نسخة كربونية:</label>
                    <div class="recipient-tags" id="ccRecipients" onclick="focusRecipientInput('cc')">
                        <input type="text" class="recipient-input" id="ccInput" 
                               placeholder="نسخة كربونية..."
                               onkeydown="handleRecipientInput(event, 'cc')"
                               onblur="addRecipient('cc')">
                    </div>
                </div>
                
                <!-- نسخة كربونية مخفية -->
                <div class="form-group" id="bccGroup" style="display: none;">
                    <label class="form-label">نسخة كربونية مخفية:</label>
                    <div class="recipient-tags" id="bccRecipients" onclick="focusRecipientInput('bcc')">
                        <input type="text" class="recipient-input" id="bccInput" 
                               placeholder="نسخة كربونية مخفية..."
                               onkeydown="handleRecipientInput(event, 'bcc')"
                               onblur="addRecipient('bcc')">
                    </div>
                </div>
                
                <!-- أزرار إضافية -->
                <div class="mb-3">
                    <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="toggleField('cc')">
                        نسخة كربونية
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleField('bcc')">
                        نسخة مخفية
                    </button>
                </div>
                
                <!-- الموضوع -->
                <div class="form-group">
                    <label class="form-label">الموضوع:</label>
                    <input type="text" class="form-control" id="subject" 
                           placeholder="موضوع الرسالة..." required>
                </div>
                
                <!-- شريط أدوات المحرر -->
                <div class="editor-toolbar">
                    <button type="button" class="editor-btn" onclick="formatText('bold')" title="عريض">
                        <i class="fas fa-bold"></i>
                    </button>
                    <button type="button" class="editor-btn" onclick="formatText('italic')" title="مائل">
                        <i class="fas fa-italic"></i>
                    </button>
                    <button type="button" class="editor-btn" onclick="formatText('underline')" title="تحته خط">
                        <i class="fas fa-underline"></i>
                    </button>
                    <div style="border-left: 1px solid #dee2e6; height: 20px; margin: 0 5px;"></div>
                    <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')" title="قائمة">
                        <i class="fas fa-list-ul"></i>
                    </button>
                    <button type="button" class="editor-btn" onclick="formatText('insertOrderedList')" title="قائمة مرقمة">
                        <i class="fas fa-list-ol"></i>
                    </button>
                    <div style="border-left: 1px solid #dee2e6; height: 20px; margin: 0 5px;"></div>
                    <button type="button" class="editor-btn" onclick="insertLink()" title="رابط">
                        <i class="fas fa-link"></i>
                    </button>
                    <button type="button" class="editor-btn" onclick="insertImage()" title="صورة">
                        <i class="fas fa-image"></i>
                    </button>
                    
                    <!-- قوالب -->
                    <div class="templates-dropdown ms-auto">
                        <button type="button" class="editor-btn" onclick="toggleTemplates()">
                            <i class="fas fa-file-code me-1"></i>
                            القوالب
                        </button>
                        <div class="templates-list" id="templatesList">
                            {% for template in templates %}
                            <div class="template-item" onclick="applyTemplate('{{ template.id }}')">
                                {{ template.name }}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                
                <!-- محرر النص -->
                <div class="email-editor" id="emailEditor" contenteditable="true" 
                     placeholder="اكتب رسالتك هنا..."></div>
                
                <!-- المرفقات -->
                <div class="form-group">
                    <label class="form-label">المرفقات:</label>
                    <div class="attachments-area" id="attachmentsArea" 
                         onclick="document.getElementById('fileInput').click()"
                         ondrop="handleDrop(event)" 
                         ondragover="handleDragOver(event)"
                         ondragleave="handleDragLeave(event)">
                        <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">اسحب الملفات هنا أو انقر للاختيار</p>
                        <small class="text-muted">الحد الأقصى: 25 ميجابايت لكل ملف</small>
                    </div>
                    <input type="file" id="fileInput" multiple style="display: none;" onchange="handleFileSelect(event)">
                    <div id="attachmentsList" class="mt-3"></div>
                </div>
                
                <!-- أزرار الإجراءات -->
                <div class="compose-actions">
                    <button type="submit" class="btn-send" id="sendBtn">
                        <i class="fas fa-paper-plane me-2"></i>
                        إرسال الرسالة
                    </button>
                    
                    <button type="button" class="btn btn-outline-secondary" onclick="saveDraft()">
                        <i class="fas fa-save me-1"></i>
                        حفظ كمسودة
                    </button>
                    
                    <button type="button" class="btn btn-outline-secondary" onclick="scheduleEmail()">
                        <i class="fas fa-clock me-1"></i>
                        جدولة الإرسال
                    </button>
                    
                    <div class="ms-auto">
                        <small class="text-muted">
                            <i class="fas fa-save me-1"></i>
                            حفظ تلقائي كل 30 ثانية
                        </small>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let recipients = {
    to: [],
    cc: [],
    bcc: []
};

let attachments = [];
let autoSaveInterval;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // بدء الحفظ التلقائي
    autoSaveInterval = setInterval(saveDraft, 30000);
    
    // تهيئة محرر النص
    initializeEditor();
});

// تهيئة محرر النص
function initializeEditor() {
    const editor = document.getElementById('emailEditor');
    
    // إضافة placeholder
    editor.addEventListener('focus', function() {
        if (this.textContent.trim() === '') {
            this.innerHTML = '';
        }
    });
    
    editor.addEventListener('blur', function() {
        if (this.textContent.trim() === '') {
            this.innerHTML = '<p style="color: #999;">اكتب رسالتك هنا...</p>';
        }
    });
}

// معالجة إدخال المستقبلين
function handleRecipientInput(event, type) {
    if (event.key === 'Enter' || event.key === ',' || event.key === ';') {
        event.preventDefault();
        addRecipient(type);
    } else if (event.key === 'Backspace' && event.target.value === '') {
        removeLastRecipient(type);
    }
}

// إضافة مستقبل
function addRecipient(type) {
    const input = document.getElementById(type + 'Input');
    const email = input.value.trim();
    
    if (email && isValidEmail(email)) {
        recipients[type].push(email);
        updateRecipientsDisplay(type);
        input.value = '';
    }
}

// التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
}

// تحديث عرض المستقبلين
function updateRecipientsDisplay(type) {
    const container = document.getElementById(type + 'Recipients');
    const input = document.getElementById(type + 'Input');
    
    // مسح العرض الحالي
    const tags = container.querySelectorAll('.recipient-tag');
    tags.forEach(tag => tag.remove());
    
    // إضافة المستقبلين
    recipients[type].forEach((email, index) => {
        const tag = document.createElement('div');
        tag.className = 'recipient-tag';
        tag.innerHTML = `
            ${email}
            <span class="remove" onclick="removeRecipient('${type}', ${index})">&times;</span>
        `;
        container.insertBefore(tag, input);
    });
}

// إزالة مستقبل
function removeRecipient(type, index) {
    recipients[type].splice(index, 1);
    updateRecipientsDisplay(type);
}

// إزالة آخر مستقبل
function removeLastRecipient(type) {
    if (recipients[type].length > 0) {
        recipients[type].pop();
        updateRecipientsDisplay(type);
    }
}

// تركيز إدخال المستقبل
function focusRecipientInput(type) {
    document.getElementById(type + 'Input').focus();
}

// تبديل عرض الحقول
function toggleField(type) {
    const group = document.getElementById(type + 'Group');
    group.style.display = group.style.display === 'none' ? 'block' : 'none';
}

// تنسيق النص
function formatText(command) {
    document.execCommand(command, false, null);
    document.getElementById('emailEditor').focus();
}

// إدراج رابط
function insertLink() {
    const url = prompt('أدخل الرابط:');
    if (url) {
        document.execCommand('createLink', false, url);
    }
}

// إدراج صورة
function insertImage() {
    const url = prompt('أدخل رابط الصورة:');
    if (url) {
        document.execCommand('insertImage', false, url);
    }
}

// تبديل قائمة القوالب
function toggleTemplates() {
    const list = document.getElementById('templatesList');
    list.style.display = list.style.display === 'block' ? 'none' : 'block';
}

// تطبيق قالب
function applyTemplate(templateId) {
    // جلب القالب وتطبيقه
    fetch(`/email/api/template/${templateId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('subject').value = data.template.subject_template || '';
                document.getElementById('emailEditor').innerHTML = data.template.body_template || '';
            }
        });
    
    toggleTemplates();
}

// معالجة اختيار الملفات
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    files.forEach(addAttachment);
}

// معالجة السحب والإفلات
function handleDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    
    const area = document.getElementById('attachmentsArea');
    area.classList.remove('dragover');
    
    const files = Array.from(event.dataTransfer.files);
    files.forEach(addAttachment);
}

function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    document.getElementById('attachmentsArea').classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    document.getElementById('attachmentsArea').classList.remove('dragover');
}

// إضافة مرفق
function addAttachment(file) {
    if (file.size > 25 * 1024 * 1024) { // 25MB
        alert('حجم الملف كبير جداً. الحد الأقصى 25 ميجابايت.');
        return;
    }
    
    attachments.push(file);
    updateAttachmentsList();
}

// تحديث قائمة المرفقات
function updateAttachmentsList() {
    const list = document.getElementById('attachmentsList');
    list.innerHTML = '';
    
    attachments.forEach((file, index) => {
        const item = document.createElement('div');
        item.className = 'attachment-item';
        item.innerHTML = `
            <div>
                <i class="fas fa-file me-2"></i>
                ${file.name} (${formatFileSize(file.size)})
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeAttachment(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        list.appendChild(item);
    });
}

// إزالة مرفق
function removeAttachment(index) {
    attachments.splice(index, 1);
    updateAttachmentsList();
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// إرسال الرسالة
document.getElementById('composeForm').addEventListener('submit', function(event) {
    event.preventDefault();
    sendEmail();
});

function sendEmail() {
    const sendBtn = document.getElementById('sendBtn');
    const originalText = sendBtn.innerHTML;
    
    // التحقق من البيانات المطلوبة
    if (recipients.to.length === 0) {
        alert('يرجى إدخال مستقبل واحد على الأقل');
        return;
    }
    
    const subject = document.getElementById('subject').value.trim();
    if (!subject) {
        alert('يرجى إدخال موضوع الرسالة');
        return;
    }
    
    const body = document.getElementById('emailEditor').innerHTML;
    if (!body.trim() || body.trim() === '<p style="color: #999;">اكتب رسالتك هنا...</p>') {
        alert('يرجى كتابة محتوى الرسالة');
        return;
    }
    
    // تعطيل الزر وإظهار التحميل
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
    sendBtn.disabled = true;
    
    // إعداد البيانات
    const emailData = {
        to_emails: recipients.to,
        cc_emails: recipients.cc,
        bcc_emails: recipients.bcc,
        subject: subject,
        body: body,
        is_html: true
    };
    
    // إرسال الطلب
    fetch('/email/send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إرسال الرسالة بنجاح!');
            window.location.href = '/email/sent';
        } else {
            alert('فشل في إرسال الرسالة: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الإرسال');
    })
    .finally(() => {
        sendBtn.innerHTML = originalText;
        sendBtn.disabled = false;
    });
}

// حفظ كمسودة
function saveDraft() {
    // تنفيذ حفظ المسودة
    console.log('Saving draft...');
}

// جدولة الإرسال
function scheduleEmail() {
    // تنفيذ جدولة الإرسال
    alert('ميزة جدولة الإرسال ستكون متاحة قريباً');
}

// العودة للخلف
function goBack() {
    if (confirm('هل تريد الخروج؟ سيتم فقدان التغييرات غير المحفوظة.')) {
        window.location.href = '/email/inbox';
    }
}

// تنظيف عند الخروج
window.addEventListener('beforeunload', function() {
    if (autoSaveInterval) {
        clearInterval(autoSaveInterval);
    }
});
</script>
{% endblock %}
