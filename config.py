# -*- coding: utf-8 -*-
"""
إعدادات النظام المحاسبي المتقدم
Advanced Accounting System Configuration
"""

import os
from datetime import timedelta

# استيراد إعدادات Oracle
try:
    from oracle_config import OracleConfig, OracleDevelopmentConfig, OracleTestingConfig, OracleProductionConfig
except ImportError:
    # إذا لم تكن إعدادات Oracle متوفرة، استخدم إعدادات افتراضية
    OracleConfig = None
    OracleDevelopmentConfig = None
    OracleTestingConfig = None
    OracleProductionConfig = None

class Config:
    """الإعدادات الأساسية للنظام"""
    
    # إعدادات قاعدة البيانات - Database Settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'advanced-accounting-system-secret-key-2024'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///accounting_system.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات اللغة والمنطقة - Language and Locale Settings
    LANGUAGES = {
        'ar': 'العربية',
        'en': 'English'
    }
    DEFAULT_LANGUAGE = 'ar'
    BABEL_DEFAULT_LOCALE = 'ar'
    BABEL_DEFAULT_TIMEZONE = 'Asia/Riyadh'

    # إعدادات العملة
    DEFAULT_CURRENCY = 'SAR'
    CURRENCY_SYMBOL = 'ر.س'
    
    # إعدادات الجلسة - Session Settings
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    SESSION_COOKIE_SECURE = False  # False للسماح بـ HTTP في التطوير
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    SESSION_COOKIE_DOMAIN = None  # السماح لجميع النطاقات
    
    # إعدادات الملفات - File Settings
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'xlsx', 'xls', 'csv'}
    
    # إعدادات التقارير - Reports Settings
    REPORTS_FOLDER = 'reports'
    BACKUP_FOLDER = 'backups'
    
    # إعدادات النظام المالي - Financial System Settings
    DEFAULT_CURRENCY = 'SAR'
    CURRENCY_SYMBOL = 'ر.س'
    DECIMAL_PLACES = 2
    
    # إعدادات الإشعارات - Notifications Settings
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # إعدادات الأمان - Security Settings
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600

    # مفتاح تشفير البريد الإلكتروني - Email Encryption Key
    EMAIL_ENCRYPTION_KEY = os.environ.get('EMAIL_ENCRYPTION_KEY') or b'ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg='
    
    # إعدادات الأجهزة المحمولة - Mobile Settings
    COMPRESS_MIMETYPES = [
        'text/html', 'text/css', 'text/xml', 'application/json',
        'application/javascript', 'text/javascript', 'application/xml'
    ]
    COMPRESS_LEVEL = 6
    COMPRESS_MIN_SIZE = 500

    # إعدادات التخزين المؤقت - Caching Settings
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300

    # إعدادات PWA - Progressive Web App
    PWA_MANIFEST = {
        'name': 'النظام المحاسبي المتقدم',
        'short_name': 'المحاسبة',
        'description': 'نظام محاسبي متقدم للأجهزة المحمولة',
        'start_url': '/',
        'display': 'standalone',
        'background_color': '#667eea',
        'theme_color': '#667eea',
        'orientation': 'portrait'
    }

    
    # إعدادات HTTPS - HTTPS Settings
    SSL_CERT_PATH = os.path.join(os.path.dirname(__file__), 'ssl', 'certificate.crt')
    SSL_KEY_PATH = os.path.join(os.path.dirname(__file__), 'ssl', 'private.key')
    FORCE_HTTPS = os.environ.get('FORCE_HTTPS', 'false').lower() == 'true'

    # تحديث إعدادات الجلسة للـ HTTPS
    SESSION_COOKIE_SECURE = False  # False للسماح بـ HTTP في التطوير
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

    # إعدادات الأمان المتقدمة
    PREFERRED_URL_SCHEME = 'https'
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق"""
        pass

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(__file__), 'instance', 'simple_accounting.db')

class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(__file__), 'data.sqlite')
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # تسجيل الأخطاء عبر البريد الإلكتروني
        import logging
        from logging.handlers import SMTPHandler
        if app.config['MAIL_SERVER']:
            auth = None
            if app.config['MAIL_USERNAME'] or app.config['MAIL_PASSWORD']:
                auth = (app.config['MAIL_USERNAME'], app.config['MAIL_PASSWORD'])
            secure = None
            if app.config['MAIL_USE_TLS']:
                secure = ()
            mail_handler = SMTPHandler(
                mailhost=(app.config['MAIL_SERVER'], app.config['MAIL_PORT']),
                fromaddr='no-reply@' + app.config['MAIL_SERVER'],
                toaddrs=[app.config['ADMIN_EMAIL']],
                subject='خطأ في النظام المحاسبي',
                credentials=auth, secure=secure)
            mail_handler.setLevel(logging.ERROR)
            app.logger.addHandler(mail_handler)

# إعدادات Oracle Database
class OracleConfig(Config):
    """إعدادات Oracle Database الأساسية"""

    # إعدادات الاتصال بـ Oracle
    ORACLE_HOST = os.environ.get('ORACLE_HOST') or 'localhost'
    ORACLE_PORT = os.environ.get('ORACLE_PORT') or '1521'
    ORACLE_SID = os.environ.get('ORACLE_SID') or 'ORCL'
    ORACLE_USERNAME = os.environ.get('ORACLE_USERNAME') or 'accounting_user'
    ORACLE_PASSWORD = os.environ.get('ORACLE_PASSWORD') or 'accounting_password'

    # بناء connection string لـ Oracle باستخدام SID
    SQLALCHEMY_DATABASE_URI = (
        f"oracle+cx_oracle://{ORACLE_USERNAME}:{ORACLE_PASSWORD}@"
        f"{ORACLE_HOST}:{ORACLE_PORT}/{ORACLE_SID}"
    )

    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_size': 10,
        'max_overflow': 20,
        'connect_args': {
            'encoding': 'UTF-8',
            'nencoding': 'UTF-8'
        }
    }

# تم نقل إعدادات Oracle إلى oracle_config.py

# جميع إعدادات Oracle متوفرة في oracle_config.py

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig,
}

# إضافة إعدادات Oracle إذا كانت متوفرة
if OracleDevelopmentConfig:
    config.update({
        'oracle_development': OracleDevelopmentConfig,
        'oracle_testing': OracleTestingConfig,
        'oracle_production': OracleProductionConfig,
        'oracle': OracleDevelopmentConfig
    })
