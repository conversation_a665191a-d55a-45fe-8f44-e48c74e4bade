"""
Flask Routes لتقارير الحوالات والأرصدة
Transfer and Balance Reports Routes
"""

from flask import Blueprint, request, jsonify, send_file
from datetime import datetime, date
import logging
import io
import csv
from decimal import Decimal

from app.transfers.reports_service import transfer_reports
from app.utils.auth import login_required
from app.utils.exceptions import ValidationError, DatabaseError

logger = logging.getLogger(__name__)

# إنشاء Blueprint
reports_bp = Blueprint('transfer_reports', __name__, url_prefix='/transfers/reports')

@reports_bp.route('/balances', methods=['GET'])
@login_required
def get_balances_report():
    """
    تقرير الأرصدة الحالية
    
    Query Parameters:
    - entity_type: نوع الكيان (SUPPLIER, MONEY_CHANGER, BANK)
    - currency: العملة
    - include_zero: تضمين الأرصدة الصفرية (true/false)
    - format: تنسيق التقرير (json/csv)
    """
    try:
        entity_type = request.args.get('entity_type')
        currency = request.args.get('currency')
        include_zero = request.args.get('include_zero', 'false').lower() == 'true'
        format_type = request.args.get('format', 'json').lower()
        
        # التحقق من صحة نوع الكيان
        if entity_type and entity_type.upper() not in ['SUPPLIER', 'MONEY_CHANGER', 'BANK']:
            return jsonify({
                'success': False,
                'message': 'نوع الكيان غير صحيح'
            }), 400
        
        # الحصول على التقرير
        report = transfer_reports.get_balances_report(
            entity_type=entity_type,
            currency=currency,
            include_zero_balances=include_zero
        )
        
        if format_type == 'csv':
            return _export_balances_to_csv(report)
        
        return jsonify({
            'success': True,
            'data': report
        }), 200
        
    except Exception as e:
        logger.error(f"خطأ في تقرير الأرصدة: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@reports_bp.route('/transfers-summary', methods=['GET'])
@login_required
def get_transfers_summary_report():
    """
    تقرير ملخص الحوالات
    
    Query Parameters:
    - date_from: تاريخ البداية (YYYY-MM-DD)
    - date_to: تاريخ النهاية (YYYY-MM-DD)
    - status: حالة الحوالة
    - currency: العملة
    - format: تنسيق التقرير (json/csv)
    """
    try:
        date_from_str = request.args.get('date_from')
        date_to_str = request.args.get('date_to')
        status = request.args.get('status')
        currency = request.args.get('currency')
        format_type = request.args.get('format', 'json').lower()
        
        # تحويل التواريخ
        date_from = None
        date_to = None
        
        if date_from_str:
            try:
                date_from = datetime.strptime(date_from_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': 'تنسيق تاريخ البداية غير صحيح (YYYY-MM-DD)'
                }), 400
        
        if date_to_str:
            try:
                date_to = datetime.strptime(date_to_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': 'تنسيق تاريخ النهاية غير صحيح (YYYY-MM-DD)'
                }), 400
        
        # الحصول على التقرير
        report = transfer_reports.get_transfers_summary_report(
            date_from=date_from,
            date_to=date_to,
            status=status,
            currency=currency
        )
        
        if format_type == 'csv':
            return _export_transfers_to_csv(report)
        
        return jsonify({
            'success': True,
            'data': report
        }), 200
        
    except Exception as e:
        logger.error(f"خطأ في تقرير ملخص الحوالات: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@reports_bp.route('/suppliers-activity', methods=['GET'])
@login_required
def get_suppliers_activity_report():
    """
    تقرير نشاط الموردين
    
    Query Parameters:
    - supplier_id: معرف المورد (اختياري)
    - date_from: تاريخ البداية (YYYY-MM-DD)
    - date_to: تاريخ النهاية (YYYY-MM-DD)
    - format: تنسيق التقرير (json/csv)
    """
    try:
        supplier_id = request.args.get('supplier_id', type=int)
        date_from_str = request.args.get('date_from')
        date_to_str = request.args.get('date_to')
        format_type = request.args.get('format', 'json').lower()
        
        # تحويل التواريخ
        date_from = None
        date_to = None
        
        if date_from_str:
            date_from = datetime.strptime(date_from_str, '%Y-%m-%d').date()
        
        if date_to_str:
            date_to = datetime.strptime(date_to_str, '%Y-%m-%d').date()
        
        # الحصول على التقرير
        report = transfer_reports.get_supplier_activity_report(
            supplier_id=supplier_id,
            date_from=date_from,
            date_to=date_to
        )
        
        if format_type == 'csv':
            return _export_suppliers_to_csv(report)
        
        return jsonify({
            'success': True,
            'data': report
        }), 200
        
    except Exception as e:
        logger.error(f"خطأ في تقرير نشاط الموردين: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@reports_bp.route('/money-changers-activity', methods=['GET'])
@login_required
def get_money_changers_activity_report():
    """
    تقرير نشاط الصرافين
    
    Query Parameters:
    - money_changer_id: معرف الصراف (اختياري)
    - date_from: تاريخ البداية (YYYY-MM-DD)
    - date_to: تاريخ النهاية (YYYY-MM-DD)
    - format: تنسيق التقرير (json/csv)
    """
    try:
        money_changer_id = request.args.get('money_changer_id', type=int)
        date_from_str = request.args.get('date_from')
        date_to_str = request.args.get('date_to')
        format_type = request.args.get('format', 'json').lower()
        
        # تحويل التواريخ
        date_from = None
        date_to = None
        
        if date_from_str:
            date_from = datetime.strptime(date_from_str, '%Y-%m-%d').date()
        
        if date_to_str:
            date_to = datetime.strptime(date_to_str, '%Y-%m-%d').date()
        
        # الحصول على التقرير
        report = transfer_reports.get_money_changer_activity_report(
            money_changer_id=money_changer_id,
            date_from=date_from,
            date_to=date_to
        )
        
        if format_type == 'csv':
            return _export_money_changers_to_csv(report)
        
        return jsonify({
            'success': True,
            'data': report
        }), 200
        
    except Exception as e:
        logger.error(f"خطأ في تقرير نشاط الصرافين: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

@reports_bp.route('/daily-summary', methods=['GET'])
@login_required
def get_daily_summary_report():
    """
    تقرير الملخص اليومي
    
    Query Parameters:
    - date: التاريخ (YYYY-MM-DD) - افتراضي: اليوم
    """
    try:
        date_str = request.args.get('date')
        target_date = None
        
        if date_str:
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        # الحصول على التقرير
        report = transfer_reports.get_daily_summary_report(target_date)
        
        return jsonify({
            'success': True,
            'data': report
        }), 200
        
    except Exception as e:
        logger.error(f"خطأ في تقرير الملخص اليومي: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في النظام'
        }), 500

# دوال مساعدة لتصدير CSV
def _export_balances_to_csv(report):
    """تصدير تقرير الأرصدة إلى CSV"""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # كتابة العناوين
    writer.writerow([
        'نوع الكيان', 'اسم الكيان', 'رمز الكيان', 'العملة',
        'الرصيد الافتتاحي', 'إجمالي المدين', 'إجمالي الدائن', 'الرصيد الحالي',
        'عدد المعاملات', 'تاريخ آخر معاملة', 'الحوالات المعلقة', 'الرصيد المتاح'
    ])
    
    # كتابة البيانات
    for balance in report['balances']:
        writer.writerow([
            balance.get('ENTITY_TYPE_CODE', ''),
            balance.get('ENTITY_NAME', ''),
            balance.get('ENTITY_CODE', ''),
            balance.get('CURRENCY_CODE', ''),
            balance.get('OPENING_BALANCE', 0),
            balance.get('DEBIT_AMOUNT', 0),
            balance.get('CREDIT_AMOUNT', 0),
            balance.get('CURRENT_BALANCE', 0),
            balance.get('TOTAL_TRANSACTIONS_COUNT', 0),
            balance.get('LAST_TRANSACTION_DATE', ''),
            balance.get('PENDING_TRANSFERS', 0),
            balance.get('AVAILABLE_BALANCE', 0)
        ])
    
    output.seek(0)
    
    return send_file(
        io.BytesIO(output.getvalue().encode('utf-8-sig')),
        mimetype='text/csv',
        as_attachment=True,
        download_name=f'balances_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    )

def _export_transfers_to_csv(report):
    """تصدير تقرير الحوالات إلى CSV"""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # كتابة العناوين
    writer.writerow([
        'معرف الحوالة', 'رقم الطلب', 'اسم المستفيد', 'البنك',
        'المبلغ', 'العملة', 'الحالة', 'الأولوية',
        'تاريخ الإنشاء', 'تاريخ الاعتماد', 'تاريخ التنفيذ',
        'الصراف', 'عدد الموردين', 'المبلغ الموزع'
    ])
    
    # كتابة البيانات
    for transfer in report['transfers']:
        writer.writerow([
            transfer.get('ID', ''),
            transfer.get('REQUEST_NUMBER', ''),
            transfer.get('BENEFICIARY_NAME', ''),
            transfer.get('BANK_NAME', ''),
            transfer.get('AMOUNT', 0),
            transfer.get('CURRENCY', ''),
            transfer.get('STATUS', ''),
            transfer.get('PRIORITY_LEVEL', ''),
            transfer.get('CREATED_AT', ''),
            transfer.get('APPROVED_AT', ''),
            transfer.get('EXECUTED_AT', ''),
            transfer.get('MONEY_CHANGER_NAME', ''),
            transfer.get('SUPPLIER_DISTRIBUTIONS_COUNT', 0),
            transfer.get('TOTAL_DISTRIBUTED_AMOUNT', 0)
        ])
    
    output.seek(0)
    
    return send_file(
        io.BytesIO(output.getvalue().encode('utf-8-sig')),
        mimetype='text/csv',
        as_attachment=True,
        download_name=f'transfers_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    )

def _export_suppliers_to_csv(report):
    """تصدير تقرير الموردين إلى CSV"""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # كتابة العناوين
    writer.writerow([
        'معرف المورد', 'اسم المورد', 'رمز المورد', 'العملة',
        'عدد الحوالات', 'إجمالي المبلغ', 'متوسط المبلغ',
        'أقل مبلغ', 'أعلى مبلغ', 'أول حوالة', 'آخر حوالة',
        'الرصيد الحالي', 'إجمالي المعاملات'
    ])
    
    # كتابة البيانات
    for supplier in report['suppliers']:
        writer.writerow([
            supplier.get('SUPPLIER_ID', ''),
            supplier.get('SUPPLIER_NAME', ''),
            supplier.get('SUPPLIER_CODE', ''),
            supplier.get('CURRENCY_CODE', ''),
            supplier.get('TRANSFERS_COUNT', 0),
            supplier.get('TOTAL_AMOUNT', 0),
            supplier.get('AVERAGE_AMOUNT', 0),
            supplier.get('MIN_AMOUNT', 0),
            supplier.get('MAX_AMOUNT', 0),
            supplier.get('FIRST_TRANSFER_DATE', ''),
            supplier.get('LAST_TRANSFER_DATE', ''),
            supplier.get('CURRENT_BALANCE', 0),
            supplier.get('TOTAL_TRANSACTIONS', 0)
        ])
    
    output.seek(0)
    
    return send_file(
        io.BytesIO(output.getvalue().encode('utf-8-sig')),
        mimetype='text/csv',
        as_attachment=True,
        download_name=f'suppliers_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    )

def _export_money_changers_to_csv(report):
    """تصدير تقرير الصرافين إلى CSV"""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # كتابة العناوين
    writer.writerow([
        'معرف الصراف', 'اسم الصراف', 'رمز الصراف', 'العملة',
        'عدد الحوالات', 'إجمالي المبلغ', 'متوسط المبلغ',
        'أقل مبلغ', 'أعلى مبلغ', 'أول حوالة', 'آخر حوالة',
        'الرصيد الحالي', 'الحوالات المعلقة', 'الرصيد المتاح'
    ])
    
    # كتابة البيانات
    for mc in report['money_changers']:
        writer.writerow([
            mc.get('MONEY_CHANGER_ID', ''),
            mc.get('MONEY_CHANGER_NAME', ''),
            mc.get('MONEY_CHANGER_CODE', ''),
            mc.get('CURRENCY', ''),
            mc.get('TRANSFERS_COUNT', 0),
            mc.get('TOTAL_AMOUNT', 0),
            mc.get('AVERAGE_AMOUNT', 0),
            mc.get('MIN_AMOUNT', 0),
            mc.get('MAX_AMOUNT', 0),
            mc.get('FIRST_TRANSFER_DATE', ''),
            mc.get('LAST_TRANSFER_DATE', ''),
            mc.get('CURRENT_BALANCE', 0),
            mc.get('PENDING_TRANSFERS', 0),
            mc.get('AVAILABLE_BALANCE', 0)
        ])
    
    output.seek(0)
    
    return send_file(
        io.BytesIO(output.getvalue().encode('utf-8-sig')),
        mimetype='text/csv',
        as_attachment=True,
        download_name=f'money_changers_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    )

# معالج الأخطاء العام
@reports_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': 'التقرير المطلوب غير موجود'
    }), 404

@reports_bp.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'message': 'الطريقة غير مسموحة'
    }), 405
