<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النافذة النهائي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid px-3 mt-4">
        <div class="row">
            <div class="col-12">
                <h2>🧪 اختبار النافذة النهائي</h2>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>اختبار API</h5>
                    </div>
                    <div class="card-body">
                        <button id="testAPI" class="btn btn-primary me-2">اختبار API</button>
                        <button id="clearResults" class="btn btn-warning">مسح النتائج</button>
                        
                        <div id="results" class="mt-3"></div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>جدول البيانات</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="dataTable">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>المستفيد</th>
                                        <th>المبلغ</th>
                                        <th>العملة</th>
                                        <th>الفرع</th>
                                        <th>الصراف</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody id="dataTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center">لا توجد بيانات</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('testAPI').addEventListener('click', async function() {
            const resultsDiv = document.getElementById('results');
            const tableBody = document.getElementById('dataTableBody');
            
            try {
                resultsDiv.innerHTML = '<div class="alert alert-info">جاري التحميل...</div>';
                
                const response = await fetch('/transfers/api/transfer-requests');
                const data = await response.json();
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6>✅ نجح الاختبار!</h6>
                            <p>تم تحميل <strong>${data.count}</strong> طلب بنجاح</p>
                        </div>
                    `;
                    
                    // عرض البيانات في الجدول
                    if (data.data && data.data.length > 0) {
                        tableBody.innerHTML = data.data.map(request => `
                            <tr>
                                <td><strong class="text-primary">${request.request_number}</strong></td>
                                <td>${request.beneficiary_name}</td>
                                <td><strong>${parseFloat(request.amount).toLocaleString('en-US', {minimumFractionDigits: 2})}</strong></td>
                                <td><span class="badge bg-secondary">${request.currency}</span></td>
                                <td>${request.branch_name}</td>
                                <td>${request.money_changer_name}</td>
                                <td><span class="badge bg-warning">${request.status}</span></td>
                            </tr>
                        `).join('');
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد بيانات</td></tr>';
                    }
                } else {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h6>❌ فشل الاختبار!</h6>
                            <p>${data.message || 'خطأ غير معروف'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>❌ خطأ في الشبكة!</h6>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        });
        
        document.getElementById('clearResults').addEventListener('click', function() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('dataTableBody').innerHTML = '<tr><td colspan="7" class="text-center">لا توجد بيانات</td></tr>';
        });
        
        // تشغيل تلقائي
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('testAPI').click();
            }, 1000);
        });
    </script>
</body>
</html>