{% extends "base.html" %}

{% block title %}إدارة العملات{% endblock %}

{% block extra_css %}
<style>
/* نفس تصميم الشحنات */
.currency-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.currency-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.status-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
}

.currency-code {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #007bff;
    font-size: 1.2rem;
    background: rgba(0, 123, 255, 0.1);
    padding: 0.3rem 0.6rem;
    border-radius: 0.25rem;
    border: 1px solid rgba(0, 123, 255, 0.2);
    display: inline-block;
}

.currency-code:hover {
    background: rgba(0, 123, 255, 0.15);
    border-color: rgba(0, 123, 255, 0.3);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

/* تحسين أزرار الإجراءات */
.btn-group-sm .btn {
    margin: 0 1px;
    transition: all 0.2s ease;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    transform: scale(1.05);
}

.btn-outline-danger:hover i {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* تحسين الجدول العام */
.table th {
    font-weight: 600;
    color: #495057;
    border-top: none;
    white-space: nowrap;
    font-size: 0.85rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.85rem;
}

.table .badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
}

.table .text-center {
    text-align: center !important;
}

.table .text-muted {
    color: #6c757d !important;
    font-size: 0.8rem;
}

/* ألوان مخصصة للحالات */
.status-badge.bg-active {
    background-color: #198754 !important;
    color: white;
}

.status-badge.bg-inactive {
    background-color: #6c757d !important;
    color: white;
}

.status-badge.bg-base {
    background-color: #fd7e14 !important;
    color: white;
}

.realtime-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section - نفس تصميم الشحنات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-coins text-primary me-2"></i>
                        نظام إدارة العملات
                    </h1>
                    <p class="text-muted mb-0">نظام متطور لإدارة العملات وأسعار الصرف مع تحكم كامل في النظام المالي</p>
                </div>
                <div>
                    <button class="btn btn-primary btn-lg" onclick="showAddCurrencyModal()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عملة جديدة
                    </button>
                    <a href="{{ url_for('currencies.exchange_rates') }}" class="btn btn-info btn-lg ms-2">
                        <i class="fas fa-exchange-alt me-2"></i>
                        أسعار الصرف
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards - نفس تصميم الشحنات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient rounded-circle p-3">
                                <i class="fas fa-coins text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">إجمالي العملات</h6>
                            <h3 class="mb-0">{{ stats.total_currencies }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-circle p-3">
                                <i class="fas fa-check-circle text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">العملات النشطة</h6>
                            <h3 class="mb-0">{{ stats.active_currencies }}</h3>
                            <span class="realtime-indicator text-success">
                                <i class="fas fa-circle"></i> مباشر
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-circle p-3">
                                <i class="fas fa-star text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">العملة الأساسية</h6>
                            <h3 class="mb-0">
                                {% if stats.base_currency %}
                                    {{ stats.base_currency.code }}
                                {% else %}
                                    --
                                {% endif %}
                            </h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-circle p-3">
                                <i class="fas fa-clock text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">آخر تحديث</h6>
                            <h3 class="mb-0">
                                {% if stats.last_update %}
                                    {{ stats.last_update.created_at.strftime('%d/%m') if stats.last_update.created_at else '--' }}
                                {% else %}
                                    --
                                {% endif %}
                            </h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content - جدول العملات مثل الشحنات -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 text-white">
                            <i class="fas fa-list me-2"></i>
                            قائمة العملات ({{ currencies|length }})
                        </h6>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-light" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if currencies %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="text-center">كود العملة</th>
                                    <th>الاسم</th>
                                    <th class="text-center">الرمز</th>
                                    <th class="text-center">سعر الصرف</th>
                                    <th class="text-center">المنازل</th>
                                    <th class="text-center">الموضع</th>
                                    <th class="text-center">الحالة</th>
                                    <th class="text-center">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for currency in currencies %}
                                <tr>
                                    <td class="text-center">
                                        <span class="currency-code">
                                            {{ currency.code }}
                                            {% if currency.is_base_currency %}
                                                <i class="fas fa-star text-warning ms-1" title="عملة أساسية"></i>
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ currency.name_ar }}</strong>
                                            <br>
                                            <small class="text-muted">{{ currency.name_en }}</small>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-light text-dark border">{{ currency.symbol }}</span>
                                    </td>
                                    <td class="text-center">
                                        {% if currency.is_base_currency %}
                                            <span class="text-warning fw-bold">1.0000 (أساسية)</span>
                                        {% else %}
                                            <span class="text-success fw-bold">{{ "%.4f"|format(currency.exchange_rate) }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info">{{ currency.decimal_places }}</span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-secondary">{{ 'قبل' if currency.position == 'before' else 'بعد' }}</span>
                                    </td>
                                    <td class="text-center">
                                        {% if currency.is_base_currency %}
                                            <span class="status-badge bg-base">أساسية</span>
                                        {% endif %}
                                        <span class="status-badge {% if currency.is_active %}bg-active{% else %}bg-inactive{% endif %}">
                                            {% if currency.is_active %}نشطة{% else %}غير نشطة{% endif %}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-primary" onclick="editCurrency({{ currency.id }})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if not currency.is_base_currency %}
                                                <button type="button" class="btn btn-outline-warning" onclick="setBaseCurrency({{ currency.id }})" title="جعل أساسية">
                                                    <i class="fas fa-star"></i>
                                                </button>
                                            {% endif %}
                                            <button type="button" class="btn btn-outline-{% if currency.is_active %}secondary{% else %}success{% endif %}"
                                                    onclick="toggleCurrencyStatus({{ currency.id }})"
                                                    title="{% if currency.is_active %}إيقاف{% else %}تفعيل{% endif %}">
                                                <i class="fas fa-{% if currency.is_active %}pause{% else %}play{% endif %}"></i>
                                            </button>
                                            {% if not currency.is_base_currency %}
                                                <button type="button" class="btn btn-outline-danger" onclick="deleteCurrency({{ currency.id }})" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-coins fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد عملات مضافة</h5>
                        <p class="text-muted">ابدأ بإضافة العملات التي تحتاجها في نظامك المالي</p>
                        <button class="btn btn-primary" onclick="showAddCurrencyModal()">
                            <i class="fas fa-plus me-2"></i>إضافة أول عملة
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>


</div>
{% endblock %}

{% block extra_js %}
<script>
// وظائف إدارة العملات
function showAddCurrencyModal() {
    // الانتقال لصفحة إضافة عملة
    window.location.href = "{{ url_for('currencies.add_currency') }}";
}

function editCurrency(id) {
    // الانتقال لصفحة تعديل العملة
    window.location.href = `/currencies/edit/${id}`;
}

function setBaseCurrency(id) {
    if (confirm('هل تريد جعل هذه العملة أساسية؟')) {
        fetch(`/currencies/set-base-currency/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('تم تعيين العملة الأساسية بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('خطأ: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('حدث خطأ في العملية', 'error');
        });
    }
}

function toggleCurrencyStatus(id) {
    if (confirm('هل تريد تغيير حالة هذه العملة؟')) {
        fetch(`/currencies/toggle-status/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('تم تغيير حالة العملة بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('خطأ: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('حدث خطأ في العملية', 'error');
        });
    }
}

function deleteCurrency(id) {
    if (confirm('هل تريد حذف هذه العملة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`/currencies/delete/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('تم حذف العملة بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('خطأ: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('حدث خطأ في العملية', 'error');
        });
    }
}

function refreshData() {
    showToast('جاري تحديث البيانات...', 'info');
    location.reload();
}

// وظيفة عرض الرسائل
function showToast(message, type = 'info') {
    // إنشاء عنصر التوست
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة التوست للصفحة
    document.body.appendChild(toast);

    // إزالة التوست بعد 3 ثوان
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>
{% endblock %}
