# مقترح نظام التصميم الموحد للنوافذ
## Unified Design System Proposal for Windows

### التاريخ: 2025-01-15
### الحالة: مؤجل للفترة القادمة
### الهدف: توحيد تصميم وتخطيط النوافذ الجديدة

---

## 📋 **نظرة عامة**

هذا المقترح يهدف إلى إنشاء نظام تصميم موحد للنوافذ الجديدة في النظام، بحيث يمكن إنشاء نوافذ جديدة بنفس التصميم والتخطيط والمظهر دون التأثير على النوافذ الموجودة.

---

## 🎯 **الأهداف الرئيسية**

1. **توحيد التصميم**: نفس المظهر والتخطيط لجميع النوافذ الجديدة
2. **تسريع التطوير**: إنشاء نوافذ جديدة بسرعة أكبر
3. **سهولة الصيانة**: تحديث واحد يؤثر على جميع النوافذ
4. **عدم التأثير على الموجود**: الحفاظ على النوافذ الحالية كما هي

---

## 🏗️ **المكونات الأساسية**

### 1. **قوالب أساسية (Base Templates)**

#### أ) قالب أساسي للنوافذ المالية:
```html
<!-- templates/base_financial_window.html -->
{% extends "base.html" %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/financial-windows.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-container">
        {% block breadcrumb %}{% endblock %}
    </div>

    <!-- Header Section -->
    <div class="dashboard-header">
        {% block header %}{% endblock %}
    </div>

    <!-- Filter Controls -->
    <div class="filter-controls">
        {% block filters %}{% endblock %}
    </div>

    <!-- Statistics Cards -->
    <div class="metrics-container">
        {% block statistics %}{% endblock %}
    </div>

    <!-- Main Content Table -->
    <div class="main-content-container">
        {% block main_table %}{% endblock %}
    </div>

    <!-- Charts Section -->
    <div class="charts-container">
        {% block charts %}{% endblock %}
    </div>
</div>
{% endblock %}
```

#### ب) استخدام القالب في النوافذ الجديدة:
```html
<!-- templates/transfers/new_window.html -->
{% extends "base_financial_window.html" %}

{% block breadcrumb %}
    <!-- مسار التنقل المخصص -->
{% endblock %}

{% block header %}
    <!-- هيدر مخصص -->
{% endblock %}

{% block filters %}
    <!-- فلاتر مخصصة -->
{% endblock %}
```

### 2. **مكتبة مكونات CSS**

#### أ) ملف CSS موحد:
```css
/* static/css/unified-design-system.css */

/* ========== متغيرات التصميم ========== */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    
    --border-radius: 12px;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* ========== مكونات موحدة ========== */
.dashboard-header { /* تصميم موحد للهيدر */ }
.breadcrumb-container { /* تصميم موحد للـ breadcrumb */ }
.filter-controls { /* تصميم موحد للفلاتر */ }
.metrics-container { /* تصميم موحد للإحصائيات */ }
.chart-card { /* تصميم موحد للرسوم البيانية */ }
.data-table-container { /* تصميم موحد للجداول */ }
```

### 3. **مكونات JavaScript قابلة لإعادة الاستخدام**

#### أ) مكتبة JavaScript موحدة:
```javascript
// static/js/unified-components.js

class UnifiedWindow {
    constructor(config) {
        this.config = config;
        this.init();
    }

    init() {
        this.loadCurrencies();
        this.setupFilters();
        this.loadData();
        this.setupCharts();
    }

    // دالة موحدة لتحميل العملات
    async loadCurrencies() {
        // كود موحد لتحميل العملات
    }

    // دالة موحدة للفلاتر
    setupFilters() {
        // كود موحد للفلاتر
    }

    // دالة موحدة للرسوم البيانية
    setupCharts() {
        // كود موحد للرسوم البيانية
    }
}

// استخدام المكون
const balancesWindow = new UnifiedWindow({
    apiEndpoint: '/transfers/api/money-changers-balances',
    chartTypes: ['currency', 'trends'],
    filters: ['currency', 'balanceType', 'search']
});
```

### 4. **مولد النوافذ (Window Generator)**

#### أ) سكريبت Python لإنشاء النوافذ:
```python
# tools/window_generator.py

class WindowGenerator:
    def __init__(self, template_name):
        self.template_name = template_name
        
    def generate_window(self, config):
        """
        إنشاء نافذة جديدة بناءً على التكوين
        """
        window_config = {
            'name': config['name'],
            'module': config['module'],
            'api_endpoint': config['api_endpoint'],
            'breadcrumb_items': config['breadcrumb_items'],
            'filters': config['filters'],
            'statistics_cards': config['statistics_cards'],
            'table_columns': config['table_columns'],
            'charts': config['charts']
        }
        
        # إنشاء ملف HTML
        self.create_html_file(window_config)
        
        # إنشاء ملف Python للـ routes
        self.create_routes_file(window_config)
        
        # إنشاء ملف JavaScript
        self.create_js_file(window_config)

# استخدام المولد
generator = WindowGenerator('financial_window')
generator.generate_window({
    'name': 'suppliers_balances',
    'module': 'suppliers',
    'api_endpoint': '/suppliers/api/balances',
    'breadcrumb_items': [
        {'name': 'الرئيسية', 'url': '/'},
        {'name': 'الموردين', 'url': '/suppliers'},
        {'name': 'أرصدة الموردين', 'active': True}
    ],
    'filters': ['supplier', 'currency', 'balance_type'],
    'statistics_cards': ['total_balance', 'suppliers_count', 'positive_balances'],
    'table_columns': ['supplier_name', 'currency', 'balance', 'last_transaction'],
    'charts': ['currency_distribution', 'balance_trends']
})
```

### 5. **نظام تكوين JSON**

#### أ) ملف تكوين للنافذة:
```json
// config/windows/suppliers_balances.json
{
    "window_id": "suppliers_balances",
    "title": "إدارة أرصدة الموردين",
    "module": "suppliers",
    "layout": {
        "breadcrumb": {
            "enabled": true,
            "items": [
                {"name": "الرئيسية", "url": "/"},
                {"name": "الموردين", "url": "/suppliers"},
                {"name": "أرصدة الموردين", "active": true}
            ]
        },
        "header": {
            "title": "إدارة أرصدة الموردين",
            "subtitle": "تحليلات متقدمة ومراقبة فورية لأرصدة الموردين",
            "icon": "fas fa-chart-line",
            "actions": ["refresh", "export"]
        },
        "filters": [
            {"type": "select", "id": "supplier", "label": "المورد", "api": "/suppliers/api/list"},
            {"type": "select", "id": "currency", "label": "العملة", "api": "/transfers/api/currencies"},
            {"type": "select", "id": "balance_type", "label": "نوع الرصيد", "options": ["positive", "negative", "zero"]}
        ],
        "statistics": [
            {"id": "total_balance", "label": "إجمالي الأرصدة", "icon": "fas fa-wallet", "type": "currency"},
            {"id": "suppliers_count", "label": "عدد الموردين", "icon": "fas fa-users", "type": "number"},
            {"id": "positive_balances", "label": "الأرصدة الموجبة", "icon": "fas fa-arrow-up", "type": "currency"}
        ],
        "table": {
            "api": "/suppliers/api/balances",
            "columns": [
                {"id": "supplier_name", "label": "اسم المورد", "type": "text"},
                {"id": "currency_code", "label": "العملة", "type": "badge"},
                {"id": "current_balance", "label": "الرصيد الجاري", "type": "currency"},
                {"id": "last_transaction_date", "label": "آخر معاملة", "type": "date"}
            ]
        },
        "charts": [
            {"type": "doughnut", "id": "currency_chart", "title": "توزيع الأرصدة حسب العملة"},
            {"type": "line", "id": "trends_chart", "title": "اتجاهات الأرصدة"}
        ]
    }
}
```

#### ب) محرك تفسير التكوين:
```javascript
// static/js/window-engine.js

class WindowEngine {
    constructor(configPath) {
        this.loadConfig(configPath);
    }

    async loadConfig(configPath) {
        const response = await fetch(configPath);
        this.config = await response.json();
        this.render();
    }

    render() {
        this.renderBreadcrumb();
        this.renderHeader();
        this.renderFilters();
        this.renderStatistics();
        this.renderTable();
        this.renderCharts();
    }

    renderFilters() {
        this.config.layout.filters.forEach(filter => {
            if (filter.api) {
                this.loadFilterData(filter);
            }
        });
    }
}

// استخدام المحرك
const window = new WindowEngine('/config/windows/suppliers_balances.json');
```

---

## 🔧 **أدوات التطوير**

### 1. **CLI Tool لإنشاء النوافذ**

```bash
# أداة سطر الأوامر
python tools/create_window.py --name "customers_balances" --module "customers" --template "financial_window"

# أو باستخدام ملف تكوين
python tools/create_window.py --config "config/windows/customers_balances.json"
```

### 2. **نظام الثيمات (Themes)**

```css
/* themes/financial-theme.css */
.theme-financial {
    --primary-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --card-background: white;
    --border-radius: 16px;
}

/* themes/modern-theme.css */
.theme-modern {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-background: rgba(255, 255, 255, 0.95);
    --border-radius: 12px;
}
```

---

## 🛡️ **الطريقة الآمنة للتطبيق**

### **المبدأ الأساسي: عدم التأثير على الموجود**

1. **إنشاء ملفات جديدة منفصلة**
2. **عدم تعديل الملفات الموجودة**
3. **إنشاء routes جديدة للنوافذ الجديدة**
4. **اختبار منفصل للتصميم الجديد**

### **مثال عملي:**

```bash
# الوضع الحالي (يبقى كما هو)
/suppliers/balances  # النافذة القديمة

# الوضع الجديد (إضافة جديدة)
/suppliers/balances-unified  # النافذة الجديدة بالتصميم الموحد
```

---

## 📅 **خطة التطبيق المرحلية**

### **المرحلة 1 (أسبوع 1): التأسيس**
- إنشاء الملفات الأساسية (CSS, JS, Templates)
- استخراج التصميم من نافذة أرصدة الصرافين/البنوك
- إنشاء أول نافذة تجريبية

### **المرحلة 2 (أسبوع 2-3): التطوير**
- إنشاء 2-3 نوافذ جديدة بالتصميم الموحد
- تحسين وتطوير المكونات المشتركة
- اختبار التوافق والأداء

### **المرحلة 3 (أسبوع 4): الأتمتة**
- إنشاء أداة لتوليد النوافذ تلقائياً
- تطوير نظام التكوين JSON
- إنشاء CLI tools

### **المرحلة 4 (حسب الحاجة): التوسع**
- نقل النوافذ القديمة تدريجياً (اختياري)
- إضافة ميزات متقدمة
- تطوير محرر مرئي للنوافذ

---

## ✅ **المزايا المتوقعة**

### **للمطورين:**
- تسريع تطوير النوافذ الجديدة بنسبة 70%
- تقليل الأخطاء والتكرار
- سهولة الصيانة والتحديث

### **للمستخدمين:**
- تجربة موحدة ومألوفة
- أداء أفضل وتحميل أسرع
- واجهة أكثر احترافية

### **للنظام:**
- كود أقل تعقيداً
- سهولة إضافة ميزات جديدة
- مرونة في التخصيص

---

## 🚨 **نقاط مهمة للتذكر**

1. **لا تأثير على النظام الحالي**: جميع النوافذ الموجودة تبقى كما هي
2. **تطبيق تدريجي**: يمكن البدء بنافذة واحدة واختبارها
3. **مرونة في التطبيق**: يمكن تعديل المقترح حسب الحاجة
4. **قابلية الإلغاء**: يمكن التراجع عن التطبيق في أي وقت

---

## 📝 **ملاحظات إضافية**

- هذا المقترح قابل للتطوير والتحسين
- يمكن تطبيقه على أي نوع من النوافذ (ليس فقط المالية)
- يدعم التخصيص والثيمات المختلفة
- متوافق مع التقنيات الحالية في النظام

---

---

## 💻 **أمثلة عملية للتطبيق**

### **مثال 1: إنشاء نافذة أرصدة الموردين**

#### أ) القالب الأساسي الجديد:
```html
<!-- templates/base_financial.html -->
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>{% block title %}{% endblock %}</title>

    <!-- Bootstrap & FontAwesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- التصميم الموحد الجديد -->
    <link href="{{ url_for('static', filename='css/unified-financial-design.css') }}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Header -->
    <div class="financial-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="{% block header_icon %}fas fa-chart-line{% endblock %} ms-3"></i>
                        {% block page_title %}{% endblock %}
                    </h1>
                    <p class="page-subtitle">
                        {% block page_subtitle %}{% endblock %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions">
                        {% block header_actions %}
                        <button class="btn-enterprise btn-secondary-enterprise" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        {% endblock %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Breadcrumb -->
        <div class="financial-breadcrumb">
            {% block breadcrumb %}{% endblock %}
        </div>

        <!-- Filters -->
        <div class="financial-filters">
            {% block filters %}{% endblock %}
        </div>

        <!-- Statistics -->
        <div class="financial-metrics">
            {% block statistics %}{% endblock %}
        </div>

        <!-- Main Content -->
        <div class="financial-table">
            {% block main_content %}{% endblock %}
        </div>

        <!-- Charts -->
        <div class="financial-charts">
            {% block charts %}{% endblock %}
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- JavaScript الموحد الجديد -->
    <script src="{{ url_for('static', filename='js/unified-financial.js') }}"></script>

    {% block scripts %}{% endblock %}
</body>
</html>
```

#### ب) نافذة الموردين الجديدة:
```html
<!-- templates/suppliers/balances_unified.html -->
{% extends "base_financial.html" %}

{% block title %}إدارة أرصدة الموردين{% endblock %}
{% block page_title %}إدارة أرصدة الموردين{% endblock %}
{% block page_subtitle %}تحليلات متقدمة ومراقبة فورية لأرصدة الموردين{% endblock %}
{% block header_icon %}fas fa-users{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-custom">
        <li class="breadcrumb-item">
            <a href="/"><i class="fas fa-home breadcrumb-icon"></i>الرئيسية</a>
        </li>
        <li class="breadcrumb-item">
            <a href="/suppliers"><i class="fas fa-users breadcrumb-icon"></i>الموردين</a>
        </li>
        <li class="breadcrumb-item active">
            <i class="fas fa-chart-line breadcrumb-icon"></i>أرصدة الموردين
        </li>
    </ol>
</nav>
{% endblock %}

{% block filters %}
<div class="filter-row">
    <div class="filter-group">
        <label for="supplierFilter">المورد</label>
        <select class="form-select" id="supplierFilter">
            <option value="">جميع الموردين</option>
        </select>
    </div>
    <div class="filter-group">
        <label for="currencyFilter">العملة</label>
        <select class="form-select" id="currencyFilter">
            <option value="">جميع العملات</option>
        </select>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// استخدام الكلاس الموحد
const suppliersWindow = new FinancialWindow({
    apiEndpoint: '/suppliers/api/balances',
    module: 'suppliers'
});

$(document).ready(function() {
    suppliersWindow.loadCurrencies();
    suppliersWindow.loadData();
});
</script>
{% endblock %}
```

#### ج) JavaScript الموحد:
```javascript
// static/js/unified-financial.js

class FinancialWindow {
    constructor(config) {
        this.config = config;
        this.data = {};
        this.charts = {};
    }

    // دالة موحدة لتحميل العملات (مأخوذة من نافذة أرصدة الصرافين)
    async loadCurrencies() {
        try {
            const currencyFilter = $('#currencyFilter');
            currencyFilter.html('<option value="">جاري تحميل العملات...</option>');

            const response = await fetch('/transfers/api/currencies');

            if (response.ok) {
                const result = await response.json();
                if (result.success && result.data) {
                    console.log(`✅ تم تحميل ${result.data.length} عملة بنجاح`);
                    this.populateCurrencyFilter(result.data);
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل العملات:', error);
        }
    }

    // دالة موحدة لملء العملات
    populateCurrencyFilter(currencies) {
        const currencyFilter = $('#currencyFilter');
        currencyFilter.html('<option value="">جميع العملات</option>');

        const sortedCurrencies = currencies.sort((a, b) => {
            if (a.is_base_currency && !b.is_base_currency) return -1;
            if (!a.is_base_currency && b.is_base_currency) return 1;
            return a.name_ar.localeCompare(b.name_ar, 'ar');
        });

        sortedCurrencies.forEach(currency => {
            const baseCurrencyIndicator = currency.is_base_currency ? ' ⭐' : '';
            const symbol = currency.symbol ? ` ${currency.symbol}` : '';
            const optionText = `${currency.name_ar} (${currency.code})${symbol}${baseCurrencyIndicator}`;
            currencyFilter.append(`<option value="${currency.code}">${optionText}</option>`);
        });
    }

    // دالة موحدة لتحميل البيانات
    async loadData() {
        try {
            const response = await fetch(this.config.apiEndpoint);
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.data = result;
                    this.updateUI();
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
        }
    }

    // دالة موحدة لتحديث الواجهة
    updateUI() {
        this.updateStatistics();
        this.updateTable();
        this.updateCharts();
    }
}

// متغير عام للاستخدام
window.FinancialWindow = FinancialWindow;
```

#### د) Route جديد:
```python
# suppliers/routes.py - إضافة route جديد فقط

@suppliers_bp.route('/balances-unified')
@login_required
def balances_unified():
    """نافذة أرصدة الموردين بالتصميم الموحد الجديد"""
    return render_template('suppliers/balances_unified.html')
```

### **مثال 2: أداة إنشاء النوافذ**

```python
# tools/window_creator.py

import os
import json
from jinja2 import Template

class WindowCreator:
    def __init__(self):
        self.base_path = os.path.dirname(os.path.dirname(__file__))

    def create_window(self, config_file):
        """إنشاء نافذة جديدة من ملف التكوين"""

        # قراءة التكوين
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # إنشاء ملف HTML
        self.create_html_template(config)

        # إنشاء Route
        self.create_route(config)

        # إنشاء JavaScript مخصص (إذا لزم الأمر)
        if config.get('custom_js'):
            self.create_custom_js(config)

    def create_html_template(self, config):
        """إنشاء قالب HTML"""

        template_content = """
{% extends "base_financial.html" %}

{% block title %}{{ config.title }}{% endblock %}
{% block page_title %}{{ config.title }}{% endblock %}
{% block page_subtitle %}{{ config.subtitle }}{% endblock %}
{% block header_icon %}{{ config.icon }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-custom">
        {% for item in config.breadcrumb %}
        <li class="breadcrumb-item{% if item.active %} active{% endif %}">
            {% if not item.active %}
            <a href="{{ item.url }}">
                <i class="{{ item.icon }} breadcrumb-icon"></i>{{ item.name }}
            </a>
            {% else %}
            <i class="{{ item.icon }} breadcrumb-icon"></i>{{ item.name }}
            {% endif %}
        </li>
        {% endfor %}
    </ol>
</nav>
{% endblock %}

{% block filters %}
<div class="filter-row">
    {% for filter in config.filters %}
    <div class="filter-group">
        <label for="{{ filter.id }}">{{ filter.label }}</label>
        <select class="form-select" id="{{ filter.id }}">
            <option value="">{{ filter.placeholder }}</option>
        </select>
    </div>
    {% endfor %}
</div>
{% endblock %}

{% block scripts %}
<script>
const {{ config.js_var_name }} = new FinancialWindow({
    apiEndpoint: '{{ config.api_endpoint }}',
    module: '{{ config.module }}'
});

$(document).ready(function() {
    {{ config.js_var_name }}.loadCurrencies();
    {{ config.js_var_name }}.loadData();
});
</script>
{% endblock %}
        """

        template = Template(template_content)
        rendered = template.render(config=config)

        # حفظ الملف
        file_path = os.path.join(
            self.base_path,
            'app', 'templates',
            config['module'],
            f"{config['name']}_unified.html"
        )

        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(rendered)

        print(f"✅ تم إنشاء قالب HTML: {file_path}")

# استخدام الأداة
creator = WindowCreator()
creator.create_window('config/windows/customers_balances.json')
```

---

## 🔄 **سير العمل المقترح**

### **للنوافذ الجديدة:**
1. إنشاء ملف تكوين JSON
2. تشغيل أداة إنشاء النوافذ
3. تخصيص التفاصيل حسب الحاجة
4. اختبار النافذة
5. نشر النافذة

### **للنوافذ الموجودة (اختياري):**
1. إنشاء نسخة جديدة بالتصميم الموحد
2. اختبار النسخة الجديدة
3. مقارنة الأداء والوظائف
4. اتخاذ قرار الانتقال أو البقاء

---

## 📊 **مقاييس النجاح**

### **مقاييس التطوير:**
- تقليل وقت إنشاء النوافذ الجديدة بنسبة 70%
- تقليل أسطر الكود المكررة بنسبة 80%
- تحسين سرعة الصيانة بنسبة 60%

### **مقاييس الأداء:**
- تحسين سرعة التحميل بنسبة 30%
- تقليل حجم الملفات بنسبة 40%
- تحسين تجربة المستخدم

### **مقاييس الجودة:**
- توحيد 100% من النوافذ الجديدة
- تقليل الأخطاء البصرية بنسبة 90%
- تحسين إمكانية الوصول

---

**تاريخ الإنشاء:** 2025-01-15
**آخر تحديث:** 2025-01-15
**الحالة:** مؤجل للفترة القادمة
**المطور:** Augment Agent

---

## 📞 **للمراجعة والتطبيق**

عند الرغبة في تطبيق هذا المقترح، يرجى مراجعة:
1. هذا الملف للتفاصيل الكاملة
2. نافذة أرصدة الصرافين/البنوك كمرجع للتصميم
3. الملفات الموجودة لضمان عدم التعارض
4. متطلبات الأداء والأمان

**ملاحظة:** هذا المقترح قابل للتعديل والتطوير حسب احتياجات المشروع.
