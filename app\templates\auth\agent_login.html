{% extends "base.html" %}

{% block title %}تسجيل دخول المخلصين - النظام المحاسبي المتقدم{% endblock %}

{% block extra_css %}
<style>
.agent-login-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.agent-login-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    max-width: 450px;
    width: 100%;
    text-align: center;
}

.agent-login-header {
    margin-bottom: 30px;
}

.agent-login-header h2 {
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.agent-login-header p {
    color: #666;
    margin-bottom: 0;
}

.agent-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
}

.form-group {
    margin-bottom: 25px;
    text-align: right;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    text-align: right;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.btn-agent-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-agent-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.login-links {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.login-links a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.login-links a:hover {
    text-decoration: underline;
}

.agent-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    text-align: right;
}

.agent-info h6 {
    color: #333;
    margin-bottom: 15px;
    font-weight: 600;
}

.agent-info ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.agent-info li {
    color: #666;
    margin-bottom: 8px;
    font-size: 14px;
}

.agent-info li i {
    color: #667eea;
    margin-left: 8px;
    width: 16px;
}

.demo-credentials {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: right;
}

.demo-credentials h6 {
    color: #1976d2;
    margin-bottom: 10px;
    font-size: 14px;
}

.demo-credentials p {
    margin: 5px 0;
    font-size: 13px;
    color: #1565c0;
}
</style>
{% endblock %}

{% block content %}
<div class="agent-login-container">
    <div class="agent-login-card">
        <div class="agent-login-header">
            <div class="agent-icon">
                <i class="fas fa-user-tie"></i>
            </div>
            <h2>بوابة المخلص الإلكترونية</h2>
            <p>تسجيل دخول المخلصين الجمركيين</p>
        </div>

        <!-- معلومات تجريبية -->
        <div class="demo-credentials">
            <h6><i class="fas fa-info-circle"></i> بيانات تجريبية للاختبار</h6>
            <p><strong>رمز المخلص:</strong> CA001, CA002, أو CA004</p>
            <p><strong>كلمة المرور:</strong> agent</p>
        </div>

        <form method="POST">
            <div class="form-group">
                <label for="agent_code">رمز المخلص</label>
                <input type="text" 
                       class="form-control" 
                       id="agent_code" 
                       name="agent_code" 
                       placeholder="أدخل رمز المخلص (مثل: CA001)"
                       required>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <input type="password" 
                       class="form-control" 
                       id="password" 
                       name="password" 
                       placeholder="أدخل كلمة المرور"
                       required>
            </div>

            <button type="submit" class="btn-agent-login">
                <i class="fas fa-sign-in-alt me-2"></i>
                تسجيل الدخول
            </button>
        </form>

        <div class="agent-info">
            <h6><i class="fas fa-shield-alt"></i> مميزات بوابة المخلص</h6>
            <ul>
                <li><i class="fas fa-tasks"></i> إدارة أوامر التسليم المخصصة</li>
                <li><i class="fas fa-upload"></i> رفع الوثائق والمستندات</li>
                <li><i class="fas fa-chart-line"></i> متابعة الأداء والإحصائيات</li>
                <li><i class="fas fa-bell"></i> استقبال الإشعارات الفورية</li>
                <li><i class="fas fa-mobile-alt"></i> واجهة متجاوبة لجميع الأجهزة</li>
            </ul>
        </div>

        <div class="login-links">
            <p>
                <a href="{{ url_for('auth.login') }}">
                    <i class="fas fa-arrow-right me-1"></i>
                    تسجيل دخول الموظفين
                </a>
            </p>
            <p class="mt-2">
                <a href="{{ url_for('main.index') }}">
                    <i class="fas fa-home me-1"></i>
                    العودة للصفحة الرئيسية
                </a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تركيز تلقائي على حقل رمز المخلص
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('agent_code').focus();
});

// تحسين تجربة المستخدم
document.getElementById('agent_code').addEventListener('input', function() {
    this.value = this.value.toUpperCase();
});

// معالجة الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const agentCode = document.getElementById('agent_code').value.trim();
    const password = document.getElementById('password').value.trim();
    
    if (!agentCode || !password) {
        e.preventDefault();
        alert('يرجى إدخال رمز المخلص وكلمة المرور');
        return;
    }
    
    // إظهار مؤشر التحميل
    const submitBtn = document.querySelector('.btn-agent-login');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحقق...';
    submitBtn.disabled = true;
    
    // إعادة تفعيل الزر بعد 3 ثوان في حالة عدم نجاح التسجيل
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
});
</script>
{% endblock %}
