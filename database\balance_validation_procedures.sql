-- =====================================================
-- إجراءات التحقق من الأرصدة
-- Balance Validation Procedures
-- =====================================================

-- 1. إجراء التحقق من كفاية رصيد الصراف
CREATE OR REPLACE FUNCTION CHECK_MONEY_CHANGER_BALANCE(
    p_money_changer_id IN NUMBER,
    p_required_amount IN NUMBER,
    p_currency_code IN VARCHAR2 DEFAULT 'SAR'
) RETURN VARCHAR2 AS
    v_current_balance NUMBER := 0;
    v_available_balance NUMBER := 0;
    v_pending_transfers NUMBER := 0;
    v_result VARCHAR2(4000);
BEGIN
    -- الحصول على الرصيد الحالي
    BEGIN
        SELECT NVL(current_balance, 0) INTO v_current_balance
        FROM CURRENT_BALANCES
        WHERE entity_type_code = 'MONEY_CHANGER'
        AND entity_id = p_money_changer_id
        AND currency_code = p_currency_code;
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN 'ERROR: لا يوجد رصيد للصراف في هذه العملة';
    END;
    
    -- حساب الحوالات المعلقة (المعتمدة وغير المنفذة)
    SELECT NVL(SUM(amount), 0) INTO v_pending_transfers
    FROM transfers
    WHERE money_changer_id = p_money_changer_id
    AND currency = p_currency_code
    AND status = 'approved';
    
    -- حساب الرصيد المتاح
    v_available_balance := v_current_balance - v_pending_transfers;
    
    -- تحديد النتيجة
    IF v_available_balance >= p_required_amount THEN
        v_result := 'OK: الرصيد كافي';
    ELSIF v_current_balance >= p_required_amount THEN
        v_result := 'WARNING: الرصيد كافي ولكن توجد حوالات معلقة';
    ELSE
        v_result := 'ERROR: الرصيد غير كافي';
    END IF;
    
    -- إضافة التفاصيل
    v_result := v_result || '|CURRENT:' || v_current_balance || 
                '|AVAILABLE:' || v_available_balance || 
                '|REQUIRED:' || p_required_amount ||
                '|PENDING:' || v_pending_transfers;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: ' || SQLERRM;
END;
/

-- 2. إجراء التحقق من صحة توزيعات الموردين
CREATE OR REPLACE FUNCTION VALIDATE_SUPPLIER_DISTRIBUTIONS(
    p_transfer_id IN NUMBER,
    p_distributions_json IN CLOB
) RETURN VARCHAR2 AS
    v_json_array JSON_ARRAY_T;
    v_json_obj JSON_OBJECT_T;
    v_supplier_id NUMBER;
    v_supplier_amount NUMBER;
    v_total_distributed NUMBER := 0;
    v_transfer_amount NUMBER := 0;
    v_currency VARCHAR2(10);
    v_supplier_exists NUMBER;
    v_errors CLOB := '';
    v_warnings CLOB := '';
    v_result VARCHAR2(4000);
BEGIN
    -- الحصول على معلومات الحوالة
    BEGIN
        SELECT amount, currency INTO v_transfer_amount, v_currency
        FROM transfers WHERE id = p_transfer_id;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN 'ERROR: الحوالة غير موجودة';
    END;
    
    -- تحليل JSON
    BEGIN
        v_json_array := JSON_ARRAY_T.parse(p_distributions_json);
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 'ERROR: تنسيق JSON غير صحيح';
    END;
    
    -- التحقق من كل توزيع
    FOR i IN 0..v_json_array.get_size-1 LOOP
        v_json_obj := JSON_OBJECT_T(v_json_array.get(i));
        
        -- استخراج البيانات
        BEGIN
            v_supplier_id := v_json_obj.get_Number('supplier_id');
            v_supplier_amount := v_json_obj.get_Number('amount');
        EXCEPTION
            WHEN OTHERS THEN
                v_errors := v_errors || 'بيانات التوزيع رقم ' || (i + 1) || ' غير صحيحة;';
                CONTINUE;
        END;
        
        -- التحقق من صحة البيانات
        IF v_supplier_id IS NULL OR v_supplier_id <= 0 THEN
            v_errors := v_errors || 'معرف المورد غير صحيح في التوزيع رقم ' || (i + 1) || ';';
            CONTINUE;
        END IF;
        
        IF v_supplier_amount IS NULL OR v_supplier_amount <= 0 THEN
            v_errors := v_errors || 'مبلغ المورد غير صحيح في التوزيع رقم ' || (i + 1) || ';';
            CONTINUE;
        END IF;
        
        -- التحقق من وجود المورد
        SELECT COUNT(*) INTO v_supplier_exists
        FROM suppliers WHERE id = v_supplier_id;
        
        IF v_supplier_exists = 0 THEN
            v_errors := v_errors || 'المورد رقم ' || v_supplier_id || ' غير موجود;';
            CONTINUE;
        END IF;
        
        v_total_distributed := v_total_distributed + v_supplier_amount;
    END LOOP;
    
    -- التحقق من تطابق المبالغ
    IF ABS(v_total_distributed - v_transfer_amount) > 0.01 THEN
        v_errors := v_errors || 'مجموع التوزيعات (' || v_total_distributed || 
                   ') لا يطابق مبلغ الحوالة (' || v_transfer_amount || ');';
    ELSIF ABS(v_total_distributed - v_transfer_amount) > 0.001 THEN
        v_warnings := v_warnings || 'فرق بسيط في المبالغ: ' || 
                     ABS(v_total_distributed - v_transfer_amount) || ';';
    END IF;
    
    -- إعداد النتيجة
    IF LENGTH(v_errors) > 0 THEN
        v_result := 'ERROR: ' || v_errors;
    ELSIF LENGTH(v_warnings) > 0 THEN
        v_result := 'WARNING: ' || v_warnings;
    ELSE
        v_result := 'OK: التوزيعات صحيحة';
    END IF;
    
    v_result := v_result || '|TOTAL:' || v_total_distributed || 
                '|EXPECTED:' || v_transfer_amount || 
                '|COUNT:' || v_json_array.get_size;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: ' || SQLERRM;
END;
/

-- 3. إجراء التحقق الشامل قبل تنفيذ الحوالة
CREATE OR REPLACE FUNCTION VALIDATE_TRANSFER_EXECUTION(
    p_transfer_id IN NUMBER,
    p_money_changer_id IN NUMBER,
    p_total_amount IN NUMBER,
    p_currency_code IN VARCHAR2,
    p_distributions_json IN CLOB
) RETURN VARCHAR2 AS
    v_transfer_status VARCHAR2(50);
    v_balance_check VARCHAR2(4000);
    v_distributions_check VARCHAR2(4000);
    v_errors CLOB := '';
    v_warnings CLOB := '';
    v_result VARCHAR2(4000);
BEGIN
    -- 1. التحقق من حالة الحوالة
    BEGIN
        SELECT status INTO v_transfer_status
        FROM transfers WHERE id = p_transfer_id;
        
        IF v_transfer_status != 'approved' THEN
            v_errors := v_errors || 'الحوالة غير معتمدة (الحالة: ' || v_transfer_status || ');';
        END IF;
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            v_errors := v_errors || 'الحوالة غير موجودة;';
    END;
    
    -- 2. التحقق من رصيد الصراف
    v_balance_check := CHECK_MONEY_CHANGER_BALANCE(
        p_money_changer_id, p_total_amount, p_currency_code
    );
    
    IF v_balance_check LIKE 'ERROR:%' THEN
        v_errors := v_errors || SUBSTR(v_balance_check, 7) || ';';
    ELSIF v_balance_check LIKE 'WARNING:%' THEN
        v_warnings := v_warnings || SUBSTR(v_balance_check, 9) || ';';
    END IF;
    
    -- 3. التحقق من توزيعات الموردين
    v_distributions_check := VALIDATE_SUPPLIER_DISTRIBUTIONS(
        p_transfer_id, p_distributions_json
    );
    
    IF v_distributions_check LIKE 'ERROR:%' THEN
        v_errors := v_errors || SUBSTR(v_distributions_check, 7) || ';';
    ELSIF v_distributions_check LIKE 'WARNING:%' THEN
        v_warnings := v_warnings || SUBSTR(v_distributions_check, 9) || ';';
    END IF;
    
    -- إعداد النتيجة النهائية
    IF LENGTH(v_errors) > 0 THEN
        v_result := 'ERROR: ' || v_errors;
    ELSIF LENGTH(v_warnings) > 0 THEN
        v_result := 'WARNING: ' || v_warnings;
    ELSE
        v_result := 'OK: جميع التحققات نجحت';
    END IF;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: خطأ في التحقق الشامل: ' || SQLERRM;
END;
/

-- 4. إجراء للحصول على ملخص الأرصدة
CREATE OR REPLACE PROCEDURE GET_BALANCES_SUMMARY(
    p_entity_type IN VARCHAR2 DEFAULT NULL,
    p_currency_code IN VARCHAR2 DEFAULT NULL,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_cursor FOR
    SELECT 
        cb.entity_type_code,
        cb.entity_id,
        CASE cb.entity_type_code
            WHEN 'SUPPLIER' THEN s.name
            WHEN 'MONEY_CHANGER' THEN mc.name
            WHEN 'BANK' THEN b.name
        END as entity_name,
        cb.currency_code,
        cb.current_balance,
        cb.debit_amount,
        cb.credit_amount,
        cb.total_transactions_count,
        cb.last_transaction_date,
        -- حساب الحوالات المعلقة للصرافين
        CASE 
            WHEN cb.entity_type_code = 'MONEY_CHANGER' THEN
                NVL((SELECT SUM(amount) FROM transfers 
                     WHERE money_changer_id = cb.entity_id 
                     AND currency = cb.currency_code 
                     AND status = 'approved'), 0)
            ELSE 0
        END as pending_transfers,
        -- حساب الرصيد المتاح
        CASE 
            WHEN cb.entity_type_code = 'MONEY_CHANGER' THEN
                cb.current_balance - NVL((SELECT SUM(amount) FROM transfers 
                                         WHERE money_changer_id = cb.entity_id 
                                         AND currency = cb.currency_code 
                                         AND status = 'approved'), 0)
            ELSE cb.current_balance
        END as available_balance
    FROM CURRENT_BALANCES cb
    LEFT JOIN suppliers s ON cb.entity_type_code = 'SUPPLIER' AND cb.entity_id = s.id
    LEFT JOIN money_changers mc ON cb.entity_type_code = 'MONEY_CHANGER' AND cb.entity_id = mc.id
    LEFT JOIN banks b ON cb.entity_type_code = 'BANK' AND cb.entity_id = b.id
    WHERE (p_entity_type IS NULL OR cb.entity_type_code = p_entity_type)
    AND (p_currency_code IS NULL OR cb.currency_code = p_currency_code)
    ORDER BY cb.entity_type_code, entity_name, cb.currency_code;
END;
/

-- 5. إجراء للتحقق من الحد الأدنى للأرصدة
CREATE OR REPLACE FUNCTION CHECK_MINIMUM_BALANCE_LIMITS(
    p_money_changer_id IN NUMBER,
    p_currency_code IN VARCHAR2 DEFAULT 'SAR'
) RETURN VARCHAR2 AS
    v_current_balance NUMBER := 0;
    v_minimum_limit NUMBER := 0;
    v_warning_limit NUMBER := 0;
    v_result VARCHAR2(4000);
BEGIN
    -- الحصول على الرصيد الحالي
    SELECT NVL(current_balance, 0) INTO v_current_balance
    FROM CURRENT_BALANCES
    WHERE entity_type_code = 'MONEY_CHANGER'
    AND entity_id = p_money_changer_id
    AND currency_code = p_currency_code;
    
    -- الحصول على حدود الرصيد (يمكن إضافة جدول منفصل للحدود)
    -- مؤقتاً سنستخدم قيم افتراضية
    v_minimum_limit := 10000; -- الحد الأدنى
    v_warning_limit := 50000; -- حد التحذير
    
    -- تحديد النتيجة
    IF v_current_balance < v_minimum_limit THEN
        v_result := 'CRITICAL: الرصيد أقل من الحد الأدنى المسموح';
    ELSIF v_current_balance < v_warning_limit THEN
        v_result := 'WARNING: الرصيد أقل من حد التحذير';
    ELSE
        v_result := 'OK: الرصيد ضمن الحدود المقبولة';
    END IF;
    
    v_result := v_result || '|CURRENT:' || v_current_balance || 
                '|MINIMUM:' || v_minimum_limit || 
                '|WARNING:' || v_warning_limit;
    
    RETURN v_result;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 'ERROR: لا يوجد رصيد للصراف';
    WHEN OTHERS THEN
        RETURN 'ERROR: ' || SQLERRM;
END;
/

-- إنشاء comments للتوثيق
COMMENT ON FUNCTION CHECK_MONEY_CHANGER_BALANCE IS 'التحقق من كفاية رصيد الصراف لتنفيذ حوالة';
COMMENT ON FUNCTION VALIDATE_SUPPLIER_DISTRIBUTIONS IS 'التحقق من صحة توزيعات الموردين';
COMMENT ON FUNCTION VALIDATE_TRANSFER_EXECUTION IS 'التحقق الشامل قبل تنفيذ الحوالة';
COMMENT ON PROCEDURE GET_BALANCES_SUMMARY IS 'الحصول على ملخص شامل للأرصدة';
COMMENT ON FUNCTION CHECK_MINIMUM_BALANCE_LIMITS IS 'التحقق من حدود الرصيد الأدنى';

-- عرض رسالة نجاح
SELECT 'تم إنشاء إجراءات التحقق من الأرصدة بنجاح' as result FROM DUAL;
