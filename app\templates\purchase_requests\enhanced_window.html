{% extends "base.html" %}

{% block title %}نافذة طلبات الشراء المحسنة{% endblock %}

{% block extra_css %}
<!-- تحميل Font Awesome مباشرة -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-purchase-requests.css') }}?v=2.0">
<style>
/* تحديث CSS - إصدار 2.0 */
/* إخفاء الأيقونات القديمة وإظهار الجديدة */
.toolbar-btn .icon { display: none !important; }
.toolbar-btn span.icon { display: none !important; }

/* تأكيد تحميل Font Awesome */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

/* إجبار عرض الأيقونات */
i.fas, i.far, i.fab {
    display: inline-block !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* تنسيق الأيقونات الجديدة - قوي جداً */
.toolbar-btn i.fas {
    display: block !important;
    font-size: 20px !important;
    margin-bottom: 8px !important;
    color: white !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

.detail-btn i {
    margin-left: 5px !important;
    font-size: 14px !important;
    display: inline-block !important;
}

.nav-link i {
    margin-left: 8px !important;
    font-size: 16px !important;
    display: inline-block !important;
}

/* نظام الألوان المحسن - Enhanced Color System */
:root {
    --primary-color: #3498DB;
    --secondary-color: #2ECC71;
    --accent-color: #9B59B6;
    --warning-color: #F1C40F;
    --danger-color: #E74C3C;
    --background-light: #F8F9FA;
    --card-background: #FFFFFF;
    --panel-background: #F5F6F7;
    --text-primary: #212529;
    --text-secondary: #6C757D;
    --text-muted: #ADB5BD;
}

/* النافذة الرئيسية */
.enhanced-window {
    background: var(--background-light);
    min-height: 100vh;
    font-family: 'Arial', sans-serif;
    direction: rtl;
}

/* شريط الأدوات الجديد - بسيط وفعال */
.new-toolbar {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: space-around;
    align-items: flex-start;
}

.toolbar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    min-width: 220px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.toolbar-section:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* ألوان مختلفة لكل قسم */
.toolbar-section:nth-child(1) h5 {
    border-bottom-color: #28a745;
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
}

.toolbar-section:nth-child(2) h5 {
    border-bottom-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
}

.toolbar-section:nth-child(3) h5 {
    border-bottom-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
}

.toolbar-section h5 {
    color: #495057;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    text-align: center;
    border-bottom: 3px solid #007bff;
    padding-bottom: 8px;
    width: 100%;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 8px;
    border-radius: 8px 8px 0 0;
    margin: -10px -10px 15px -10px;
}

.toolbar-section .btn {
    min-width: 100px;
    margin: 2px;
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.toolbar-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* تنسيقات إضافية للشريط الجديد */
.new-toolbar .btn span {
    display: inline-block;
    font-size: 13px;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .new-toolbar {
        flex-direction: column;
        gap: 20px;
    }

    .toolbar-section {
        width: 100%;
        min-width: auto;
    }

    .toolbar-section .btn {
        min-width: 80px;
        font-size: 11px;
    }
}

/* تم حذف التنسيقات القديمة - الشريط الجديد لا يحتاجها */

/* تم حذف التأثيرات القديمة */

/* تم حذف الحركات المتحركة القديمة */

/* تم حذف ألوان الأزرار القديمة - الشريط الجديد يستخدم Bootstrap */
/* تم حذف جميع تنسيقات الألوان القديمة */

/* المحتوى الرئيسي */
.main-content {
    display: flex;
    height: calc(100vh - 120px);
    gap: 15px;
    padding: 15px;
}

/* القسم الرئيسي - Master Section */
.master-section {
    flex: 2;
    background: var(--card-background);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* التبويبات */
.nav-tabs {
    background: var(--panel-background);
    border-bottom: 2px solid var(--primary-color);
}

.nav-tabs .nav-link {
    color: var(--text-secondary);
    font-weight: bold;
    border: none;
    border-radius: 0;
    padding: 12px 20px;
}

.nav-tabs .nav-link.active {
    background: var(--primary-color);
    color: white;
    border-bottom: 3px solid var(--secondary-color);
}

.tab-content {
    padding: 20px;
    height: calc(100% - 60px);
    overflow-y: auto;
}

/* تخطيط النماذج - 3 أعمدة */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
    align-items: start;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    font-weight: bold;
    color: var(--text-primary);
    margin-bottom: 5px;
    display: block;
}

.form-control {
    border: 2px solid #E9ECEF;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* قسم التفاصيل - Detail Section */
.detail-section {
    flex: 3;
    background: var(--card-background);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
}

.detail-header {
    background: var(--primary-color);
    color: white;
    padding: 15px 20px;
    font-weight: bold;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-toolbar {
    display: flex;
    gap: 8px;
}

.detail-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    color: white;
    transition: all 0.3s ease;
}

.detail-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* جدول التفاصيل - 7 أعمدة */
.details-table-container {
    flex: 1;
    overflow: auto;
    padding: 15px;
}

.details-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
}

.details-table th {
    background: var(--panel-background);
    color: var(--text-primary);
    font-weight: bold;
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #DEE2E6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.details-table td {
    padding: 8px;
    border: 1px solid #DEE2E6;
    text-align: center;
    vertical-align: middle;
}

.details-table tbody tr:hover {
    background: #F8F9FA;
}

.details-table tbody tr.selected {
    background: rgba(52, 152, 219, 0.1);
}

/* حقول الإدخال في الجدول */
.table-input {
    width: 100%;
    border: 1px solid #CED4DA;
    border-radius: 4px;
    padding: 4px 6px;
    font-size: 12px;
}

.table-input:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* شريط التنقل */
.navigation-bar {
    background: var(--card-background);
    border-top: 2px solid var(--primary-color);
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
}

.nav-buttons {
    display: flex;
    gap: 10px;
}

.nav-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    background: var(--primary-color);
    color: white;
}

.nav-btn:hover {
    background: #2980B9;
    transform: translateY(-1px);
}

.nav-btn:disabled {
    background: #BDC3C7;
    cursor: not-allowed;
    transform: none;
}

.record-info {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border-top: 3px solid #3498DB;
    padding: 12px 20px;
    font-size: 13px;
    color: #ecf0f1;
    text-align: center;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 -4px 15px rgba(0,0,0,0.2);
    font-weight: 500;
}

.record-info i {
    margin-left: 8px;
    margin-right: 4px;
    color: #3498DB;
    font-size: 14px;
}

/* الاستجابة للأجهزة المحمولة */
@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
        height: auto;
    }
    
    .form-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .toolbar-group {
        margin-left: 10px;
        padding-left: 10px;
    }
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .enhanced-toolbar {
        justify-content: center;
    }
    
    .toolbar-group {
        margin-left: 5px;
        padding-left: 5px;
    }
    
    .toolbar-btn {
        min-width: 50px;
        padding: 6px 8px;
        font-size: 10px;
    }
}

/* تحسينات إضافية */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.success-message {
    background: #D4EDDA;
    color: #155724;
    border: 1px solid #C3E6CB;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

.error-message {
    background: #F8D7DA;
    color: #721C24;
    border: 1px solid #F5C6CB;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

.required {
    color: #E74C3C;
}

.readonly {
    background: #F8F9FA;
    color: #6C757D;
}
</style>
{% endblock %}

{% block content %}
<div class="enhanced-window">
    <!-- شريط الأدوات الجديد - بسيط وفعال -->
    <div class="new-toolbar">
        <div class="toolbar-section">
            <h5>إدارة الطلب</h5>
            <button class="btn btn-success btn-sm" onclick="newRecord()" title="إنشاء طلب جديد">
                <span>+ جديد</span>
            </button>
            <button class="btn btn-primary btn-sm" onclick="saveRecord()" title="حفظ التغييرات">
                <span>💾 حفظ</span>
            </button>
            <button class="btn btn-warning btn-sm" onclick="editRecord()" title="تفعيل وضع التعديل">
                <span>✏️ تعديل</span>
            </button>
            <button class="btn btn-danger btn-sm" onclick="deleteRecord()" title="حذف الطلب">
                <span>🗑️ حذف</span>
            </button>
        </div>

        <div class="toolbar-section">
            <h5>التنقل</h5>
            <button class="btn btn-outline-secondary btn-sm" onclick="navigateFirst()" title="الطلب الأول">
                <span>⏮️ الأول</span>
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="navigatePrevious()" title="الطلب السابق">
                <span>⏪ السابق</span>
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="navigateNext()" title="الطلب التالي">
                <span>⏩ التالي</span>
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="navigateLast()" title="الطلب الأخير">
                <span>⏭️ الأخير</span>
            </button>
        </div>

        <div class="toolbar-section">
            <h5>أدوات إضافية</h5>
            <button class="btn btn-info btn-sm" onclick="openAdvancedSearch()" title="البحث المتقدم">
                <span>🔍 بحث</span>
            </button>
            <button class="btn btn-secondary btn-sm" onclick="printRecord()" title="طباعة الطلب">
                <span>🖨️ طباعة</span>
            </button>
            <button class="btn btn-outline-info btn-sm" onclick="refreshData()" title="تحديث البيانات">
                <span>🔄 تحديث</span>
            </button>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- القسم الرئيسي - Master Section -->
        <div class="master-section">
            <!-- التبويبات الثلاث -->
            <ul class="nav nav-tabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#basic-info" role="tab">
                        <i class="fas fa-edit"></i> المعلومات الأساسية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#financial-info" role="tab">
                        <i class="fas fa-money-bill-wave"></i> المعلومات المالية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#additional-info" role="tab">
                        <i class="fas fa-file-alt"></i> معلومات إضافية
                    </a>
                </li>
            </ul>

            <div class="tab-content">
                <!-- تبويب المعلومات الأساسية (3 أعمدة × 6 صفوف) -->
                <div class="tab-pane fade show active" id="basic-info" role="tabpanel">
                    <form id="purchaseRequestForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="req_no">رقم الطلب <span class="required">*</span></label>
                                <input type="text" class="form-control" id="req_no" name="req_no" required>
                            </div>
                            <div class="form-group">
                                <label for="req_serial">الرقم التسلسلي <span class="required">*</span></label>
                                <input type="number" class="form-control" id="req_serial" name="req_serial" required>
                            </div>
                            <div class="form-group">
                                <label for="requester_name">اسم الطالب <span class="required">*</span></label>
                                <input type="text" class="form-control" id="requester_name" name="requester_name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="department_name">اسم القسم <span class="required">*</span></label>
                                <input type="text" class="form-control" id="department_name" name="department_name" required>
                            </div>
                            <div class="form-group">
                                <label for="req_type">نوع الطلب</label>
                                <select class="form-control" id="req_type" name="req_type">
                                    <option value="عادي">عادي</option>
                                    <option value="عاجل">عاجل</option>
                                    <option value="طارئ">طارئ</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="req_priority">أولوية الطلب</label>
                                <select class="form-control" id="req_priority" name="req_priority">
                                    <option value="عادي">عادي</option>
                                    <option value="عاجل">عاجل</option>
                                    <option value="طارئ">طارئ</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="req_date">تاريخ الطلب <span class="required">*</span></label>
                                <input type="date" class="form-control" id="req_date" name="req_date" required>
                            </div>
                            <div class="form-group">
                                <label for="needed_date">تاريخ الحاجة <span class="required">*</span></label>
                                <input type="date" class="form-control" id="needed_date" name="needed_date" required>
                            </div>
                            <div class="form-group">
                                <label for="req_status">حالة الطلب</label>
                                <select class="form-control" id="req_status" name="req_status">
                                    <option value="مسودة">مسودة</option>
                                    <option value="مرسل">مرسل</option>
                                    <option value="معتمد">معتمد</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- تبويب المعلومات المالية (3 أعمدة × 4 صفوف) -->
                <div class="tab-pane fade" id="financial-info" role="tabpanel">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="currency">العملة</label>
                            <select class="form-control" id="currency" name="currency">
                                <option value="ريال">ريال سعودي</option>
                                <option value="دولار">دولار أمريكي</option>
                                <option value="يورو">يورو</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="total_amount">الإجمالي</label>
                            <input type="number" class="form-control readonly" id="total_amount" name="total_amount" readonly step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="approval_status">حالة الموافقة</label>
                            <select class="form-control" id="approval_status" name="approval_status">
                                <option value="في انتظار">في انتظار</option>
                                <option value="معتمد">معتمد</option>
                                <option value="مرفوض">مرفوض</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="contract_no">رقم العقد</label>
                            <input type="text" class="form-control" id="contract_no" name="contract_no">
                        </div>
                        <div class="form-group">
                            <label for="contract_serial">رقم تسلسلي العقد</label>
                            <input type="number" class="form-control" id="contract_serial" name="contract_serial">
                        </div>
                        <div class="form-group">
                            <label for="contract_amount">مبلغ العقد</label>
                            <input type="number" class="form-control" id="contract_amount" name="contract_amount" step="0.01">
                        </div>
                    </div>
                </div>

                <!-- تبويب معلومات إضافية -->
                <div class="tab-pane fade" id="additional-info" role="tabpanel">
                    <div class="form-group">
                        <label for="attachments">المرفقات</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="attachments" name="attachments">
                            <button class="btn btn-outline-secondary" type="button" onclick="browseFiles()">استعراض</button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">الملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم التفاصيل - Detail Section -->
        <div class="detail-section">
            <div class="detail-header">
                <span><i class="fas fa-clipboard-list"></i> تفاصيل الطلب</span>
                <div class="detail-toolbar">
                    <button class="detail-btn btn-record" onclick="addDetailRow()" title="إضافة سطر جديد">
                        <i class="fas fa-plus"></i> <span>إضافة</span>
                    </button>
                    <button class="detail-btn btn-control" onclick="editDetailRow()" title="تعديل السطر المحدد">
                        <i class="fas fa-edit"></i> <span>تعديل</span>
                    </button>
                    <button class="detail-btn btn-delete" onclick="deleteDetailRow()" title="حذف السطر المحدد">
                        <i class="fas fa-trash-alt"></i> <span>حذف</span>
                    </button>
                    <button class="detail-btn btn-record" onclick="saveDetailChanges()" title="حفظ التعديلات">
                        <i class="fas fa-save"></i> <span>حفظ</span>
                    </button>
                    <button class="detail-btn btn-control" onclick="cancelDetailEdit()" title="إلغاء التعديل">
                        <i class="fas fa-times"></i> <span>إلغاء</span>
                    </button>
                </div>
            </div>
            
            <div class="details-table-container">
                <table class="details-table" id="detailsTable">
                    <thead>
                        <tr>
                            <th style="width: 80px;">رقم السطر</th>
                            <th style="width: 120px;">كود الصنف</th>
                            <th style="width: 200px;">اسم الصنف</th>
                            <th style="width: 150px;">الوصف</th>
                            <th style="width: 100px;">الكمية</th>
                            <th style="width: 120px;">السعر</th>
                            <th style="width: 120px;">الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody id="detailsTableBody">
                        <!-- سيتم ملء الصفوف ديناميكياً -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- شريط التنقل -->
    <div class="navigation-bar">
        <div class="nav-buttons">
            <button class="nav-btn" onclick="navigatePrevious()" id="prevBtn">
                ⏪ السابق
            </button>
            <button class="nav-btn" onclick="navigateNext()" id="nextBtn">
                التالي ⏩
            </button>
        </div>
        
        <div class="record-info" id="recordInfo">
            <i class="fas fa-chart-bar"></i> السجل 1 من {{ total_records }} |
            <i class="fas fa-file-alt"></i> {{ current_request.req_no if current_request else 'جديد' }} |
            <i class="fas fa-user"></i> {{ current_request.requester_name if current_request else '' }} |
            <i class="fas fa-bookmark"></i> {{ current_request.req_status if current_request else 'مسودة' }} |
            <i class="fas fa-money-bill-wave"></i> {{ current_request.formatted_total if current_request else '0.00 ريال' }}
        </div>
        
        <div class="nav-buttons">
            <input type="number" class="form-control" style="width: 80px; display: inline-block;" id="quickNavInput" placeholder="رقم">
            <button class="nav-btn" onclick="quickNavigate()">انتقال</button>
        </div>
    </div>
</div>

<!-- رسائل التنبيه -->
<div id="messageContainer"></div>

<!-- حقول مخفية -->
<input type="hidden" id="currentRecordId" value="{{ current_request.id if current_request else '' }}">
<input type="hidden" id="currentRecordNumber" value="{{ current_record }}">
<input type="hidden" id="totalRecords" value="{{ total_records }}">
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let currentRecord = null;
let detailRows = [];
let isEditMode = false;
let selectedDetailRow = -1;

// تحميل البيانات عند بدء الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadCurrentRecord();
    setupEventListeners();
});

// تهيئة الصفحة
function initializePage() {
    // تعيين التاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('req_date').value = today;

    // إضافة صف فارغ في جدول التفاصيل
    addDetailRow();

    // تحديث معلومات السجل
    updateRecordInfo();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // مستمعي أحداث الحقول للحساب التلقائي
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('quantity-input') || e.target.classList.contains('price-input')) {
            calculateRowTotal(e.target.closest('tr'));
            calculateGrandTotal();
        }
    });

    // مستمع أحداث تحديد الصفوف
    document.addEventListener('click', function(e) {
        if (e.target.closest('#detailsTableBody tr')) {
            selectDetailRow(e.target.closest('tr'));
        }
    });

    // اختصارات لوحة المفاتيح
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey) {
            switch(e.key) {
                case 'Home': navigateFirst(); e.preventDefault(); break;
                case 'End': navigateLast(); e.preventDefault(); break;
                case 'ArrowLeft': navigatePrevious(); e.preventDefault(); break;
                case 'ArrowRight': navigateNext(); e.preventDefault(); break;
                case 'f': openAdvancedSearch(); e.preventDefault(); break;
                case 'g': quickNavigation(); e.preventDefault(); break;
                case 'n': newRecord(); e.preventDefault(); break;
                case 's': saveRecord(); e.preventDefault(); break;
            }
        } else {
            switch(e.key) {
                case 'F3': openAdvancedSearch(); e.preventDefault(); break;
                case 'F5': refreshData(); e.preventDefault(); break;
                case 'PageUp': navigatePrevious(); e.preventDefault(); break;
                case 'PageDown': navigateNext(); e.preventDefault(); break;
            }
        }
    });
}

// 1️⃣ مجموعة إدارة السجلات
function newRecord() {
    if (confirm('هل تريد إنشاء طلب جديد؟ سيتم فقدان التغييرات غير المحفوظة.')) {
        clearForm();
        currentRecord = null;
        document.getElementById('currentRecordId').value = '';
        isEditMode = true;
        showMessage('تم إنشاء طلب جديد', 'success');

        // إضافة صف فارغ في التفاصيل
        clearDetailsTable();
        addDetailRow();
    }
}

function saveRecord() {
    if (!validateForm()) {
        return;
    }

    showLoading(true);

    const formData = collectFormData();

    fetch('/purchase_requests/api/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);
        if (data.success) {
            showMessage(data.message, 'success');
            document.getElementById('currentRecordId').value = data.id;
            document.getElementById('total_amount').value = data.total_amount;
            currentRecord = data;
            isEditMode = false;
        } else {
            showMessage(data.message, 'error');
        }
    })
    .catch(error => {
        showLoading(false);
        showMessage('خطأ في الاتصال: ' + error.message, 'error');
    });
}

function deleteRecord() {
    const recordId = document.getElementById('currentRecordId').value;
    if (!recordId) {
        showMessage('لا يوجد سجل للحذف', 'error');
        return;
    }

    if (confirm('هل أنت متأكد من حذف هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.')) {
        showLoading(true);

        fetch(`/purchase_requests/api/delete/${recordId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                showMessage(data.message, 'success');
                newRecord();
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            showLoading(false);
            showMessage('خطأ في الاتصال: ' + error.message, 'error');
        });
    }
}

function refreshData() {
    const recordId = document.getElementById('currentRecordId').value;
    if (recordId) {
        loadRecordById(recordId);
    } else {
        location.reload();
    }
    showMessage('تم تحديث البيانات', 'success');
}

// 2️⃣ مجموعة التحكم
function editRecord() {
    isEditMode = true;
    enableFormFields(true);
    showMessage('تم تفعيل وضع التعديل', 'success');
}

function cancelEdit() {
    if (confirm('هل تريد إلغاء التعديلات؟ سيتم فقدان التغييرات غير المحفوظة.')) {
        isEditMode = false;
        enableFormFields(false);
        const recordId = document.getElementById('currentRecordId').value;
        if (recordId) {
            loadRecordById(recordId);
        } else {
            clearForm();
        }
        showMessage('تم إلغاء التعديل', 'success');
    }
}

function printRecord() {
    const recordId = document.getElementById('currentRecordId').value;
    if (!recordId) {
        showMessage('لا يوجد سجل للطباعة', 'error');
        return;
    }

    // فتح نافذة الطباعة
    window.open(`/purchase_requests/print/${recordId}`, '_blank');
}

// 3️⃣ مجموعة البحث
function openAdvancedSearch() {
    // فتح نافذة البحث المتقدم
    showMessage('سيتم فتح نافذة البحث المتقدم قريباً', 'info');
}

function applyQuickFilter() {
    // تطبيق فلاتر سريعة
    showMessage('سيتم تطبيق الفلاتر السريعة قريباً', 'info');
}

// 4️⃣ مجموعة التنقل
function navigateFirst() {
    navigate('first');
}

function navigatePrevious() {
    navigate('previous');
}

function navigateNext() {
    navigate('next');
}

function navigateLast() {
    navigate('last');
}

function quickNavigation() {
    const recordNumber = prompt('أدخل رقم السجل:');
    if (recordNumber && !isNaN(recordNumber)) {
        const totalRecords = parseInt(document.getElementById('totalRecords').value);
        if (recordNumber >= 1 && recordNumber <= totalRecords) {
            showMessage(`الانتقال إلى السجل رقم ${recordNumber}`, 'success');
        } else {
            showMessage('رقم السجل غير صحيح', 'error');
        }
    }
}

function navigate(direction) {
    const currentId = document.getElementById('currentRecordId').value;

    showLoading(true);

    fetch(`/purchase_requests/api/navigate/${direction}?current_id=${currentId}`)
    .then(response => response.json())
    .then(data => {
        showLoading(false);
        if (data.error) {
            showMessage(data.error, 'error');
        } else {
            loadRecordData(data);
            updateRecordInfo(data.current_record, data.total_records);
        }
    })
    .catch(error => {
        showLoading(false);
        showMessage('خطأ في التنقل: ' + error.message, 'error');
    });
}

// 5️⃣ مجموعة العمليات المتقدمة
function linkToContract() {
    showMessage('سيتم فتح نافذة اختيار العقد قريباً', 'info');
}

function exportData() {
    showMessage('سيتم تصدير البيانات قريباً', 'info');
}

function importData() {
    showMessage('سيتم استيراد البيانات قريباً', 'info');
}

// 6️⃣ مجموعة المساعدة
function showHelp() {
    alert('مساعدة نافذة طلبات الشراء المحسنة\\n\\nاختصارات لوحة المفاتيح:\\n- Ctrl+N: طلب جديد\\n- Ctrl+S: حفظ\\n- Ctrl+F: بحث\\n- F5: تحديث\\n- Page Up/Down: تنقل\\n\\nللمزيد من المساعدة، راجع دليل المستخدم.');
}

function showAbout() {
    alert('نافذة طلبات الشراء المحسنة\\nالإصدار 2.0\\nتطوير: Augment Agent\\nيوليو 2025\\n\\nالميزات:\\n- 19 زر وظيفي\\n- 3 تبويبات\\n- جدول تفاصيل تفاعلي\\n- بحث متقدم\\n- ربط بالعقود');
}

// وظائف جدول التفاصيل
function addDetailRow() {
    const tbody = document.getElementById('detailsTableBody');
    const rowCount = tbody.children.length + 1;

    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${rowCount}</td>
        <td><input type="text" class="table-input item-code-input" placeholder="كود الصنف"></td>
        <td><input type="text" class="table-input item-name-input" placeholder="اسم الصنف"></td>
        <td><input type="text" class="table-input description-input" placeholder="الوصف"></td>
        <td><input type="number" class="table-input quantity-input" placeholder="الكمية" step="0.001" min="0"></td>
        <td><input type="number" class="table-input price-input" placeholder="السعر" step="0.01" min="0"></td>
        <td class="total-cell">0.00</td>
    `;

    tbody.appendChild(row);

    // تركيز على أول حقل في الصف الجديد
    row.querySelector('.item-code-input').focus();
}

function editDetailRow() {
    if (selectedDetailRow >= 0) {
        const row = document.getElementById('detailsTableBody').children[selectedDetailRow];
        enableRowEditing(row, true);
        showMessage('تم تفعيل تعديل السطر', 'success');
    } else {
        showMessage('يرجى تحديد سطر للتعديل', 'error');
    }
}

function deleteDetailRow() {
    if (selectedDetailRow >= 0) {
        if (confirm('هل تريد حذف هذا السطر؟')) {
            const tbody = document.getElementById('detailsTableBody');
            tbody.removeChild(tbody.children[selectedDetailRow]);
            selectedDetailRow = -1;
            renumberRows();
            calculateGrandTotal();
            showMessage('تم حذف السطر', 'success');
        }
    } else {
        showMessage('يرجى تحديد سطر للحذف', 'error');
    }
}

function saveDetailChanges() {
    calculateGrandTotal();
    showMessage('تم حفظ تغييرات التفاصيل', 'success');
}

function cancelDetailEdit() {
    showMessage('تم إلغاء تعديل التفاصيل', 'success');
}

// وظائف مساعدة
function selectDetailRow(row) {
    // إزالة التحديد من جميع الصفوف
    const rows = document.querySelectorAll('#detailsTableBody tr');
    rows.forEach(r => r.classList.remove('selected'));

    // تحديد الصف الحالي
    row.classList.add('selected');
    selectedDetailRow = Array.from(row.parentNode.children).indexOf(row);
}

function calculateRowTotal(row) {
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const price = parseFloat(row.querySelector('.price-input').value) || 0;
    const total = quantity * price;

    row.querySelector('.total-cell').textContent = total.toFixed(2);
    return total;
}

function calculateGrandTotal() {
    let grandTotal = 0;
    const rows = document.querySelectorAll('#detailsTableBody tr');

    rows.forEach(row => {
        const total = calculateRowTotal(row);
        grandTotal += total;
    });

    document.getElementById('total_amount').value = grandTotal.toFixed(2);
    updateRecordInfo();
    return grandTotal;
}

function renumberRows() {
    const rows = document.querySelectorAll('#detailsTableBody tr');
    rows.forEach((row, index) => {
        row.cells[0].textContent = index + 1;
    });
}

function clearDetailsTable() {
    document.getElementById('detailsTableBody').innerHTML = '';
    selectedDetailRow = -1;
}

function validateForm() {
    const requiredFields = ['req_no', 'req_serial', 'requester_name', 'department_name', 'req_date', 'needed_date'];

    for (let fieldId of requiredFields) {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            showMessage(`الحقل "${field.previousElementSibling.textContent.replace(' *', '')}" مطلوب`, 'error');
            field.focus();
            return false;
        }
    }

    // التحقق من وجود عناصر في التفاصيل
    const hasItems = Array.from(document.querySelectorAll('.item-name-input')).some(input => input.value.trim());
    if (!hasItems) {
        showMessage('يجب إضافة عنصر واحد على الأقل في التفاصيل', 'error');
        return false;
    }

    return true;
}

function collectFormData() {
    const formData = {
        id: document.getElementById('currentRecordId').value || null,
        req_no: document.getElementById('req_no').value,
        req_serial: parseInt(document.getElementById('req_serial').value),
        requester_name: document.getElementById('requester_name').value,
        department_name: document.getElementById('department_name').value,
        req_type: document.getElementById('req_type').value,
        req_priority: document.getElementById('req_priority').value,
        req_date: document.getElementById('req_date').value,
        needed_date: document.getElementById('needed_date').value,
        req_status: document.getElementById('req_status').value,
        currency: document.getElementById('currency').value,
        total_amount: parseFloat(document.getElementById('total_amount').value) || 0,
        approval_status: document.getElementById('approval_status').value,
        contract_no: document.getElementById('contract_no').value,
        contract_serial: parseInt(document.getElementById('contract_serial').value) || null,
        contract_amount: parseFloat(document.getElementById('contract_amount').value) || null,
        description: document.getElementById('description').value,
        notes: document.getElementById('notes').value,
        attachments: document.getElementById('attachments').value,
        items: []
    };

    // جمع عناصر التفاصيل
    const rows = document.querySelectorAll('#detailsTableBody tr');
    rows.forEach((row, index) => {
        const itemName = row.querySelector('.item-name-input').value.trim();
        if (itemName) {
            formData.items.push({
                line_number: index + 1,
                item_code: row.querySelector('.item-code-input').value,
                item_name: itemName,
                item_description: row.querySelector('.description-input').value,
                quantity: parseFloat(row.querySelector('.quantity-input').value) || 0,
                unit_name: 'قطعة',
                unit_price: parseFloat(row.querySelector('.price-input').value) || 0,
                total_price: parseFloat(row.querySelector('.total-cell').textContent) || 0
            });
        }
    });

    return formData;
}

function loadRecordData(data) {
    // تحميل البيانات الأساسية
    document.getElementById('currentRecordId').value = data.id;
    document.getElementById('req_no').value = data.req_no || '';
    document.getElementById('req_serial').value = data.req_serial || '';
    document.getElementById('requester_name').value = data.requester_name || '';
    document.getElementById('department_name').value = data.department_name || '';
    document.getElementById('req_type').value = data.req_type || 'عادي';
    document.getElementById('req_priority').value = data.req_priority || 'عادي';
    document.getElementById('req_date').value = data.req_date || '';
    document.getElementById('needed_date').value = data.needed_date || '';
    document.getElementById('req_status').value = data.req_status || 'مسودة';

    // تحميل البيانات المالية
    document.getElementById('currency').value = data.currency || 'ريال';
    document.getElementById('total_amount').value = data.total_amount || 0;
    document.getElementById('approval_status').value = data.approval_status || 'في انتظار';
    document.getElementById('contract_no').value = data.contract_no || '';
    document.getElementById('contract_serial').value = data.contract_serial || '';
    document.getElementById('contract_amount').value = data.contract_amount || '';

    // تحميل المعلومات الإضافية
    document.getElementById('description').value = data.description || '';
    document.getElementById('notes').value = data.notes || '';
    document.getElementById('attachments').value = data.attachments || '';

    // تحميل عناصر التفاصيل
    clearDetailsTable();
    if (data.items && data.items.length > 0) {
        data.items.forEach(item => {
            addDetailRowWithData(item);
        });
    } else {
        addDetailRow();
    }

    currentRecord = data;
    updateRecordInfo();
}

function addDetailRowWithData(itemData) {
    const tbody = document.getElementById('detailsTableBody');
    const row = document.createElement('tr');

    row.innerHTML = `
        <td>${itemData.line_number}</td>
        <td><input type="text" class="table-input item-code-input" value="${itemData.item_code || ''}"></td>
        <td><input type="text" class="table-input item-name-input" value="${itemData.item_name || ''}"></td>
        <td><input type="text" class="table-input description-input" value="${itemData.item_description || ''}"></td>
        <td><input type="number" class="table-input quantity-input" value="${itemData.quantity || 0}" step="0.001" min="0"></td>
        <td><input type="number" class="table-input price-input" value="${itemData.unit_price || 0}" step="0.01" min="0"></td>
        <td class="total-cell">${(itemData.total_price || 0).toFixed(2)}</td>
    `;

    tbody.appendChild(row);
}

function clearForm() {
    // مسح جميع الحقول
    document.getElementById('purchaseRequestForm').reset();
    document.getElementById('description').value = '';
    document.getElementById('notes').value = '';
    document.getElementById('attachments').value = '';

    // تعيين القيم الافتراضية
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('req_date').value = today;
    document.getElementById('req_status').value = 'مسودة';
    document.getElementById('req_type').value = 'عادي';
    document.getElementById('req_priority').value = 'عادي';
    document.getElementById('currency').value = 'ريال';
    document.getElementById('approval_status').value = 'في انتظار';
    document.getElementById('total_amount').value = '0.00';

    clearDetailsTable();
    updateRecordInfo();
}

function updateRecordInfo(current = null, total = null) {
    const currentRecord = current || document.getElementById('currentRecordNumber').value || '1';
    const totalRecords = total || document.getElementById('totalRecords').value || '0';
    const reqNo = document.getElementById('req_no').value || 'جديد';
    const requesterName = document.getElementById('requester_name').value || '';
    const reqStatus = document.getElementById('req_status').value || 'مسودة';
    const totalAmount = document.getElementById('total_amount').value || '0.00';
    const currency = document.getElementById('currency').value || 'ريال';

    document.getElementById('recordInfo').innerHTML =
        `<i class="fas fa-chart-bar"></i> السجل ${currentRecord} من ${totalRecords} | <i class="fas fa-file-alt"></i> ${reqNo} | <i class="fas fa-user"></i> ${requesterName} | <i class="fas fa-bookmark"></i> ${reqStatus} | <i class="fas fa-money-bill-wave"></i> ${totalAmount} ${currency}`;
}

function enableFormFields(enabled) {
    const fields = document.querySelectorAll('#purchaseRequestForm input, #purchaseRequestForm select, #purchaseRequestForm textarea');
    fields.forEach(field => {
        if (!field.classList.contains('readonly')) {
            field.disabled = !enabled;
        }
    });
}

function showMessage(message, type = 'info') {
    const container = document.getElementById('messageContainer');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    container.appendChild(alertDiv);

    // إزالة الرسالة تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showLoading(show) {
    const body = document.body;
    if (show) {
        body.classList.add('loading');
    } else {
        body.classList.remove('loading');
    }
}

function loadCurrentRecord() {
    const recordId = document.getElementById('currentRecordId').value;
    if (recordId) {
        loadRecordById(recordId);
    }
}

function loadRecordById(id) {
    navigate('current', id);
}

function browseFiles() {
    // فتح مربع حوار اختيار الملفات
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.onchange = function(e) {
        const files = Array.from(e.target.files).map(f => f.name).join(', ');
        document.getElementById('attachments').value = files;
    };
    input.click();
}

// تحديث معلومات السجل عند تغيير البيانات
document.addEventListener('input', function(e) {
    if (['req_no', 'requester_name', 'req_status', 'total_amount', 'currency'].includes(e.target.id)) {
        updateRecordInfo();
    }
});

// وظائف الشريط الجديد
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الشريط الجديد بنجاح');
    initializeNewToolbar();
});

function initializeNewToolbar() {
    // إضافة تأثيرات للأزرار
    const buttons = document.querySelectorAll('.new-toolbar .btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    console.log(`تم تهيئة ${buttons.length} زر في الشريط الجديد`);
}

// وظائف الأزرار
function newRecord() {
    console.log('إنشاء طلب جديد');
    // مسح جميع الحقول
    document.getElementById('purchaseRequestForm').reset();
    updateRecordInfo();
    showMessage('تم إنشاء طلب جديد', 'success');
}

function saveRecord() {
    console.log('حفظ الطلب');
    const formData = collectFormData();
    // هنا يمكن إرسال البيانات للخادم
    showMessage('تم حفظ الطلب بنجاح', 'success');
}

function editRecord() {
    console.log('تفعيل وضع التعديل');
    enableFormFields(true);
    showMessage('تم تفعيل وضع التعديل', 'info');
}

function deleteRecord() {
    if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
        console.log('حذف الطلب');
        showMessage('تم حذف الطلب', 'warning');
    }
}

function navigateFirst() {
    console.log('الانتقال للطلب الأول');
    showMessage('تم الانتقال للطلب الأول', 'info');
}

function navigatePrevious() {
    console.log('الانتقال للطلب السابق');
    showMessage('تم الانتقال للطلب السابق', 'info');
}

function navigateNext() {
    console.log('الانتقال للطلب التالي');
    showMessage('تم الانتقال للطلب التالي', 'info');
}

function navigateLast() {
    console.log('الانتقال للطلب الأخير');
    showMessage('تم الانتقال للطلب الأخير', 'info');
}

function openAdvancedSearch() {
    console.log('فتح البحث المتقدم');
    showMessage('فتح نافذة البحث المتقدم', 'info');
}

function printRecord() {
    console.log('طباعة الطلب');
    window.print();
}

function refreshData() {
    console.log('تحديث البيانات');
    location.reload();
}

// دالة لعرض الرسائل
function showMessage(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'info': 'alert-info',
        'warning': 'alert-warning',
        'danger': 'alert-danger'
    };

    const alert = document.createElement('div');
    alert.className = `alert ${alertClass[type]} alert-dismissible fade show position-fixed`;
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 3000);
}
</script>

<!-- تحميل ملف تحسينات الأيقونات -->
<script src="{{ url_for('static', filename='js/enhanced-icons.js') }}?v=2.0"></script>
{% endblock %}
