{% extends "base.html" %}

{% block content %}
<div class="container mt-5">
    <h1>اختبار Bootstrap Modal</h1>
    
    <!-- زر لفتح النافذة -->
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
        فتح النافذة التجريبية
    </button>
    
    <!-- زر لفتح النافذة بـ JavaScript -->
    <button type="button" class="btn btn-success ms-2" onclick="openTestModal()">
        فتح بـ JavaScript
    </button>
    
    <!-- معلومات Bootstrap -->
    <div class="mt-4">
        <h3>معلومات Bootstrap:</h3>
        <div id="bootstrapInfo" class="alert alert-info">
            جاري فحص Bootstrap...
        </div>
    </div>
</div>

<!-- النافذة التجريبية -->
<div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModalLabel">نافذة تجريبية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هذه نافذة تجريبية للتأكد من عمل Bootstrap بشكل صحيح.</p>
                <p>إذا كنت ترى هذه الرسالة، فإن Bootstrap يعمل بشكل صحيح!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const infoDiv = document.getElementById('bootstrapInfo');
    
    if (typeof bootstrap !== 'undefined') {
        infoDiv.innerHTML = `
            <strong>✅ Bootstrap محمل بنجاح!</strong><br>
            الإصدار: ${bootstrap.Tooltip.VERSION || 'غير محدد'}<br>
            المكونات المتاحة: Modal, Tooltip, Popover, Dropdown, etc.
        `;
        infoDiv.className = 'alert alert-success';
    } else {
        infoDiv.innerHTML = `
            <strong>❌ Bootstrap غير محمل!</strong><br>
            يرجى التحقق من تحميل ملفات Bootstrap.
        `;
        infoDiv.className = 'alert alert-danger';
    }
});

function openTestModal() {
    if (typeof bootstrap === 'undefined') {
        alert('Bootstrap غير محمل!');
        return;
    }
    
    try {
        const modalElement = document.getElementById('testModal');
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('Modal opened successfully');
    } catch (error) {
        console.error('Error opening modal:', error);
        alert('خطأ في فتح النافذة: ' + error.message);
    }
}
</script>
{% endblock %}
