"""
محلل ذكي لجدول ITEM_MOVEMENT باستخدام الذكاء الاصطناعي
Intelligent Analyzer for ITEM_MOVEMENT table using AI
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# مكتبات التعلم الآلي
from sklearn.ensemble import IsolationForest, RandomForestRegressor
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.decomposition import PCA
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score

# مكتبات التحليل المتقدم
import scipy.stats as stats
from scipy.cluster.hierarchy import dendrogram, linkage
import statsmodels.api as sm
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.arima.model import ARIMA

# مكتبات التصور المتقدم
import plotly.figure_factory as ff
from wordcloud import WordCloud

import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class ItemMovementAnalyzer:
    """محلل ذكي لحركة الأصناف"""
    
    def __init__(self, db_connector):
        """
        تهيئة المحلل
        
        Args:
            db_connector: موصل قاعدة البيانات
        """
        self.db_connector = db_connector
        self.data = None
        self.processed_data = None
        self.analysis_results = {}
        
    def load_data(self, limit: int = None) -> bool:
        """
        تحميل بيانات جدول ITEM_MOVEMENT
        
        Args:
            limit: حد عدد الصفوف (اختياري)
            
        Returns:
            bool: نجح التحميل أم لا
        """
        try:
            query = "SELECT * FROM ITEM_MOVEMENT"
            if limit:
                query += f" WHERE ROWNUM <= {limit}"
                
            self.data = self.db_connector.execute_query(query)
            
            if not self.data.empty:
                logger.info(f"تم تحميل {len(self.data)} صف من جدول ITEM_MOVEMENT")
                return True
            else:
                logger.warning("لا توجد بيانات في الجدول")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات: {str(e)}")
            return False
    
    def preprocess_data(self):
        """معالجة وتنظيف البيانات"""
        try:
            if self.data is None or self.data.empty:
                logger.error("لا توجد بيانات للمعالجة")
                return
            
            self.processed_data = self.data.copy()
            
            # تحويل أعمدة التاريخ
            date_columns = [col for col in self.processed_data.columns 
                          if 'DATE' in col.upper() or 'TIME' in col.upper()]
            
            for col in date_columns:
                try:
                    self.processed_data[col] = pd.to_datetime(self.processed_data[col])
                except:
                    logger.warning(f"لا يمكن تحويل العمود {col} إلى تاريخ")
            
            # معالجة القيم المفقودة
            numeric_columns = self.processed_data.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                self.processed_data[col].fillna(self.processed_data[col].median(), inplace=True)
            
            # معالجة الأعمدة النصية
            text_columns = self.processed_data.select_dtypes(include=['object']).columns
            for col in text_columns:
                self.processed_data[col].fillna('غير محدد', inplace=True)
            
            logger.info("تم معالجة البيانات بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في معالجة البيانات: {str(e)}")
    
    def basic_analysis(self) -> Dict:
        """التحليل الأساسي للبيانات"""
        try:
            if self.processed_data is None:
                self.preprocess_data()
            
            analysis = {
                'data_overview': {
                    'total_records': len(self.processed_data),
                    'total_columns': len(self.processed_data.columns),
                    'date_range': self._get_date_range(),
                    'memory_usage': self.processed_data.memory_usage(deep=True).sum() / 1024**2  # MB
                },
                'column_analysis': {},
                'data_quality': {
                    'missing_values': self.processed_data.isnull().sum().to_dict(),
                    'duplicate_records': self.processed_data.duplicated().sum(),
                    'data_types': self.processed_data.dtypes.astype(str).to_dict()
                }
            }
            
            # تحليل كل عمود
            for column in self.processed_data.columns:
                col_analysis = self._analyze_column(column)
                analysis['column_analysis'][column] = col_analysis
            
            self.analysis_results['basic'] = analysis
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الأساسي: {str(e)}")
            return {}
    
    def _get_date_range(self) -> Dict:
        """الحصول على نطاق التواريخ في البيانات"""
        date_columns = self.processed_data.select_dtypes(include=['datetime64']).columns
        
        if len(date_columns) == 0:
            return {'message': 'لا توجد أعمدة تاريخ'}
        
        date_ranges = {}
        for col in date_columns:
            date_ranges[col] = {
                'min_date': self.processed_data[col].min(),
                'max_date': self.processed_data[col].max(),
                'date_span_days': (self.processed_data[col].max() - self.processed_data[col].min()).days
            }
        
        return date_ranges
    
    def _analyze_column(self, column: str) -> Dict:
        """تحليل عمود واحد"""
        col_data = self.processed_data[column]
        
        analysis = {
            'data_type': str(col_data.dtype),
            'non_null_count': col_data.count(),
            'null_count': col_data.isnull().sum(),
            'null_percentage': (col_data.isnull().sum() / len(col_data)) * 100,
            'unique_count': col_data.nunique(),
            'unique_percentage': (col_data.nunique() / len(col_data)) * 100
        }
        
        # تحليل الأعمدة الرقمية
        if col_data.dtype in ['int64', 'float64']:
            analysis.update({
                'statistics': {
                    'mean': col_data.mean(),
                    'median': col_data.median(),
                    'std': col_data.std(),
                    'min': col_data.min(),
                    'max': col_data.max(),
                    'q25': col_data.quantile(0.25),
                    'q75': col_data.quantile(0.75),
                    'skewness': col_data.skew(),
                    'kurtosis': col_data.kurtosis()
                },
                'outliers': self._detect_outliers(col_data)
            })
        
        # تحليل الأعمدة النصية
        elif col_data.dtype == 'object':
            value_counts = col_data.value_counts().head(10)
            analysis.update({
                'top_values': value_counts.to_dict(),
                'avg_length': col_data.astype(str).str.len().mean(),
                'max_length': col_data.astype(str).str.len().max(),
                'min_length': col_data.astype(str).str.len().min()
            })
        
        # تحليل أعمدة التاريخ
        elif col_data.dtype == 'datetime64[ns]':
            analysis.update({
                'date_range': {
                    'min_date': col_data.min(),
                    'max_date': col_data.max(),
                    'span_days': (col_data.max() - col_data.min()).days
                },
                'temporal_patterns': self._analyze_temporal_patterns(col_data)
            })
        
        return analysis
    
    def _detect_outliers(self, data: pd.Series) -> Dict:
        """كشف القيم الشاذة"""
        Q1 = data.quantile(0.25)
        Q3 = data.quantile(0.75)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = data[(data < lower_bound) | (data > upper_bound)]
        
        return {
            'count': len(outliers),
            'percentage': (len(outliers) / len(data)) * 100,
            'lower_bound': lower_bound,
            'upper_bound': upper_bound,
            'outlier_values': outliers.tolist()[:20]  # أول 20 قيمة شاذة
        }
    
    def _analyze_temporal_patterns(self, date_data: pd.Series) -> Dict:
        """تحليل الأنماط الزمنية"""
        try:
            # إنشاء DataFrame مؤقت للتحليل
            temp_df = pd.DataFrame({'date': date_data})
            temp_df['year'] = temp_df['date'].dt.year
            temp_df['month'] = temp_df['date'].dt.month
            temp_df['day_of_week'] = temp_df['date'].dt.dayofweek
            temp_df['hour'] = temp_df['date'].dt.hour
            
            patterns = {
                'yearly_distribution': temp_df['year'].value_counts().to_dict(),
                'monthly_distribution': temp_df['month'].value_counts().to_dict(),
                'weekly_distribution': temp_df['day_of_week'].value_counts().to_dict(),
                'hourly_distribution': temp_df['hour'].value_counts().to_dict()
            }
            
            return patterns
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الأنماط الزمنية: {str(e)}")
            return {}

    def advanced_analysis(self) -> Dict:
        """التحليل المتقدم باستخدام الذكاء الاصطناعي"""
        try:
            if self.processed_data is None:
                self.preprocess_data()

            analysis = {
                'clustering_analysis': self._perform_clustering(),
                'anomaly_detection': self._detect_anomalies(),
                'correlation_analysis': self._analyze_correlations(),
                'trend_analysis': self._analyze_trends(),
                'predictive_insights': self._generate_predictions()
            }

            self.analysis_results['advanced'] = analysis
            return analysis

        except Exception as e:
            logger.error(f"خطأ في التحليل المتقدم: {str(e)}")
            return {}

    def _perform_clustering(self) -> Dict:
        """تحليل التجميع (Clustering)"""
        try:
            # اختيار الأعمدة الرقمية فقط
            numeric_data = self.processed_data.select_dtypes(include=[np.number])

            if numeric_data.empty:
                return {'message': 'لا توجد بيانات رقمية للتجميع'}

            # تطبيع البيانات
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(numeric_data.fillna(0))

            # تطبيق K-Means
            kmeans = KMeans(n_clusters=5, random_state=42)
            clusters = kmeans.fit_predict(scaled_data)

            # تطبيق DBSCAN
            dbscan = DBSCAN(eps=0.5, min_samples=5)
            dbscan_clusters = dbscan.fit_predict(scaled_data)

            return {
                'kmeans': {
                    'n_clusters': 5,
                    'cluster_centers': kmeans.cluster_centers_.tolist(),
                    'cluster_distribution': pd.Series(clusters).value_counts().to_dict(),
                    'inertia': kmeans.inertia_
                },
                'dbscan': {
                    'n_clusters': len(set(dbscan_clusters)) - (1 if -1 in dbscan_clusters else 0),
                    'noise_points': sum(dbscan_clusters == -1),
                    'cluster_distribution': pd.Series(dbscan_clusters).value_counts().to_dict()
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل التجميع: {str(e)}")
            return {}

    def _detect_anomalies(self) -> Dict:
        """كشف الشذوذ باستخدام Isolation Forest"""
        try:
            numeric_data = self.processed_data.select_dtypes(include=[np.number])

            if numeric_data.empty:
                return {'message': 'لا توجد بيانات رقمية لكشف الشذوذ'}

            # تطبيق Isolation Forest
            iso_forest = IsolationForest(contamination=0.1, random_state=42)
            anomalies = iso_forest.fit_predict(numeric_data.fillna(0))

            anomaly_scores = iso_forest.decision_function(numeric_data.fillna(0))

            return {
                'total_anomalies': sum(anomalies == -1),
                'anomaly_percentage': (sum(anomalies == -1) / len(anomalies)) * 100,
                'anomaly_scores': {
                    'mean': np.mean(anomaly_scores),
                    'std': np.std(anomaly_scores),
                    'min': np.min(anomaly_scores),
                    'max': np.max(anomaly_scores)
                },
                'anomaly_indices': np.where(anomalies == -1)[0].tolist()[:50]  # أول 50 شذوذ
            }

        except Exception as e:
            logger.error(f"خطأ في كشف الشذوذ: {str(e)}")
            return {}

    def _analyze_correlations(self) -> Dict:
        """تحليل الارتباطات بين المتغيرات"""
        try:
            numeric_data = self.processed_data.select_dtypes(include=[np.number])

            if numeric_data.empty:
                return {'message': 'لا توجد بيانات رقمية لتحليل الارتباط'}

            # حساب مصفوفة الارتباط
            correlation_matrix = numeric_data.corr()

            # العثور على أقوى الارتباطات
            strong_correlations = []
            for i in range(len(correlation_matrix.columns)):
                for j in range(i+1, len(correlation_matrix.columns)):
                    corr_value = correlation_matrix.iloc[i, j]
                    if abs(corr_value) > 0.5:  # ارتباط قوي
                        strong_correlations.append({
                            'variable1': correlation_matrix.columns[i],
                            'variable2': correlation_matrix.columns[j],
                            'correlation': corr_value,
                            'strength': 'قوي' if abs(corr_value) > 0.7 else 'متوسط'
                        })

            return {
                'correlation_matrix': correlation_matrix.to_dict(),
                'strong_correlations': strong_correlations,
                'correlation_summary': {
                    'max_correlation': correlation_matrix.max().max(),
                    'min_correlation': correlation_matrix.min().min(),
                    'avg_correlation': correlation_matrix.mean().mean()
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل الارتباطات: {str(e)}")
            return {}

    def _analyze_trends(self) -> Dict:
        """تحليل الاتجاهات الزمنية"""
        try:
            date_columns = self.processed_data.select_dtypes(include=['datetime64']).columns
            numeric_columns = self.processed_data.select_dtypes(include=[np.number]).columns

            if len(date_columns) == 0 or len(numeric_columns) == 0:
                return {'message': 'لا توجد بيانات زمنية أو رقمية كافية لتحليل الاتجاهات'}

            trends = {}

            # تحليل الاتجاه لكل متغير رقمي مع الزمن
            for date_col in date_columns[:1]:  # أول عمود تاريخ
                for num_col in numeric_columns[:5]:  # أول 5 أعمدة رقمية
                    try:
                        # إنشاء سلسلة زمنية
                        temp_df = self.processed_data[[date_col, num_col]].dropna()
                        temp_df = temp_df.sort_values(date_col)

                        # حساب الاتجاه باستخدام الانحدار الخطي
                        x = np.arange(len(temp_df))
                        y = temp_df[num_col].values

                        slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)

                        trends[f"{num_col}_trend"] = {
                            'slope': slope,
                            'r_squared': r_value**2,
                            'p_value': p_value,
                            'trend_direction': 'صاعد' if slope > 0 else 'هابط',
                            'trend_strength': 'قوي' if abs(r_value) > 0.7 else 'متوسط' if abs(r_value) > 0.3 else 'ضعيف'
                        }

                    except Exception as e:
                        logger.warning(f"لا يمكن تحليل الاتجاه للعمود {num_col}: {str(e)}")

            return trends

        except Exception as e:
            logger.error(f"خطأ في تحليل الاتجاهات: {str(e)}")
            return {}

    def _generate_predictions(self) -> Dict:
        """توليد تنبؤات باستخدام التعلم الآلي"""
        try:
            numeric_data = self.processed_data.select_dtypes(include=[np.number])

            if len(numeric_data.columns) < 2:
                return {'message': 'لا توجد بيانات كافية للتنبؤ'}

            predictions = {}

            # اختيار متغير تابع (أول عمود رقمي)
            target_column = numeric_data.columns[0]
            feature_columns = numeric_data.columns[1:6]  # أول 5 متغيرات مستقلة

            # إعداد البيانات
            X = numeric_data[feature_columns].fillna(0)
            y = numeric_data[target_column].fillna(0)

            if len(X) < 10:  # نحتاج بيانات كافية
                return {'message': 'البيانات غير كافية للتنبؤ'}

            # تقسيم البيانات
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            # تدريب نموذج Random Forest
            rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
            rf_model.fit(X_train, y_train)

            # التنبؤ
            y_pred = rf_model.predict(X_test)

            # تقييم النموذج
            mse = mean_squared_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)

            # أهمية المتغيرات
            feature_importance = dict(zip(feature_columns, rf_model.feature_importances_))

            predictions = {
                'target_variable': target_column,
                'model_performance': {
                    'mse': mse,
                    'rmse': np.sqrt(mse),
                    'r2_score': r2,
                    'accuracy_percentage': max(0, r2 * 100)
                },
                'feature_importance': feature_importance,
                'top_features': sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:3]
            }

            return predictions

        except Exception as e:
            logger.error(f"خطأ في توليد التنبؤات: {str(e)}")
            return {}
