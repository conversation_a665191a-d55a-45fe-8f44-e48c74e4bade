{% extends "base.html" %}

{% block title %}نظام مطابقة أرصدة الموردين{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">
<style>
    .reconciliation-status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .status-matched { background-color: #28a745; color: #fff; }
    .status-unmatched { background-color: #dc3545; color: #fff; }
    .status-pending { background-color: #ffc107; color: #000; }
    .status-approved { background-color: #17a2b8; color: #fff; }
    .status-adjusted { background-color: #fd7e14; color: #fff; }
    
    .cycle-status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .cycle-open { background-color: #6c757d; color: #fff; }
    .cycle-in-progress { background-color: #007bff; color: #fff; }
    .cycle-completed { background-color: #28a745; color: #fff; }
    .cycle-cancelled { background-color: #dc3545; color: #fff; }
    
    .difference-amount {
        font-family: 'Courier New', monospace;
        font-weight: bold;
    }
    
    .difference-positive { color: #28a745; }
    .difference-negative { color: #dc3545; }
    .difference-zero { color: #6c757d; }
    
    .stats-card {
        border-left: 4px solid;
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-card.cycles { border-left-color: #007bff; }
    .stats-card.matched { border-left-color: #28a745; }
    .stats-card.unmatched { border-left-color: #dc3545; }
    .stats-card.differences { border-left-color: #ffc107; }
    
    .balance-comparison {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
    }
    
    .balance-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .balance-row:last-child {
        border-bottom: none;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">🔄 نظام مطابقة أرصدة الموردين</h2>
                    <p class="text-muted mb-0">مطابقة شاملة لأرصدة الموردين مع كشوفات الحساب الخارجية</p>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCycleModal">
                        <i class="fas fa-plus"></i> دورة مطابقة جديدة
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card cycles">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">دورات المطابقة</h6>
                            <h3 class="mb-0" id="totalCycles">-</h3>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-sync fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card matched">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">متطابقة</h6>
                            <h3 class="mb-0" id="matchedCount">-</h3>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card unmatched">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">غير متطابقة</h6>
                            <h3 class="mb-0" id="unmatchedCount">-</h3>
                        </div>
                        <div class="text-danger">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card differences">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">إجمالي الفروقات</h6>
                            <h3 class="mb-0" id="totalDifferences">-</h3>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-balance-scale fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs Navigation -->
    <ul class="nav nav-tabs mb-4" id="reconciliationTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="cycles-tab" data-bs-toggle="tab" data-bs-target="#cycles" 
                    type="button" role="tab">
                <i class="fas fa-sync"></i> دورات المطابقة
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="reconciliations-tab" data-bs-toggle="tab" data-bs-target="#reconciliations" 
                    type="button" role="tab">
                <i class="fas fa-list"></i> المطابقات التفصيلية
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports" 
                    type="button" role="tab">
                <i class="fas fa-chart-bar"></i> التقارير
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="reconciliationTabContent">
        <!-- Cycles Tab -->
        <div class="tab-pane fade show active" id="cycles" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">📋 دورات المطابقة</h6>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="cycleStatusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="OPEN">مفتوحة</option>
                                <option value="IN_PROGRESS">قيد التنفيذ</option>
                                <option value="COMPLETED">مكتملة</option>
                                <option value="CANCELLED">ملغية</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="cycleDateFromFilter">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="cycleDateToFilter">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-primary d-block w-100" onclick="applyCycleFilters()">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                    
                    <!-- Cycles Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="cyclesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم الدورة</th>
                                    <th>تاريخ المطابقة</th>
                                    <th>الفترة</th>
                                    <th>النوع</th>
                                    <th>الحالة</th>
                                    <th>الموردين</th>
                                    <th>المتطابق</th>
                                    <th>غير المتطابق</th>
                                    <th>إجمالي الفروقات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تحميل البيانات عبر AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reconciliations Tab -->
        <div class="tab-pane fade" id="reconciliations" role="tabpanel">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">📊 المطابقات التفصيلية</h6>
                    <div>
                        <select class="form-select" id="selectedCycleFilter" onchange="loadCycleReconciliations()">
                            <option value="">اختر دورة المطابقة</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="reconciliationsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>المورد</th>
                                    <th>العملة</th>
                                    <th>رصيد النظام</th>
                                    <th>رصيد المورد</th>
                                    <th>الفرق</th>
                                    <th>الحالة</th>
                                    <th>عدد الفروقات</th>
                                    <th>تاريخ كشف المورد</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تحميل البيانات عبر AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports Tab -->
        <div class="tab-pane fade" id="reports" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">📈 تقارير المطابقة</h6>
                </div>
                <div class="card-body">
                    <div id="reportsContent">
                        <div class="text-center py-5">
                            <i class="fas fa-chart-bar fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">تقارير المطابقة</h4>
                            <p class="text-muted">سيتم عرض التقارير والإحصائيات هنا</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء دورة مطابقة جديدة -->
<div class="modal fade" id="createCycleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">🔄 إنشاء دورة مطابقة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createCycleForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">اسم الدورة *</label>
                            <input type="text" class="form-control" id="cycleName" required 
                                   placeholder="مثال: مطابقة ديسمبر 2024">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نوع الدورة</label>
                            <select class="form-select" id="cycleType">
                                <option value="MONTHLY">شهرية</option>
                                <option value="QUARTERLY">ربع سنوية</option>
                                <option value="YEARLY">سنوية</option>
                                <option value="ADHOC">خاصة</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">من تاريخ *</label>
                            <input type="date" class="form-control" id="periodFrom" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">إلى تاريخ *</label>
                            <input type="date" class="form-control" id="periodTo" required>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="cycleNotes" rows="3" 
                                      placeholder="أي ملاحظات إضافية حول دورة المطابقة..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="createReconciliationCycle()">
                    <i class="fas fa-save"></i> إنشاء الدورة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تفاصيل المطابقة -->
<div class="modal fade" id="reconciliationDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📋 تفاصيل المطابقة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="reconciliationDetailsContent">
                <!-- سيتم تحميل التفاصيل عبر AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-success" onclick="approveReconciliation()">
                    <i class="fas fa-check"></i> اعتماد المطابقة
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

<script>
let cyclesTable;
let reconciliationsTable;
let currentCycleId = null;
let currentReconciliationId = null;

$(document).ready(function() {
    initializeDataTables();
    loadInitialData();
    setupEventHandlers();
});

function initializeDataTables() {
    cyclesTable = $('#cyclesTable').DataTable({
        responsive: true,
        pageLength: 25,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        order: [[1, 'desc']], // ترتيب حسب تاريخ المطابقة
        columnDefs: [
            { targets: [9], orderable: false } // عمود الإجراءات غير قابل للترتيب
        ]
    });
    
    reconciliationsTable = $('#reconciliationsTable').DataTable({
        responsive: true,
        pageLength: 25,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        order: [[4, 'desc']], // ترتيب حسب الفرق
        columnDefs: [
            { targets: [8], orderable: false } // عمود الإجراءات غير قابل للترتيب
        ]
    });
}

function loadInitialData() {
    loadReconciliationSummary();
    loadReconciliationCycles();
    loadCycleOptions();
}

function setupEventHandlers() {
    // تحديث التواريخ عند تغيير نوع الدورة
    $('#cycleType').on('change', function() {
        updatePeriodDates($(this).val());
    });
}

// سيتم إضافة باقي الدوال في ملف JavaScript منفصل...
</script>

<!-- تحميل ملف JavaScript منفصل للمطابقة -->
<script src="{{ url_for('static', filename='js/reconciliation_system.js') }}"></script>
{% endblock %}
