# -*- coding: utf-8 -*-
"""
مسارات أوامر الشراء
Purchase Orders Routes
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app, send_file, session
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app.purchase_orders import bp
from oracle_manager import get_oracle_manager, OracleManager
from datetime import datetime
import logging
import os
import mimetypes

logger = logging.getLogger(__name__)

# استيراد مدير الروابط السحابية
try:
    from app.shipments.cloud_link_manager import CloudLinkManager
    cloud_link_manager = CloudLinkManager()
    CLOUD_SERVICES_AVAILABLE = True
    logger.info("✅ تم تحميل مدير الروابط السحابية")
except ImportError as e:
    cloud_link_manager = None
    CLOUD_SERVICES_AVAILABLE = False
    logger.warning(f"⚠️ مدير الروابط السحابية غير متوفر: {e}")

def _create_direct_download_link(file_name: str, document_id: int) -> str:
    """إنشاء رابط تحميل مباشر من النظام"""
    try:
        import hashlib
        import time

        # إنشاء معرف فريد للملف
        unique_string = f"{document_id}_{file_name}_{int(time.time())}"
        file_hash = hashlib.sha256(unique_string.encode()).hexdigest()[:16]

        # إنشاء رابط تحميل مباشر
        base_url = "https://saserp.alfogehi.net:5000"
        share_link = f"{base_url}/purchase-orders/shared/download/{file_hash}"

        logger.info(f"✅ تم إنشاء رابط تحميل مباشر: {share_link}")
        return share_link

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء رابط التحميل المباشر: {e}")
        return None

def _create_cloud_link(file_path: str, file_name: str, service: str) -> str:
    """إنشاء رابط سحابي باستخدام cloud_link_manager"""
    try:
        if not CLOUD_SERVICES_AVAILABLE or not cloud_link_manager:
            logger.warning("⚠️ مدير الروابط السحابية غير متوفر")
            return None

        result = cloud_link_manager.create_share_link(file_path, file_name, service)

        if result and result.get('success'):
            share_link = result.get('share_link')
            logger.info(f"✅ تم إنشاء رابط {service}: {share_link}")
            return share_link
        elif result and result.get('error'):
            logger.warning(f"⚠️ خطأ في إنشاء رابط {service}: {result.get('error')}")
            return None
        else:
            logger.warning(f"⚠️ فشل في إنشاء رابط {service}")
            return None

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء رابط {service}: {e}")
        return None

@bp.route('/api/branches')
@login_required
def api_branches():
    """API لجلب قائمة الفروع"""
    try:
        oracle_manager = get_oracle_manager()

        branches_query = """
        SELECT BRN_NO, BRN_LNAME, BRN_FNAME, BRN_LADD
        FROM BRANCHES
        WHERE NVL(IS_ACTIVE, 1) = 1
        ORDER BY BRN_LNAME
        """

        branches_result = oracle_manager.execute_query(branches_query)

        if branches_result:
            branches = [
                {
                    'brn_no': branch[0],
                    'brn_lname': branch[1],
                    'brn_fname': branch[2] or '',
                    'brn_ladd': branch[3] or ''
                }
                for branch in branches_result
            ]
        else:
            # فرع افتراضي
            branches = [
                {
                    'brn_no': 21,
                    'brn_lname': 'الفرع الرئيسي',
                    'brn_fname': 'Main Branch',
                    'brn_ladd': ''
                }
            ]

        return jsonify(branches)

    except Exception as e:
        logger.error(f"❌ خطأ في جلب الفروع: {e}")
        return jsonify([
            {
                'brn_no': 21,
                'brn_lname': 'الفرع الرئيسي',
                'brn_fname': 'Main Branch',
                'brn_ladd': ''
            }
        ])

@bp.route('/')
@login_required
def index():
    """صفحة قائمة أوامر الشراء"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب أوامر الشراء مع حساب الحالة تلقائياً من الشحنات الفعلية
        query = """
        SELECT po.ID, po.PO_NUMBER, po.SUPPLIER_NAME, po.PO_DATE,
               po.DELIVERY_DATE, po.STATUS, po.TOTAL_AMOUNT, po.CURRENCY,
               CASE
                   WHEN cs.shipment_count > 0 THEN 1
                   ELSE 0
               END as IS_USED,
               cs.first_used_at as USED_AT,
               cs.first_shipment_id as USED_IN_SHIPMENT_ID,
               c.CONTRACT_NUMBER, c.SUPPLIER_NAME as CONTRACT_SUPPLIER,
               curr.SYMBOL as CURRENCY_SYMBOL, curr.NAME_AR as CURRENCY_NAME
        FROM PURCHASE_ORDERS po
        LEFT JOIN CONTRACTS c ON po.CONTRACT_ID = c.CONTRACT_ID
        LEFT JOIN CURRENCIES curr ON po.CURRENCY = curr.CODE
        LEFT JOIN (
            SELECT purchase_order_id,
                   COUNT(*) as shipment_count,
                   MIN(created_at) as first_used_at,
                   MIN(id) as first_shipment_id
            FROM cargo_shipments
            WHERE purchase_order_id IS NOT NULL
            GROUP BY purchase_order_id
        ) cs ON po.ID = cs.purchase_order_id
        ORDER BY po.CREATED_AT DESC
        """

        purchase_orders = oracle_manager.execute_query(query, [])

        # تحديث حالة أوامر الشراء بناءً على أحدث حالة شحنة
        updated_purchase_orders = []
        for po in purchase_orders:
            po_list = list(po)  # تحويل tuple إلى list للتعديل
            po_id = po_list[0]

            # جلب أحدث حالة شحنة لهذا الأمر
            shipment_status_query = """
                SELECT ssc.status_name_ar
                FROM cargo_shipments cs
                LEFT JOIN shipment_status_config ssc ON cs.shipment_status = ssc.status_code
                WHERE cs.purchase_order_id = :1
                ORDER BY cs.status_updated_at DESC, cs.created_at DESC
                FETCH FIRST 1 ROWS ONLY
            """

            shipment_status_result = oracle_manager.execute_query(shipment_status_query, [po_id])

            if shipment_status_result and shipment_status_result[0][0]:
                # تحديث حالة أمر الشراء بحالة الشحنة
                po_list[5] = shipment_status_result[0][0]  # STATUS في الموضع 5

            updated_purchase_orders.append(tuple(po_list))

        # حساب الإحصائيات
        stats = {
            'total_count': len(updated_purchase_orders),
            'draft_count': 0,
            'sent_count': 0,
            'confirmed_count': 0,
            'cancelled_count': 0,
            'used_count': 0,
            'total_amount': 0,
            'currencies': {}
        }

        for po in updated_purchase_orders:
            # حساب الحالات
            status = po[5] if po[5] else 'مسودة'
            if status in ['مسودة', 'DRAFT']:
                stats['draft_count'] += 1
            elif status in ['مرسل', 'SENT']:
                stats['sent_count'] += 1
            elif status in ['مؤكد', 'CONFIRMED']:
                stats['confirmed_count'] += 1
            elif status in ['ملغي', 'CANCELLED']:
                stats['cancelled_count'] += 1

            # حساب المستخدمة
            if po[8]:  # IS_USED
                stats['used_count'] += 1

            # حساب المبالغ حسب العملة
            amount = float(po[6]) if po[6] else 0
            currency = po[7] if po[7] else 'SAR'
            currency_symbol = po[13] if len(po) > 13 and po[13] else 'ر.س'

            if currency not in stats['currencies']:
                stats['currencies'][currency] = {
                    'amount': 0,
                    'symbol': currency_symbol,
                    'count': 0
                }

            stats['currencies'][currency]['amount'] += amount
            stats['currencies'][currency]['count'] += 1
            stats['total_amount'] += amount

        return render_template('purchase_orders/index_simple.html',
                             purchase_orders=updated_purchase_orders,
                             stats=stats)

    except Exception as e:
        logger.error(f"❌ خطأ في جلب أوامر الشراء: {e}")
        flash('خطأ في جلب أوامر الشراء', 'error')
        return render_template('purchase_orders/index.html',
                             purchase_orders=[])


@bp.route('/new')
@bp.route('/new/<int:contract_id>')
@login_required
def new(contract_id=None):
    """إنشاء أمر شراء جديد"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب قائمة العقود المتاحة للاستخدام (استثناء المستخدمة والمنفذة كلياً)
        contracts_query = """
        SELECT CONTRACT_ID, CONTRACT_NUMBER, SUPPLIER_NAME, CONTRACT_AMOUNT, SUPPLIER_ID
        FROM CONTRACTS
        WHERE IS_ACTIVE = 1
        AND NOT (IS_USED = 1 AND CONTRACT_STATUS IN ('FULLY_EXECUTED', 'منفذ كليا', 'COMPLETED'))
        ORDER BY CONTRACT_NUMBER DESC
        """
        contracts = oracle_manager.execute_query(contracts_query, [])

        # جلب قائمة الموردين النشطين
        suppliers_query = """
        SELECT ID, NAME_AR, CONTACT_PERSON, PHONE, EMAIL
        FROM SUPPLIERS
        WHERE IS_ACTIVE = 1
        ORDER BY NAME_AR
        """
        suppliers = oracle_manager.execute_query(suppliers_query, [])

        # جلب قائمة العملات النشطة
        currencies_query = """
        SELECT ID, CODE, NAME_AR, NAME_EN, SYMBOL, IS_BASE_CURRENCY
        FROM CURRENCIES
        WHERE IS_ACTIVE = 1
        ORDER BY IS_BASE_CURRENCY DESC, NAME_AR
        """
        currencies = oracle_manager.execute_query(currencies_query, [])

        # جلب قائمة الفروع النشطة
        branches_query = """
        SELECT BRN_NO, BRN_LNAME, BRN_FNAME
        FROM BRANCHES
        WHERE NVL(IS_ACTIVE, 1) = 1
        ORDER BY BRN_LNAME
        """
        branches_result = oracle_manager.execute_query(branches_query, [])

        if branches_result:
            branches = [
                {
                    'brn_no': branch[0],
                    'brn_lname': branch[1],
                    'brn_fname': branch[2] or ''
                }
                for branch in branches_result
            ]
        else:
            # فرع افتراضي
            branches = [
                {
                    'brn_no': 21,
                    'brn_lname': 'الفرع الرئيسي',
                    'brn_fname': 'Main Branch'
                }
            ]



        # إذا تم تمرير contract_id، جلب بيانات العقد (اختياري)
        contract_data = None
        if contract_id:
            contract_query = """
            SELECT CONTRACT_ID, CONTRACT_NUMBER, SUPPLIER_NAME, SUPPLIER_ID
            FROM CONTRACTS
            WHERE CONTRACT_ID = :1
            """
            contract_result = oracle_manager.execute_query(contract_query, [contract_id])
            if contract_result:
                contract_data = contract_result[0]

        return render_template('purchase_orders/new.html',
                             contracts=contracts,
                             suppliers=suppliers,
                             currencies=currencies,
                             branches=branches,
                             contract_data=contract_data)

    except Exception as e:
        logger.error(f"❌ خطأ في صفحة إنشاء أمر الشراء: {e}")
        flash('خطأ في تحميل الصفحة', 'error')
        return redirect(url_for('purchase_orders.index'))

@bp.route('/api/contract/<int:contract_id>')
@login_required
def get_contract_details(contract_id):
    """جلب تفاصيل العقد مع الأصناف"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب بيانات العقد الأساسية مع بيانات المورد من جدول الموردين
        contract_query = """
        SELECT c.CONTRACT_ID, c.BRANCH_ID, c.CONTRACT_DATE, c.START_DATE, c.END_DATE,
               c.SUPPLIER_ID, s.SUPPLIER_CODE, s.NAME_AR as SUPPLIER_NAME,
               c.CURRENCY_CODE, c.EXCHANGE_RATE, c.REFERENCE_NUMBER, c.CONTRACT_AMOUNT, c.DESCRIPTION
        FROM CONTRACTS c
        LEFT JOIN SUPPLIERS s ON c.SUPPLIER_ID = s.ID
        WHERE c.CONTRACT_ID = :1
        """

        contract_result = oracle_manager.execute_query(contract_query, [contract_id])

        if not contract_result:
            return jsonify({
                'success': False,
                'message': 'العقد غير موجود'
            })

        contract = contract_result[0]

        # جلب تفاصيل أصناف العقد
        details_query = """
        SELECT ITEM_ID, ITEM_NAME, QUANTITY, FREE_QUANTITY, UNIT_NAME,
               UNIT_PRICE, DISCOUNT_PERCENTAGE, TAX_AMOUNT, LINE_TOTAL,
               PRODUCTION_DATE, EXPIRY_DATE
        FROM CONTRACT_DETAILS
        WHERE CONTRACT_ID = :1
        ORDER BY DETAIL_ID
        """

        details_result = oracle_manager.execute_query(details_query, [contract_id])

        # تنسيق البيانات
        contract_data = {
            'id': contract[0],
            'branch_id': contract[1],
            'contract_date': contract[2].strftime('%Y-%m-%d') if contract[2] else None,
            'start_date': contract[3].strftime('%Y-%m-%d') if contract[3] else None,
            'end_date': contract[4].strftime('%Y-%m-%d') if contract[4] else None,
            'supplier_id': contract[5],
            'supplier_code': contract[6],  # كود المورد من جدول الموردين
            'supplier_name': contract[7],  # اسم المورد من جدول الموردين
            'currency_code': contract[8],
            'exchange_rate': float(contract[9]) if contract[9] else 1.0,
            'reference_number': contract[10],
            'contract_amount': float(contract[11]) if contract[11] else 0.0,
            'description': contract[12]
        }

        # تنسيق تفاصيل الأصناف
        items = []
        if details_result:
            for detail in details_result:
                # تنسيق التواريخ
                production_date = detail[9].strftime('%Y-%m-%d') if detail[9] else None
                expiry_date = detail[10].strftime('%Y-%m-%d') if detail[10] else None

                # إضافة logging للتحقق من التواريخ
                logger.info(f"📅 معالجة الصنف {detail[1]}: production_date={production_date}, expiry_date={expiry_date}")

                item = {
                    'item_id': detail[0],
                    'item_name': detail[1],
                    'quantity': float(detail[2]) if detail[2] else 0.0,
                    'free_quantity': float(detail[3]) if detail[3] else 0.0,
                    'unit_name': detail[4],
                    'unit_price': float(detail[5]) if detail[5] else 0.0,
                    'discount_percentage': float(detail[6]) if detail[6] else 0.0,
                    'tax_amount': float(detail[7]) if detail[7] else 0.0,
                    'line_total': float(detail[8]) if detail[8] else 0.0,
                    'production_date': production_date,
                    'expiry_date': expiry_date
                }
                items.append(item)

        return jsonify({
            'success': True,
            'contract': contract_data,
            'items': items
        })

    except Exception as e:
        logger.error(f"خطأ في جلب تفاصيل العقد {contract_id}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب تفاصيل العقد: {str(e)}'
        })

@bp.route('/api/save', methods=['POST'])
@login_required
def save_purchase_order():
    """حفظ أمر شراء جديد"""
    try:
        oracle_manager = get_oracle_manager()

        # استلام البيانات
        data = request.get_json()

        # تشخيص: طباعة البيانات المستلمة
        logger.info(f"🔍 البيانات المستلمة: {data}")
        logger.info(f"📋 كود المورد: {data.get('supplier_code')}")
        logger.info(f"📋 اسم المورد: {data.get('supplier_name')}")

        # توليد رقم أمر الشراء
        po_number = generate_po_number()

        # حفظ أمر الشراء مع حقل الفرع
        insert_query = """
        INSERT INTO PURCHASE_ORDERS (
            PO_NUMBER, CONTRACT_ID, SUPPLIER_CODE, SUPPLIER_NAME, TITLE,
            PO_DATE, DELIVERY_DATE, DELIVERY_ADDRESS, PAYMENT_TERMS,
            SUPPLIER_INVOICE_NUMBER, SHIPPING_COST, CLEARANCE_COST, CURRENCY,
            PRIORITY, EXPECTED_DELIVERY_DAYS, DESCRIPTION, NOTES, STATUS,
            SUBTOTAL, TOTAL_DISCOUNT, TOTAL_AMOUNT, GROSS_AMOUNT, DISCOUNT_AMOUNT,
            NET_AMOUNT, BRANCH_ID, CREATED_BY
        ) VALUES (
            :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15, :16, :17, :18, :19, :20, :21, :22, :23, :24, :25, :26
        )
        """

        # التأكد من وجود بيانات المورد
        supplier_code = data.get('supplier_code') or ''
        supplier_name = data.get('supplier_name') or ''

        # إذا لم يكن هناك كود مورد، لا نحفظ None
        if not supplier_code or supplier_code.lower() == 'none':
            supplier_code = ''

        if not supplier_name or supplier_name.lower() == 'none':
            supplier_name = ''

        # استخراج الحقول المالية المهمة
        gross_amount = float(data.get('gross_amount', 0))
        discount_amount = float(data.get('discount_amount', 0))
        shipping_cost = float(data.get('shipping_cost', 0))
        clearance_cost = float(data.get('clearance_cost', 0))

        # حساب صافي المبلغ بعد الخصم
        net_amount_after_discount = gross_amount - discount_amount

        # حساب المبلغ الإجمالي النهائي (يشمل أجور الشحن والتخليص)
        final_total_amount = net_amount_after_discount + shipping_cost + clearance_cost

        # تشخيص: طباعة الحقول المالية
        logger.info(f"💰 الحقول المالية المحسوبة:")
        logger.info(f"   المبلغ الإجمالي (gross_amount): {gross_amount}")
        logger.info(f"   مبلغ الخصم (discount_amount): {discount_amount}")
        logger.info(f"   أجور الشحن (shipping_cost): {shipping_cost}")
        logger.info(f"   أجور التخليص (clearance_cost): {clearance_cost}")
        logger.info(f"   صافي المبلغ بعد الخصم: {net_amount_after_discount}")
        logger.info(f"   المبلغ الإجمالي النهائي: {final_total_amount}")

        params = [
            po_number,
            data.get('contract_id') if data.get('contract_id') else None,  # العقد اختياري
            supplier_code,  # كود المورد النصي
            supplier_name,  # اسم المورد
            data.get('title'),
            datetime.strptime(data.get('po_date'), '%Y-%m-%d') if data.get('po_date') else datetime.now(),
            datetime.strptime(data.get('delivery_date'), '%Y-%m-%d') if data.get('delivery_date') else None,
            data.get('delivery_address'),
            data.get('payment_terms'),
            data.get('supplier_invoice_number'),  # رقم فاتورة المورد
            float(data.get('shipping_cost', 0)),  # أجور الشحن
            float(data.get('clearance_cost', 0)),  # أجور التخليص
            data.get('currency', 'ريال'),
            data.get('priority', 'عادي'),
            data.get('expected_delivery_days'),
            data.get('description'),
            data.get('notes'),
            data.get('status', 'مسودة'),
            data.get('subtotal', 0),
            data.get('total_discount', 0),
            final_total_amount,  # المبلغ الإجمالي النهائي (يشمل الشحن والتخليص)
            gross_amount,  # المبلغ الإجمالي للأصناف
            discount_amount,  # مبلغ الخصم
            final_total_amount,  # صافي المبلغ النهائي
            int(data.get('branch_id', 21)),  # الفرع (افتراضي 21)
            current_user.username
        ]

        oracle_manager.execute_update(insert_query, params)
        oracle_manager.commit()

        # جلب ID أمر الشراء المحفوظ
        po_id_query = "SELECT ID FROM PURCHASE_ORDERS WHERE PO_NUMBER = :1"
        po_id_result = oracle_manager.execute_query(po_id_query, [po_number])
        po_id = po_id_result[0][0] if po_id_result else None

        # حفظ أصناف أمر الشراء
        if po_id and data.get('items'):
            for item in data['items']:
                item_insert = """
                INSERT INTO PO_ITEMS (
                    PO_ID, ITEM_CODE, ITEM_NAME, UNIT,
                    PRODUCTION_DATE, EXPIRY_DATE, QUANTITY, UNIT_PRICE,
                    TOTAL_PRICE
                ) VALUES (
                    :1, :2, :3, :4, :5, :6, :7, :8, :9
                )
                """

                item_params = [
                    po_id,
                    item.get('code'),
                    item['name'],
                    item.get('unit'),
                    datetime.strptime(item.get('production_date'), '%Y-%m-%d') if item.get('production_date') else None,
                    datetime.strptime(item.get('expiry_date'), '%Y-%m-%d') if item.get('expiry_date') else None,
                    item['quantity'],
                    item['price'],
                    item.get('total_price', 0)
                ]

                oracle_manager.execute_update(item_insert, item_params)

        oracle_manager.commit()

        # ترحيل المعاملة المحاسبية باستخدام BT_PKG
        if po_id and supplier_code and final_total_amount > 0:
            try:
                # ترحيل معاملة للمورد (دائن)
                bt_query = """
                BEGIN
                    BT_PKG.POST_TXN(
                        p_ent_type => 'SUPPLIER',
                        p_ent_id => :1,
                        p_doc_type => 'PURCHASE_ORDER',
                        p_doc_no => :2,
                        p_doc_date => SYSDATE,
                        p_curr => :3,
                        p_dr => 0,
                        p_cr => :4,
                        p_rate => 1,
                        p_desc => :5,
                        p_branch => :6,
                        p_user => :7
                    );
                END;
                """

                bt_params = [
                    supplier_code,  # معرف المورد
                    po_number,      # رقم المستند
                    data.get('currency', 'SAR'),  # العملة
                    final_total_amount,  # المبلغ (دائن للمورد)
                    f'أمر شراء رقم {po_number}',  # الوصف
                    int(data.get('branch_id', 21)),  # الفرع
                    current_user.id if hasattr(current_user, 'id') else 1  # المستخدم
                ]

                oracle_manager.execute_update(bt_query, bt_params)
                oracle_manager.commit()
                logger.info(f"✅ تم ترحيل المعاملة المحاسبية لأمر الشراء {po_number}")

            except Exception as e:
                logger.warning(f"⚠️ خطأ في ترحيل المعاملة المحاسبية: {e}")
                # لا نوقف العملية في حالة فشل الترحيل المحاسبي

        # تحديث حالة العقد إلى "مُستخدم" إذا تم ربط أمر الشراء بعقد
        contract_id = data.get('contract_id')
        if contract_id:
            try:
                update_contract_query = "UPDATE CONTRACTS SET IS_USED = 1 WHERE CONTRACT_ID = :1"
                oracle_manager.execute_update(update_contract_query, [contract_id])
                oracle_manager.commit()
                logger.info(f"✅ تم تحديث العقد {contract_id} إلى حالة 'مُستخدم'")

                # تحديث حالة تنفيذ العقد (الكميات المنفذة والمتبقية)
                from contract_execution_utils import update_contract_execution_status
                execution_result = update_contract_execution_status(contract_id, oracle_manager)
                if execution_result['success']:
                    logger.info(f"✅ تم تحديث حالة تنفيذ العقد {contract_id}: {execution_result['contract_status_ar']}")
                else:
                    logger.warning(f"⚠️ خطأ في تحديث حالة تنفيذ العقد {contract_id}: {execution_result['message']}")

            except Exception as e:
                logger.warning(f"⚠️ خطأ في تحديث حالة العقد {contract_id}: {e}")

        return jsonify({
            'success': True,
            'message': 'تم حفظ أمر الشراء بنجاح',
            'po_number': po_number,
            'po_id': po_id
        })

    except Exception as e:
        logger.error(f"❌ خطأ في حفظ أمر الشراء: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في حفظ أمر الشراء: {str(e)}'
        })

def generate_po_number():
    """توليد رقم أمر شراء جديد باستخدام النظام الذكي"""
    try:
        oracle_manager = get_oracle_manager()
        
        # استخدام الدالة الذكية الجديدة
        result = oracle_manager.execute_query("SELECT GENERATE_SMART_PO_NUMBER() FROM DUAL")
        
        if result and result[0][0]:
            po_number = result[0][0]
            
            # تسجيل العملية
            try:
                log_query = """
                INSERT INTO PO_NUMBER_LOG (
                    id, new_po_number, operation_type, year_context, notes
                ) VALUES (
                    PO_NUMBER_LOG_SEQ.NEXTVAL, :1, 'GENERATE', 
                    EXTRACT(YEAR FROM SYSDATE), 'تم توليد رقم ذكي'
                )
                """
                oracle_manager.execute_update(log_query, [po_number])
            except:
                pass  # تجاهل أخطاء التسجيل
            
            return po_number
        else:
            # في حالة فشل النظام الذكي، استخدم الطريقة التقليدية
            current_year = datetime.now().year
            return f"PO-{current_year}-0001"

    except Exception as e:
        logger.error(f"❌ خطأ في توليد رقم أمر الشراء الذكي: {e}")
        # العودة للطريقة التقليدية في حالة الخطأ
        current_year = datetime.now().year
        return f"PO-{current_year}-0001"


@bp.route('/view/<int:po_id>')
@login_required
def view(po_id):
    """عرض تفاصيل أمر الشراء"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب بيانات أمر الشراء مع حقول الاستخدام ومعلومات العملة
        po_query = """
        SELECT po.ID, po.PO_NUMBER, po.CONTRACT_ID, po.SUPPLIER_NAME,
               po.PO_DATE, po.DELIVERY_DATE, po.DELIVERY_LOCATION,
               po.DESCRIPTION, po.STATUS, po.TOTAL_AMOUNT, po.CREATED_BY,
               c.CONTRACT_NUMBER, c.SUPPLIER_NAME as CONTRACT_SUPPLIER,
               po.TITLE, po.DELIVERY_ADDRESS, po.PAYMENT_TERMS, po.CURRENCY,
               po.PRIORITY, po.EXPECTED_DELIVERY_DAYS, po.NOTES, po.SUBTOTAL,
               po.TOTAL_DISCOUNT, po.GROSS_AMOUNT, po.DISCOUNT_AMOUNT, po.NET_AMOUNT,
               po.CREATED_AT, po.UPDATED_AT,
               po.IS_USED, po.USED_AT, po.USED_IN_SHIPMENT_ID,
               curr.SYMBOL as CURRENCY_SYMBOL, curr.NAME_AR as CURRENCY_NAME,
               po.BRANCH_ID, b.BRN_LNAME as BRANCH_NAME
        FROM PURCHASE_ORDERS po
        LEFT JOIN CONTRACTS c ON po.CONTRACT_ID = c.CONTRACT_ID
        LEFT JOIN CURRENCIES curr ON po.CURRENCY = curr.CODE
        LEFT JOIN BRANCHES b ON po.BRANCH_ID = b.BRN_NO
        WHERE po.ID = :1
        """

        po_result = oracle_manager.execute_query(po_query, [po_id])

        if not po_result:
            flash('أمر الشراء غير موجود', 'error')
            return redirect(url_for('purchase_orders.index'))

        purchase_order = po_result[0]

        # جلب أصناف أمر الشراء
        items_query = """
        SELECT ID, ITEM_CODE, ITEM_NAME, UNIT, PRODUCTION_DATE, EXPIRY_DATE,
               QUANTITY, UNIT_PRICE, TOTAL_PRICE
        FROM PO_ITEMS
        WHERE PO_ID = :1
        ORDER BY ID
        """

        items = oracle_manager.execute_query(items_query, [po_id])

        return render_template('purchase_orders/view.html',
                             purchase_order=purchase_order,
                             items=items)

    except Exception as e:
        logger.error(f"❌ خطأ في عرض أمر الشراء: {e}")
        flash('خطأ في عرض أمر الشراء', 'error')
        return redirect(url_for('purchase_orders.index'))


@bp.route('/edit/<int:po_id>')
@login_required
def edit(po_id):
    """تعديل أمر شراء"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب بيانات أمر الشراء مع حقول الاستخدام والحقول الجديدة مع BRANCH_ID
        po_query = """
        SELECT ID, PO_NUMBER, CONTRACT_ID, SUPPLIER_CODE, SUPPLIER_NAME, TITLE,
               PO_DATE, DELIVERY_DATE, DELIVERY_ADDRESS, PAYMENT_TERMS,
               SUPPLIER_INVOICE_NUMBER, SHIPPING_COST, CLEARANCE_COST,
               CURRENCY, PRIORITY, EXPECTED_DELIVERY_DAYS,
               DESCRIPTION, NOTES, STATUS, SUBTOTAL, TOTAL_DISCOUNT,
               TOTAL_AMOUNT, GROSS_AMOUNT, DISCOUNT_AMOUNT, NET_AMOUNT,
               CREATED_AT, UPDATED_AT,
               IS_USED, USED_AT, USED_IN_SHIPMENT_ID, BRANCH_ID
        FROM PURCHASE_ORDERS
        WHERE ID = :1
        """

        po_result = oracle_manager.execute_query(po_query, [po_id])

        if not po_result:
            flash('أمر الشراء غير موجود', 'error')
            return redirect(url_for('purchase_orders.index'))

        purchase_order = po_result[0]

        # جلب أصناف أمر الشراء
        items_query = """
        SELECT ID, ITEM_CODE, ITEM_NAME, UNIT, PRODUCTION_DATE, EXPIRY_DATE,
               QUANTITY, UNIT_PRICE, TOTAL_PRICE
        FROM PO_ITEMS
        WHERE PO_ID = :1
        ORDER BY ID
        """

        items = oracle_manager.execute_query(items_query, [po_id])

        # جلب قائمة العقود المتاحة للاختيار (استثناء المنفذة كلياً)
        contracts_query = """
        SELECT CONTRACT_ID, CONTRACT_NUMBER, SUPPLIER_NAME, START_DATE, END_DATE
        FROM CONTRACTS
        WHERE IS_ACTIVE = 1
        AND NOT (IS_USED = 1 AND CONTRACT_STATUS IN ('FULLY_EXECUTED', 'منفذ كليا', 'COMPLETED'))
        ORDER BY CONTRACT_NUMBER
        """
        contracts = oracle_manager.execute_query(contracts_query)

        # جلب قائمة الموردين النشطين
        suppliers_query = """
        SELECT ID, NAME_AR, CONTACT_PERSON, PHONE, EMAIL
        FROM SUPPLIERS
        WHERE IS_ACTIVE = 1
        ORDER BY NAME_AR
        """
        suppliers = oracle_manager.execute_query(suppliers_query, [])

        # جلب قائمة العملات النشطة
        currencies_query = """
        SELECT ID, CODE, NAME_AR, NAME_EN, SYMBOL, IS_BASE_CURRENCY
        FROM CURRENCIES
        WHERE IS_ACTIVE = 1
        ORDER BY IS_BASE_CURRENCY DESC, NAME_AR
        """
        currencies = oracle_manager.execute_query(currencies_query, [])

        # جلب قائمة الفروع النشطة
        branches_query = """
        SELECT BRN_NO, BRN_LNAME, BRN_FNAME
        FROM BRANCHES
        WHERE NVL(IS_ACTIVE, 1) = 1
        ORDER BY BRN_LNAME
        """
        branches_result = oracle_manager.execute_query(branches_query, [])

        if branches_result:
            branches = [
                {
                    'brn_no': branch[0],
                    'brn_lname': branch[1],
                    'brn_fname': branch[2] or ''
                }
                for branch in branches_result
            ]
        else:
            # فرع افتراضي
            branches = [
                {
                    'brn_no': 21,
                    'brn_lname': 'الفرع الرئيسي',
                    'brn_fname': 'Main Branch'
                }
            ]

        return render_template('purchase_orders/edit.html',
                             purchase_order=purchase_order,
                             items=items,
                             contracts=contracts,
                             suppliers=suppliers,
                             currencies=currencies,
                             branches=branches)

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل أمر الشراء للتعديل: {e}")
        flash('خطأ في تحميل أمر الشراء', 'error')
        return redirect(url_for('purchase_orders.index'))


@bp.route('/api/update/<int:po_id>', methods=['PUT'])
@login_required
def update_purchase_order(po_id):
    """تحديث أمر شراء مع حماية الأوامر المستخدمة"""
    try:
        data = request.get_json()
        oracle = OracleManager()
        oracle.connect()

        # 🛡️ فحص حماية أمر الشراء قبل التحديث
        protection_check_query = """
            SELECT PO_NUMBER, IS_USED, USED_IN_SHIPMENT_ID
            FROM PURCHASE_ORDERS
            WHERE ID = :1
        """
        protection_result = oracle.execute_query(protection_check_query, [po_id])

        if not protection_result:
            oracle.disconnect()
            return jsonify({
                'success': False,
                'message': 'أمر الشراء غير موجود'
            }), 404

        po_number, is_used, used_in_shipment_id = protection_result[0]

        # فحص إضافي للشحنات المرتبطة
        shipment_check_query = """
            SELECT COUNT(*), MIN(shipment_number)
            FROM cargo_shipments
            WHERE purchase_order_id = :1
        """
        shipment_check_result = oracle.execute_query(shipment_check_query, [po_id])
        shipment_count = shipment_check_result[0][0] if shipment_check_result else 0

        # تحديد مستوى الحماية
        if is_used == 1 or shipment_count > 0:
            # للآن، منع التحديث الكامل للأوامر المستخدمة
            # يمكن تخفيف هذا لاحقاً للسماح بتحديث حقول معينة فقط

            shipment_info = ""
            if shipment_check_result and shipment_check_result[0][1]:
                shipment_info = f" (مثل: {shipment_check_result[0][1]})"

            oracle.disconnect()
            logger.warning(f"🛡️ محاولة تعديل أمر شراء محمي: {po_number} - مستخدم في {shipment_count} شحنة")

            return jsonify({
                'success': False,
                'message': f'لا يمكن تعديل أمر الشراء {po_number} لأنه مستخدم في {shipment_count} شحنة{shipment_info}',
                'error_type': 'PROTECTED_ORDER',
                'is_used': True,
                'shipment_count': shipment_count,
                'protection_level': 'full'
            }), 403  # Forbidden

        # جلب البيانات القديمة للمقارنة (للترحيل المحاسبي)
        old_data_query = """
        SELECT SUPPLIER_CODE, TOTAL_AMOUNT, CURRENCY, BRANCH_ID, PO_NUMBER
        FROM PURCHASE_ORDERS
        WHERE ID = :1
        """
        old_data_result = oracle.execute_query(old_data_query, [po_id])
        old_supplier_code, old_total_amount, old_currency, old_branch_id, po_number = old_data_result[0] if old_data_result else (None, 0, 'SAR', 21, '')

        # تحديث بيانات أمر الشراء (للأوامر غير المحمية فقط) مع حقل الفرع
        update_query = """
        UPDATE PURCHASE_ORDERS SET
            TITLE = :1, PO_DATE = :2, DELIVERY_DATE = :3, DELIVERY_ADDRESS = :4,
            PAYMENT_TERMS = :5, SUPPLIER_INVOICE_NUMBER = :6, SHIPPING_COST = :7,
            CLEARANCE_COST = :8, CURRENCY = :9, PRIORITY = :10, EXPECTED_DELIVERY_DAYS = :11,
            DESCRIPTION = :12, NOTES = :13, STATUS = :14, SUBTOTAL = :15, TOTAL_DISCOUNT = :16,
            TOTAL_AMOUNT = :17, GROSS_AMOUNT = :18, DISCOUNT_AMOUNT = :19, NET_AMOUNT = :20,
            BRANCH_ID = :21
        WHERE ID = :22
        """

        # حساب المبلغ الإجمالي النهائي للتحديث
        update_gross_amount = float(data.get('gross_amount', 0))
        update_discount_amount = float(data.get('discount_amount', 0))
        update_shipping_cost = float(data.get('shipping_cost', 0))
        update_clearance_cost = float(data.get('clearance_cost', 0))

        # حساب صافي المبلغ بعد الخصم
        update_net_after_discount = update_gross_amount - update_discount_amount

        # حساب المبلغ الإجمالي النهائي (يشمل أجور الشحن والتخليص)
        update_final_total = update_net_after_discount + update_shipping_cost + update_clearance_cost

        params = [
            data.get('title'),
            datetime.strptime(data.get('po_date'), '%Y-%m-%d') if data.get('po_date') else None,
            datetime.strptime(data.get('delivery_date'), '%Y-%m-%d') if data.get('delivery_date') else None,
            data.get('delivery_address'),
            data.get('payment_terms'),
            data.get('supplier_invoice_number'),  # رقم فاتورة المورد
            update_shipping_cost,  # أجور الشحن
            update_clearance_cost,  # أجور التخليص
            data.get('currency', 'ريال'),
            data.get('priority', 'عادي'),
            data.get('expected_delivery_days'),
            data.get('description'),
            data.get('notes'),
            data.get('status', 'مسودة'),
            data.get('subtotal', 0),
            data.get('total_discount', 0),
            update_final_total,  # المبلغ الإجمالي النهائي (يشمل الشحن والتخليص)
            update_gross_amount,  # المبلغ الإجمالي للأصناف
            update_discount_amount,  # مبلغ الخصم
            update_final_total,  # صافي المبلغ النهائي
            int(data.get('branch_id', old_branch_id or 21)),  # الفرع
            po_id
        ]

        oracle.execute_update(update_query, params)

        # حذف الأصناف القديمة
        delete_items_query = "DELETE FROM PO_ITEMS WHERE PO_ID = :1"
        oracle.execute_update(delete_items_query, [po_id])

        # إضافة الأصناف الجديدة
        items = data.get('items', [])
        for item in items:
            item_insert = """
            INSERT INTO PO_ITEMS (
                PO_ID, ITEM_CODE, ITEM_NAME, UNIT,
                PRODUCTION_DATE, EXPIRY_DATE, QUANTITY, UNIT_PRICE,
                TOTAL_PRICE
            ) VALUES (
                :1, :2, :3, :4, :5, :6, :7, :8, :9
            )
            """

            item_params = [
                po_id,
                item.get('code'),
                item['name'],
                item.get('unit'),
                datetime.strptime(item.get('production_date'), '%Y-%m-%d') if item.get('production_date') else None,
                datetime.strptime(item.get('expiry_date'), '%Y-%m-%d') if item.get('expiry_date') else None,
                item['quantity'],
                item['price'],
                item.get('total_price', 0)
            ]

            oracle.execute_update(item_insert, item_params)

        # تحديث الترحيل المحاسبي باستخدام BT_PKG إذا تغير المبلغ
        if old_supplier_code and (old_total_amount != update_final_total):
            try:
                # عكس المعاملة القديمة
                if old_total_amount > 0:
                    reverse_query = """
                    BEGIN
                        BT_PKG.REVERSE_TXN(
                            p_orig_doc => :1,
                            p_rev_doc => :2,
                            p_reason => 'تحديث أمر الشراء',
                            p_user => :3
                        );
                    END;
                    """

                    reverse_params = [
                        po_number,  # المستند الأصلي
                        f'{po_number}-REV-UPD',  # مستند العكس
                        current_user.id if hasattr(current_user, 'id') else 1
                    ]

                    oracle.execute_update(reverse_query, reverse_params)

                # ترحيل المعاملة الجديدة
                if update_final_total > 0:
                    new_bt_query = """
                    BEGIN
                        BT_PKG.POST_TXN(
                            p_ent_type => 'SUPPLIER',
                            p_ent_id => :1,
                            p_doc_type => 'PURCHASE_ORDER',
                            p_doc_no => :2,
                            p_doc_date => SYSDATE,
                            p_curr => :3,
                            p_dr => 0,
                            p_cr => :4,
                            p_rate => 1,
                            p_desc => :5,
                            p_branch => :6,
                            p_user => :7
                        );
                    END;
                    """

                    new_bt_params = [
                        old_supplier_code,  # معرف المورد
                        po_number,          # رقم المستند
                        data.get('currency', old_currency),  # العملة
                        update_final_total, # المبلغ الجديد
                        f'تحديث أمر شراء رقم {po_number}',  # الوصف
                        int(data.get('branch_id', old_branch_id or 21)),  # الفرع
                        current_user.id if hasattr(current_user, 'id') else 1
                    ]

                    oracle.execute_update(new_bt_query, new_bt_params)

                logger.info(f"✅ تم تحديث الترحيل المحاسبي لأمر الشراء {po_number}")

            except Exception as e:
                logger.warning(f"⚠️ خطأ في تحديث الترحيل المحاسبي: {e}")

        # تحديث حالة العقد إلى "مُستخدم" إذا تم ربط أمر الشراء بعقد
        contract_id = data.get('contract_id')
        if contract_id:
            try:
                update_contract_query = "UPDATE CONTRACTS SET IS_USED = 1 WHERE CONTRACT_ID = :1"
                oracle.execute_update(update_contract_query, [contract_id])
                logger.info(f"✅ تم تحديث العقد {contract_id} إلى حالة 'مُستخدم'")

                # تحديث حالة تنفيذ العقد (الكميات المنفذة والمتبقية)
                from contract_execution_utils import update_contract_execution_status
                execution_result = update_contract_execution_status(contract_id, oracle)
                if execution_result['success']:
                    logger.info(f"✅ تم تحديث حالة تنفيذ العقد {contract_id}: {execution_result['contract_status_ar']}")
                else:
                    logger.warning(f"⚠️ خطأ في تحديث حالة تنفيذ العقد {contract_id}: {execution_result['message']}")

            except Exception as e:
                logger.warning(f"⚠️ خطأ في تحديث حالة العقد {contract_id}: {e}")

        oracle.disconnect()

        logger.info(f"✅ تم تحديث أمر الشراء {po_id} بنجاح")

        return jsonify({
            'success': True,
            'message': 'تم تحديث أمر الشراء بنجاح',
            'po_id': po_id
        })

    except Exception as e:
        logger.error(f"❌ خطأ في تحديث أمر الشراء: {e}")
        return jsonify({
            'success': False,
            'message': f'فشل في تحديث أمر الشراء: {str(e)}'
        }), 500


@bp.route('/api/delete/<int:po_id>', methods=['DELETE'])
@login_required
def delete_purchase_order(po_id):
    """حذف أمر شراء"""
    try:
        from oracle_manager import OracleManager
        oracle = OracleManager()
        oracle.connect()

        # التحقق من وجود أمر الشراء وحالة الاستخدام مع بيانات الترحيل
        check_query = """
            SELECT PO_NUMBER, CONTRACT_ID, IS_USED, USED_IN_SHIPMENT_ID,
                   SUPPLIER_CODE, TOTAL_AMOUNT, CURRENCY, BRANCH_ID
            FROM PURCHASE_ORDERS
            WHERE ID = :1
        """
        result = oracle.execute_query(check_query, [po_id])

        if not result:
            oracle.disconnect()
            return jsonify({
                'success': False,
                'message': 'أمر الشراء غير موجود'
            }), 404

        po_number, contract_id, is_used, used_in_shipment_id, supplier_code, total_amount, currency, branch_id = result[0]

        # 🛡️ حماية أوامر الشراء المستخدمة
        if is_used == 1:
            # البحث عن الشحنات المرتبطة للحصول على معلومات أكثر
            shipment_query = """
                SELECT shipment_number, tracking_number
                FROM cargo_shipments
                WHERE purchase_order_id = :1
                AND ROWNUM = 1
            """
            shipment_result = oracle.execute_query(shipment_query, [po_id])

            shipment_info = ""
            if shipment_result:
                shipment_number, tracking_number = shipment_result[0]
                shipment_info = f" (الشحنة: {shipment_number})"

            oracle.disconnect()
            logger.warning(f"🛡️ محاولة حذف أمر شراء محمي: {po_number} - مستخدم في شحنة")

            return jsonify({
                'success': False,
                'message': f'لا يمكن حذف أمر الشراء {po_number} لأنه مستخدم في شحنة{shipment_info}',
                'error_type': 'PROTECTED_ORDER',
                'is_used': True,
                'shipment_id': used_in_shipment_id
            }), 403  # Forbidden

        # التحقق الإضافي: فحص وجود شحنات مرتبطة (double check)
        double_check_query = """
            SELECT COUNT(*), MIN(shipment_number)
            FROM cargo_shipments
            WHERE purchase_order_id = :1
        """
        double_check_result = oracle.execute_query(double_check_query, [po_id])

        if double_check_result and double_check_result[0][0] > 0:
            shipment_count = double_check_result[0][0]
            first_shipment = double_check_result[0][1]

            oracle.disconnect()
            logger.warning(f"🛡️ محاولة حذف أمر شراء مرتبط بشحنات: {po_number} - {shipment_count} شحنة")

            return jsonify({
                'success': False,
                'message': f'لا يمكن حذف أمر الشراء {po_number} لأنه مرتبط بـ {shipment_count} شحنة (مثل: {first_shipment})',
                'error_type': 'HAS_SHIPMENTS',
                'shipment_count': shipment_count,
                'first_shipment': first_shipment
            }), 403  # Forbidden

        # عكس الترحيل المحاسبي باستخدام BT_PKG قبل الحذف
        if supplier_code and total_amount and total_amount > 0:
            try:
                reverse_query = """
                BEGIN
                    BT_PKG.REVERSE_TXN(
                        p_orig_doc => :1,
                        p_rev_doc => :2,
                        p_reason => 'حذف أمر الشراء',
                        p_user => :3
                    );
                END;
                """

                reverse_params = [
                    po_number,  # المستند الأصلي
                    f'{po_number}-DEL',  # مستند العكس للحذف
                    current_user.id if hasattr(current_user, 'id') else 1
                ]

                oracle.execute_update(reverse_query, reverse_params)
                logger.info(f"✅ تم عكس الترحيل المحاسبي لأمر الشراء {po_number}")

            except Exception as e:
                logger.warning(f"⚠️ خطأ في عكس الترحيل المحاسبي: {e}")
                # لا نوقف عملية الحذف في حالة فشل عكس الترحيل

        # حذف أصناف أمر الشراء أولاً
        delete_items_query = "DELETE FROM PO_ITEMS WHERE PO_ID = :1"
        oracle.execute_update(delete_items_query, [po_id])

        # حذف أمر الشراء
        delete_po_query = "DELETE FROM PURCHASE_ORDERS WHERE ID = :1"
        oracle.execute_update(delete_po_query, [po_id])

        # تحديث حالة العقد إذا كان مرتبطاً بعقد
        if contract_id:
            try:
                # تحديث حالة تنفيذ العقد (الكميات المنفذة والمتبقية)
                from contract_execution_utils import update_contract_execution_status
                execution_result = update_contract_execution_status(contract_id, oracle)
                if execution_result['success']:
                    logger.info(f"✅ تم تحديث حالة تنفيذ العقد {contract_id} بعد حذف أمر الشراء: {execution_result['contract_status_ar']}")
                else:
                    logger.warning(f"⚠️ خطأ في تحديث حالة تنفيذ العقد {contract_id}: {execution_result['message']}")

            except Exception as e:
                logger.warning(f"⚠️ خطأ في تحديث حالة العقد {contract_id} بعد الحذف: {e}")

        oracle.disconnect()

        logger.info(f"✅ تم حذف أمر الشراء {po_number} بنجاح")

        return jsonify({
            'success': True,
            'message': f'تم حذف أمر الشراء {po_number} بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في حذف أمر الشراء: {e}")
        return jsonify({
            'success': False,
            'message': f'فشل في حذف أمر الشراء: {str(e)}'
        }), 500


@bp.route('/api/search-suppliers', methods=['POST'])
@login_required
def search_suppliers():
    """البحث عن الموردين"""
    try:
        data = request.get_json()
        oracle_manager = get_oracle_manager()

        # بناء استعلام البحث
        base_query = """
        SELECT ID, SUPPLIER_CODE, NAME_AR, SUPPLIER_TYPE, PHONE, EMAIL, IS_ACTIVE,
               CONTACT_PERSON, ADDRESS, CITY, COUNTRY
        FROM SUPPLIERS
        WHERE 1=1
        """

        params = []
        conditions = []

        # البحث بالكود
        if data.get('code'):
            conditions.append("UPPER(SUPPLIER_CODE) LIKE UPPER(:code)")
            params.append(f"%{data['code']}%")

        # البحث بالاسم
        if data.get('name'):
            conditions.append("UPPER(NAME_AR) LIKE UPPER(:name)")
            params.append(f"%{data['name']}%")

        # البحث بالنوع
        if data.get('type'):
            conditions.append("SUPPLIER_TYPE = :type")
            params.append(data['type'])

        # إضافة الشروط للاستعلام
        if conditions:
            query = base_query + " AND " + " AND ".join(conditions)
        else:
            query = base_query

        # ترتيب النتائج
        query += " ORDER BY IS_ACTIVE DESC, NAME_AR ASC"

        # تنفيذ الاستعلام
        results = oracle_manager.execute_query(query, params)

        suppliers = []
        if results:
            for row in results:
                # معالجة CLOB fields
                address_value = ''
                if row[8] is not None:
                    try:
                        address_value = str(row[8].read()) if hasattr(row[8], 'read') else str(row[8])
                    except:
                        address_value = str(row[8]) if row[8] else ''

                supplier = {
                    'id': row[0],
                    'code': row[1] or '',  # SUPPLIER_CODE
                    'name': row[2] or '',  # NAME_AR
                    'type': row[3] or '',  # SUPPLIER_TYPE
                    'phone': row[4] or '',
                    'email': row[5] or '',
                    'is_active': bool(row[6]) if row[6] is not None else True,
                    'contact_person': row[7] or '',
                    'address': address_value,
                    'city': row[9] or '',
                    'country': row[10] or ''
                }
                suppliers.append(supplier)

        return jsonify({
            'success': True,
            'suppliers': suppliers,
            'count': len(suppliers)
        })

    except Exception as e:
        logger.error(f"❌ خطأ في البحث عن الموردين: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في البحث: {str(e)}'
        }), 500


@bp.route('/supplier-search')
@login_required
def supplier_search():
    """صفحة البحث عن الموردين"""
    return render_template('purchase_orders/supplier_search.html')


@bp.route('/api/get-supplier-by-code/<supplier_code>')
@login_required
def get_supplier_by_code(supplier_code):
    """البحث المباشر عن مورد بالكود"""
    try:
        # التحقق من صحة كود المورد
        if not supplier_code or supplier_code.lower() in ['none', 'null', '']:
            return jsonify({
                'success': False,
                'message': 'كود المورد غير صالح'
            })

        oracle_manager = get_oracle_manager()

        # البحث عن المورد بالكود الكامل
        query = """
        SELECT ID, SUPPLIER_CODE, NAME_AR, SUPPLIER_TYPE, PHONE, EMAIL, IS_ACTIVE,
               CONTACT_PERSON, CITY, COUNTRY, COMMERCIAL_REGISTER, TAX_NUMBER
        FROM SUPPLIERS
        WHERE UPPER(SUPPLIER_CODE) = UPPER(:code)
        AND IS_ACTIVE = 1
        """

        result = oracle_manager.execute_query(query, [supplier_code])

        if result and len(result) > 0:
            row = result[0]
            supplier = {
                'id': row[0],
                'code': row[1] or '',
                'name': row[2] or '',
                'type': row[3] or '',
                'phone': row[4] or '',
                'email': row[5] or '',
                'is_active': bool(row[6]) if row[6] is not None else True,
                'contact_person': row[7] or '',
                'city': row[8] or '',
                'country': row[9] or '',
                'commercial_register': row[10] or '',
                'tax_number': row[11] or ''
            }

            return jsonify({
                'success': True,
                'supplier': supplier,
                'message': f'تم العثور على المورد: {supplier["name"]}'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'لم يتم العثور على مورد بالكود: {supplier_code}'
            })

    except Exception as e:
        logger.error(f"❌ خطأ في البحث عن المورد بالكود {supplier_code}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في البحث: {str(e)}'
        }), 500

@bp.route('/api/search-items')
@login_required
def search_items():
    """البحث في الأصناف"""
    try:
        oracle_manager = get_oracle_manager()

        # استلام معايير البحث
        item_code = request.args.get('code', '').strip()
        item_name = request.args.get('name', '').strip()

        # بناء الاستعلام
        query = """
        SELECT ITEM_CODE, NAME_AR, UNIT_OF_MEASURE
        FROM ITEMS
        WHERE IS_ACTIVE = 1
        """
        params = []

        if item_code:
            query += " AND UPPER(ITEM_CODE) LIKE UPPER(:1)"
            params.append(f"%{item_code}%")

        if item_name:
            if params:
                query += " AND UPPER(NAME_AR) LIKE UPPER(:2)"
            else:
                query += " AND UPPER(NAME_AR) LIKE UPPER(:1)"
            params.append(f"%{item_name}%")

        query += " ORDER BY ITEM_CODE"

        # تحديد عدد النتائج
        if not item_code and not item_name:
            query += " FETCH FIRST 100 ROWS ONLY"
        else:
            query += " FETCH FIRST 50 ROWS ONLY"

        items = oracle_manager.execute_query(query, params)

        items_list = []
        if items:
            for item in items:
                items_list.append({
                    'code': item[0],
                    'name': item[1],
                    'unit': item[2] or 'قطعة'
                })

        return jsonify({
            'success': True,
            'items': items_list,
            'count': len(items_list)
        })

    except Exception as e:
        logger.error(f"خطأ في البحث عن الأصناف: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في البحث عن الأصناف: {str(e)}'
        })

@bp.route('/api/get-item-by-code/<item_code>')
@login_required
def get_item_by_code(item_code):
    """البحث المباشر عن صنف بالكود"""
    try:
        # التحقق من صحة كود الصنف
        if not item_code or item_code.lower() in ['none', 'null', '']:
            return jsonify({
                'success': False,
                'message': 'كود الصنف غير صالح'
            })

        oracle_manager = get_oracle_manager()

        # البحث عن الصنف بالكود
        query = """
        SELECT ITEM_CODE, NAME_AR, UNIT_OF_MEASURE
        FROM ITEMS
        WHERE UPPER(ITEM_CODE) = UPPER(:1)
        AND IS_ACTIVE = 1
        """

        result = oracle_manager.execute_query(query, [item_code])

        if result and len(result) > 0:
            item = result[0]
            return jsonify({
                'success': True,
                'item': {
                    'code': item[0],
                    'name': item[1],
                    'unit': item[2] or 'قطعة'
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': f'لم يتم العثور على صنف بالكود: {item_code}'
            })

    except Exception as e:
        logger.error(f"خطأ في البحث عن الصنف {item_code}: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في البحث عن الصنف: {str(e)}'
        })

@bp.route('/item-search-window')
@login_required
def item_search_window():
    """نافذة بحث الأصناف المنفصلة"""
    return render_template('purchase_orders/item_search_window.html')


# ==========================================
# 🛡️ دوال الحماية لأوامر الشراء
# Purchase Order Protection Functions
# ==========================================

@bp.route('/api/check-protection/<int:po_id>', methods=['GET'])
@login_required
def check_purchase_order_protection(po_id):
    """فحص حماية أمر الشراء - التحقق من إمكانية التعديل/الحذف"""
    try:
        from oracle_manager import OracleManager
        oracle = OracleManager()
        oracle.connect()

        # فحص حالة أمر الشراء
        check_query = """
            SELECT PO_NUMBER, IS_USED, USED_IN_SHIPMENT_ID, STATUS
            FROM PURCHASE_ORDERS
            WHERE ID = :1
        """
        result = oracle.execute_query(check_query, [po_id])

        if not result:
            oracle.disconnect()
            return jsonify({
                'success': False,
                'message': 'أمر الشراء غير موجود'
            }), 404

        po_number, is_used, used_in_shipment_id, status = result[0]

        # فحص الشحنات المرتبطة
        shipment_query = """
            SELECT COUNT(*),
                   LISTAGG(shipment_number, ', ') WITHIN GROUP (ORDER BY shipment_number) as shipments
            FROM cargo_shipments
            WHERE purchase_order_id = :1
        """
        shipment_result = oracle.execute_query(shipment_query, [po_id])

        shipment_count = 0
        shipment_list = ""
        if shipment_result:
            shipment_count = shipment_result[0][0] or 0
            shipment_list = shipment_result[0][1] or ""

        oracle.disconnect()

        # تحديد مستوى الحماية
        protection_level = "none"  # لا توجد حماية
        can_edit = True
        can_delete = True
        protection_reason = ""

        if is_used == 1 or shipment_count > 0:
            protection_level = "full"  # حماية كاملة
            can_edit = False  # منع التعديل الكامل (يمكن تخفيفه لاحقاً)
            can_delete = False  # منع الحذف

            if shipment_count == 1:
                protection_reason = f"مستخدم في شحنة واحدة: {shipment_list}"
            elif shipment_count > 1:
                protection_reason = f"مستخدم في {shipment_count} شحنة: {shipment_list}"
            else:
                protection_reason = "مُعلم كمستخدم في النظام"

        return jsonify({
            'success': True,
            'po_id': po_id,
            'po_number': po_number,
            'status': status,
            'protection_level': protection_level,
            'can_edit': can_edit,
            'can_delete': can_delete,
            'is_used': is_used == 1,
            'shipment_count': shipment_count,
            'shipment_list': shipment_list,
            'protection_reason': protection_reason,
            'used_in_shipment_id': used_in_shipment_id
        })

    except Exception as e:
        logger.error(f"❌ خطأ في فحص حماية أمر الشراء: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في فحص الحماية: {str(e)}'
        }), 500


@bp.route('/api/protection-status', methods=['POST'])
@login_required
def get_multiple_protection_status():
    """فحص حماية عدة أوامر شراء دفعة واحدة"""
    try:
        data = request.get_json()
        po_ids = data.get('po_ids', [])

        if not po_ids:
            return jsonify({
                'success': False,
                'message': 'لم يتم تحديد أوامر شراء للفحص'
            }), 400

        from oracle_manager import OracleManager
        oracle = OracleManager()
        oracle.connect()

        # إنشاء استعلام للفحص المتعدد
        placeholders = ','.join([':' + str(i+1) for i in range(len(po_ids))])

        check_query = f"""
            SELECT po.ID, po.PO_NUMBER, po.IS_USED, po.USED_IN_SHIPMENT_ID, po.STATUS,
                   NVL(shipment_count.cnt, 0) as shipment_count
            FROM PURCHASE_ORDERS po
            LEFT JOIN (
                SELECT purchase_order_id, COUNT(*) as cnt
                FROM cargo_shipments
                WHERE purchase_order_id IN ({placeholders})
                GROUP BY purchase_order_id
            ) shipment_count ON po.ID = shipment_count.purchase_order_id
            WHERE po.ID IN ({placeholders})
        """

        # تكرار po_ids للاستعلام (مرة للـ subquery ومرة للـ main query)
        params = po_ids + po_ids
        result = oracle.execute_query(check_query, params)

        oracle.disconnect()

        protection_status = {}
        for row in result:
            po_id, po_number, is_used, used_in_shipment_id, status, shipment_count = row

            can_edit = True
            can_delete = True
            protection_level = "none"
            protection_reason = ""

            if is_used == 1 or shipment_count > 0:
                protection_level = "full"
                can_edit = False
                can_delete = False

                if shipment_count > 0:
                    protection_reason = f"مستخدم في {shipment_count} شحنة"
                else:
                    protection_reason = "مُعلم كمستخدم في النظام"

            protection_status[po_id] = {
                'po_number': po_number,
                'status': status,
                'protection_level': protection_level,
                'can_edit': can_edit,
                'can_delete': can_delete,
                'is_used': is_used == 1,
                'shipment_count': shipment_count,
                'protection_reason': protection_reason
            }

        return jsonify({
            'success': True,
            'protection_status': protection_status
        })

    except Exception as e:
        logger.error(f"❌ خطأ في فحص الحماية المتعددة: {e}")
        return jsonify({
            'success': False,
            'message': f'خطأ في فحص الحماية: {str(e)}'
        }), 500


# تم حذف الكود البدائي - الآن النظام ذكي وتلقائي مع حماية متقدمة


# ==================== إدارة وثائق أوامر الشراء ====================

@bp.route('/<int:po_id>/documents')
@login_required
def purchase_order_documents(po_id):
    """صفحة إدارة وثائق أمر الشراء"""
    logger.info(f"🔍 طلب إدارة وثائق أمر الشراء {po_id}")
    try:
        oracle_manager = get_oracle_manager()

        # جلب بيانات أمر الشراء
        po_query = """
            SELECT ID, PO_NUMBER, SUPPLIER_NAME, PO_DATE, TOTAL_AMOUNT, CURRENCY
            FROM PURCHASE_ORDERS
            WHERE ID = :po_id
        """

        po_result = oracle_manager.execute_query(po_query, {'po_id': po_id})

        if not po_result:
            flash('أمر الشراء غير موجود', 'error')
            return redirect(url_for('purchase_orders.index'))

        purchase_order = {
            'id': po_result[0][0],
            'po_number': po_result[0][1],
            'supplier_name': po_result[0][2],
            'po_date': po_result[0][3],
            'total_amount': po_result[0][4],
            'currency': po_result[0][5]
        }

        # جلب وثائق أمر الشراء (إذا كان الجدول موجود)
        try:
            documents_query = """
                SELECT ID, TITLE, DOCUMENT_TYPE, FILENAME, ORIGINAL_FILENAME, FILE_SIZE,
                       FILE_PATH, DESCRIPTION, CREATED_BY, CREATED_AT, UPDATED_BY, UPDATED_AT,
                       IS_ACTIVE, NEXTCLOUD_SHARE_LINK, ONEDRIVE_SHARE_LINK, URL,
                       DOCUMENT_CATEGORY, VERSION_NUMBER, APPROVAL_STATUS, ACCESS_LEVEL,
                       DOWNLOAD_COUNT, LAST_ACCESSED, MIME_TYPE
                FROM PO_DOCUMENTS
                WHERE PO_ID = :po_id AND IS_ACTIVE = 1
                ORDER BY CREATED_AT DESC
            """

            documents_result = oracle_manager.execute_query(documents_query, {'po_id': po_id})

            documents = []
            for doc in documents_result:
                # حساب حجم الملف بالميجابايت
                file_size_mb = round(doc[5] / (1024 * 1024), 2) if doc[5] else 0

                documents.append({
                    'id': doc[0],
                    'title': doc[1],
                    'document_type': doc[2],
                    'file_name': doc[3],
                    'original_filename': doc[4],
                    'file_size': doc[5],
                    'file_size_mb': file_size_mb,
                    'file_path': doc[6],
                    'description': doc[7],
                    'notes': doc[7],  # للتوافق مع الكود القديم
                    'created_by': doc[8],
                    'uploaded_by': doc[8],  # للتوافق مع الكود القديم
                    'created_at': doc[9],
                    'uploaded_at': doc[9],  # للتوافق مع الكود القديم
                    'updated_by': doc[10],
                    'updated_at': doc[11],
                    'is_active': doc[12],
                    'nextcloud_share_link': doc[13],
                    'onedrive_share_link': doc[14],
                    'url': doc[15],
                    'document_category': doc[16],
                    'version_number': doc[17],
                    'approval_status': doc[18],
                    'access_level': doc[19],
                    'download_count': doc[20],
                    'last_accessed': doc[21],
                    'mime_type': doc[22]
                })
        except Exception as doc_error:
            logger.warning(f"⚠️ جدول PO_DOCUMENTS غير موجود أو خطأ في الاستعلام: {doc_error}")
            documents = []

        return render_template('purchase_orders/documents.html',
                             purchase_order=purchase_order,
                             documents=documents)

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل وثائق أمر الشراء: {e}")
        flash('خطأ في تحميل وثائق أمر الشراء', 'error')
        return redirect(url_for('purchase_orders.index'))


@bp.route('/<int:po_id>/documents/upload', methods=['POST'])
@login_required
def upload_po_document(po_id):
    """رفع وثيقة لأمر الشراء"""
    logger.info(f"📤 طلب رفع وثيقة لأمر الشراء {po_id}")
    try:
        oracle_manager = get_oracle_manager()

        # التحقق من وجود أمر الشراء
        po_query = "SELECT ID FROM PURCHASE_ORDERS WHERE ID = :po_id"
        po_result = oracle_manager.execute_query(po_query, {'po_id': po_id})

        if not po_result:
            return jsonify({'success': False, 'message': 'أمر الشراء غير موجود'})

        # الحصول على البيانات من النموذج
        document_type = request.form.get('document_type')
        document_name = request.form.get('document_name', '')
        notes = request.form.get('notes', '')

        if not document_type:
            return jsonify({'success': False, 'message': 'نوع الوثيقة مطلوب'})

        # التحقق من وجود ملفات
        if 'files' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم اختيار أي ملفات'})

        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({'success': False, 'message': 'لم يتم اختيار أي ملفات'})

        # التحقق من وجود جدول الوثائق (الجدول موجود بالفعل بالهيكل الجديد)
        try:
            check_table_query = """
                SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = 'PO_DOCUMENTS'
            """
            table_exists = oracle_manager.execute_query(check_table_query, [])
            if not table_exists or table_exists[0][0] == 0:
                logger.error("❌ جدول PO_DOCUMENTS غير موجود")
                return jsonify({'success': False, 'message': 'جدول الوثائق غير موجود'})

            logger.debug("✅ جدول PO_DOCUMENTS موجود")
        except Exception as table_error:
            logger.error(f"❌ خطأ في فحص جدول PO_DOCUMENTS: {table_error}")
            return jsonify({'success': False, 'message': 'خطأ في فحص جدول الوثائق'})

        uploaded_files = []
        upload_folder = os.path.join(current_app.root_path, 'static', 'uploads', 'po_documents')

        # إنشاء مجلد الرفع إذا لم يكن موجوداً
        os.makedirs(upload_folder, exist_ok=True)

        for file in files:
            if file and file.filename:
                # التحقق من نوع الملف
                allowed_extensions = {'pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'}
                file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

                if file_extension not in allowed_extensions:
                    return jsonify({'success': False, 'message': f'نوع الملف {file_extension} غير مدعوم'})

                # التحقق من حجم الملف (10 ميجابايت)
                file.seek(0, 2)  # الانتقال لنهاية الملف
                file_size = file.tell()
                file.seek(0)  # العودة لبداية الملف

                if file_size > 10 * 1024 * 1024:  # 10 ميجابايت
                    return jsonify({'success': False, 'message': f'حجم الملف {file.filename} كبير جداً (أكثر من 10 ميجابايت)'})

                # إنشاء اسم ملف فريد
                import uuid
                from datetime import datetime

                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                unique_id = str(uuid.uuid4())[:8]
                safe_filename = f"{timestamp}_{unique_id}_{file.filename}"

                file_path = os.path.join(upload_folder, safe_filename)

                # حفظ الملف
                file.save(file_path)

                # إدراج معلومات الملف في قاعدة البيانات
                # تحديد نوع MIME
                mime_type, _ = mimetypes.guess_type(file.filename)

                insert_query = """
                    INSERT INTO PO_DOCUMENTS (
                        PO_ID, TITLE, DOCUMENT_TYPE, FILENAME, ORIGINAL_FILENAME,
                        FILE_PATH, FILE_SIZE, DESCRIPTION, CREATED_BY, MIME_TYPE,
                        VERSION_NUMBER, APPROVAL_STATUS, ACCESS_LEVEL
                    ) VALUES (
                        :po_id, :title, :document_type, :filename, :original_filename,
                        :file_path, :file_size, :description, :created_by, :mime_type,
                        :version_number, :approval_status, :access_level
                    )
                """

                oracle_manager.execute_update(insert_query, {
                    'po_id': po_id,
                    'title': document_name or file.filename,
                    'document_type': document_type,
                    'filename': safe_filename,
                    'original_filename': file.filename,
                    'file_path': file_path,
                    'file_size': file_size,
                    'description': notes,
                    'created_by': session.get('username', 'unknown'),
                    'mime_type': mime_type,
                    'version_number': 1,
                    'approval_status': 'APPROVED',
                    'access_level': 'PUBLIC'
                })
                oracle_manager.commit()

                uploaded_files.append({
                    'name': file.filename,
                    'size': file_size,
                    'type': document_type
                })

                logger.info(f"✅ تم رفع ملف: {file.filename} لأمر الشراء {po_id}")

        return jsonify({
            'success': True,
            'message': f'تم رفع {len(uploaded_files)} ملف بنجاح',
            'files': uploaded_files
        })

    except Exception as e:
        logger.error(f"❌ خطأ في رفع وثيقة أمر الشراء {po_id}: {e}")
        return jsonify({'success': False, 'message': f'حدث خطأ في رفع الملف: {str(e)}'})


@bp.route('/documents/<int:doc_id>/download')
@login_required
def download_po_document(doc_id):
    """تحميل وثيقة أمر الشراء"""
    logger.info(f"⬇️ طلب تحميل وثيقة {doc_id}")
    try:
        oracle_manager = get_oracle_manager()

        # جلب معلومات الوثيقة
        doc_query = """
            SELECT FILE_PATH, FILENAME, ORIGINAL_FILENAME, TITLE
            FROM PO_DOCUMENTS
            WHERE ID = :doc_id AND IS_ACTIVE = 1
        """

        doc_result = oracle_manager.execute_query(doc_query, {'doc_id': doc_id})

        if not doc_result:
            flash('الوثيقة غير موجودة', 'error')
            return redirect(request.referrer or url_for('purchase_orders.index'))

        file_path = doc_result[0][0]
        filename = doc_result[0][1]
        original_filename = doc_result[0][2]
        title = doc_result[0][3]

        # استخدام الاسم الأصلي للتحميل
        download_name = original_filename or filename or title

        if os.path.exists(file_path):
            # تحديث عداد التحميل
            try:
                update_download_query = """
                    UPDATE PO_DOCUMENTS
                    SET DOWNLOAD_COUNT = NVL(DOWNLOAD_COUNT, 0) + 1,
                        LAST_ACCESSED = SYSDATE
                    WHERE ID = :doc_id
                """
                oracle_manager.execute_update(update_download_query, {'doc_id': doc_id})
                oracle_manager.commit()
            except Exception as update_error:
                logger.warning(f"⚠️ خطأ في تحديث عداد التحميل: {update_error}")

            logger.info(f"✅ تم تحميل الوثيقة: {download_name}")
            return send_file(file_path, as_attachment=True, download_name=download_name)
        else:
            flash('الملف غير موجود على الخادم', 'error')
            return redirect(request.referrer or url_for('purchase_orders.index'))

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل وثيقة {doc_id}: {e}")
        flash('حدث خطأ في تحميل الملف', 'error')
        return redirect(request.referrer or url_for('purchase_orders.index'))


@bp.route('/documents/<int:doc_id>/delete', methods=['POST'])
@login_required
def delete_po_document(doc_id):
    """حذف وثيقة أمر الشراء"""
    logger.info(f"🗑️ طلب حذف وثيقة {doc_id}")
    try:
        oracle_manager = get_oracle_manager()

        # جلب معلومات الوثيقة
        doc_query = """
            SELECT FILE_PATH, FILENAME, ORIGINAL_FILENAME, PO_ID, TITLE
            FROM PO_DOCUMENTS
            WHERE ID = :doc_id AND IS_ACTIVE = 1
        """

        doc_result = oracle_manager.execute_query(doc_query, {'doc_id': doc_id})

        if not doc_result:
            return jsonify({'success': False, 'message': 'الوثيقة غير موجودة'})

        file_path = doc_result[0][0]
        filename = doc_result[0][1]
        original_filename = doc_result[0][2]
        po_id = doc_result[0][3]
        title = doc_result[0][4]

        display_name = title or original_filename or filename

        # حذف الملف من الخادم
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"✅ تم حذف الملف من الخادم: {file_path}")

        # حذف منطقي (تعطيل الوثيقة بدلاً من الحذف الفعلي)
        delete_query = """
            UPDATE PO_DOCUMENTS
            SET IS_ACTIVE = 0, UPDATED_BY = :updated_by, UPDATED_AT = SYSDATE
            WHERE ID = :doc_id
        """
        oracle_manager.execute_update(delete_query, {
            'doc_id': doc_id,
            'updated_by': session.get('username', 'unknown')
        })
        oracle_manager.commit()

        logger.info(f"✅ تم حذف الوثيقة: {display_name}")

        return jsonify({
            'success': True,
            'message': f'تم حذف الوثيقة "{display_name}" بنجاح'
        })

    except Exception as e:
        logger.error(f"❌ خطأ في حذف وثيقة {doc_id}: {e}")
        return jsonify({'success': False, 'message': f'حدث خطأ في حذف الملف: {str(e)}'})


@bp.route('/documents/<int:doc_id>/create-link', methods=['POST'])
@login_required
def create_po_document_link(doc_id):
    """إنشاء رابط مشاركة لوثيقة أمر الشراء"""
    logger.info(f"🔗 طلب إنشاء رابط مشاركة للوثيقة {doc_id}")
    try:
        oracle_manager = get_oracle_manager()

        # الحصول على نوع الخدمة من الطلب
        data = request.get_json()
        service = data.get('service', 'nextcloud')  # افتراضي nextcloud

        # جلب معلومات الوثيقة
        doc_query = """
            SELECT ID, FILENAME, ORIGINAL_FILENAME, FILE_PATH, PO_ID, TITLE
            FROM PO_DOCUMENTS
            WHERE ID = :doc_id AND IS_ACTIVE = 1
        """

        doc_result = oracle_manager.execute_query(doc_query, {'doc_id': doc_id})

        if not doc_result:
            return jsonify({'success': False, 'message': 'الوثيقة غير موجودة'})

        filename = doc_result[0][1]
        original_filename = doc_result[0][2]
        file_path = doc_result[0][3]
        po_id = doc_result[0][4]
        title = doc_result[0][5]

        display_name = title or original_filename or filename

        # التحقق من وجود الملف
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'message': 'الملف غير موجود على الخادم'})

        # تحديد اسم العمود
        if service == 'nextcloud':
            column_name = 'NEXTCLOUD_SHARE_LINK'
        else:  # onedrive
            column_name = 'ONEDRIVE_SHARE_LINK'

        # التحقق من وجود رابط مسبق
        check_query = f"""
            SELECT {column_name}
            FROM PO_DOCUMENTS
            WHERE ID = :doc_id
        """

        existing_result = oracle_manager.execute_query(check_query, {'doc_id': doc_id})
        existing_link = existing_result[0][0] if existing_result and existing_result[0][0] else None

        if existing_link:
            logger.info(f"✅ رابط {service} موجود مسبقاً: {existing_link}")
            return jsonify({
                'success': True,
                'message': f'رابط {service} موجود مسبقاً',
                'share_link': existing_link,
                'is_existing': True
            })

        logger.info(f"🔧 إنشاء رابط جديد للخدمة: {service}")

        # إنشاء رابط حقيقي باستخدام الخدمات السحابية
        share_link = None

        if service == 'nextcloud':
            share_link = _create_cloud_link(file_path, original_filename or filename, 'nextcloud')
        elif service == 'onedrive':
            # إنشاء رابط تحميل مباشر بدلاً من OneDrive وهمي
            share_link = _create_direct_download_link(original_filename or filename, doc_id)

        if not share_link:
            return jsonify({
                'success': False,
                'message': f'فشل في إنشاء رابط {service}'
            }), 500

        # تحديث قاعدة البيانات
        try:
            update_query = f"""
                UPDATE PO_DOCUMENTS
                SET {column_name} = :share_link, UPDATED_BY = :updated_by, UPDATED_AT = SYSDATE
                WHERE ID = :doc_id
            """

            oracle_manager.execute_update(update_query, {
                'share_link': share_link,
                'doc_id': doc_id,
                'updated_by': session.get('username', 'unknown')
            })
            oracle_manager.commit()
        except Exception as db_error:
            # إذا فشل التحديث، نرجع رابط وهمي فقط
            logger.warning(f"فشل في تحديث قاعدة البيانات: {db_error}")

        logger.info(f"✅ تم إنشاء رابط {service} للوثيقة {display_name}")

        return jsonify({
            'success': True,
            'message': f'تم إنشاء رابط {service} بنجاح',
            'share_link': share_link,
            'is_existing': False
        })

    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء رابط مشاركة للوثيقة {doc_id}: {e}")
        return jsonify({'success': False, 'message': f'حدث خطأ في إنشاء الرابط: {str(e)}'})


@bp.route('/api/data')
@login_required
def api_get_purchase_orders_data():
    """API لجلب بيانات أوامر الشراء مع الفلترة"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب معاملات الفلترة
        status_filter = request.args.get('status', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        usage_filter = request.args.get('usage', '')

        # بناء الاستعلام الأساسي
        query = """
        SELECT po.ID, po.PO_NUMBER, po.SUPPLIER_NAME, po.PO_DATE,
               po.DELIVERY_DATE, po.STATUS, po.TOTAL_AMOUNT, po.CURRENCY,
               CASE
                   WHEN cs.shipment_count > 0 THEN 1
                   ELSE 0
               END as IS_USED,
               c.CONTRACT_NUMBER, c.SUPPLIER_NAME as CONTRACT_SUPPLIER,
               curr.SYMBOL as CURRENCY_SYMBOL, curr.NAME_AR as CURRENCY_NAME
        FROM PURCHASE_ORDERS po
        LEFT JOIN CONTRACTS c ON po.CONTRACT_ID = c.CONTRACT_ID
        LEFT JOIN CURRENCIES curr ON po.CURRENCY = curr.CODE
        LEFT JOIN (
            SELECT purchase_order_id,
                   COUNT(*) as shipment_count
            FROM cargo_shipments
            WHERE purchase_order_id IS NOT NULL
            GROUP BY purchase_order_id
        ) cs ON po.ID = cs.purchase_order_id
        WHERE 1=1
        """

        params = {}

        # إضافة فلاتر
        if status_filter:
            query += " AND po.STATUS = :status_filter"
            params['status_filter'] = status_filter

        if date_from:
            query += " AND po.PO_DATE >= TO_DATE(:date_from, 'YYYY-MM-DD')"
            params['date_from'] = date_from

        if date_to:
            query += " AND po.PO_DATE <= TO_DATE(:date_to, 'YYYY-MM-DD')"
            params['date_to'] = date_to

        if usage_filter == 'used':
            query += " AND cs.shipment_count > 0"
        elif usage_filter == 'unused':
            query += " AND (cs.shipment_count IS NULL OR cs.shipment_count = 0)"

        query += " ORDER BY po.PO_DATE DESC, po.ID DESC"

        # تنفيذ الاستعلام
        results = oracle_manager.execute_query(query, params)

        # تحويل النتائج إلى قائمة من القواميس
        purchase_orders = []
        for row in results:
            purchase_orders.append({
                'id': row[0],
                'po_number': row[1],
                'supplier_name': row[2],
                'po_date': row[3].isoformat() if row[3] else None,
                'delivery_date': row[4].isoformat() if row[4] else None,
                'status': row[5],
                'total_amount': float(row[6]) if row[6] else 0,
                'currency': row[7],
                'is_used': bool(row[8]),
                'contract_number': row[9],
                'contract_supplier': row[10],
                'currency_symbol': row[11],
                'currency_name': row[12]
            })

        return jsonify({
            'success': True,
            'data': purchase_orders,
            'total_count': len(purchase_orders)
        })

    except Exception as e:
        logger.error(f"❌ خطأ في جلب بيانات أوامر الشراء: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/shared/download/<file_hash>')
def shared_download(file_hash):
    """تحميل ملف مشترك باستخدام hash"""
    try:
        oracle_manager = get_oracle_manager()

        # البحث عن الملف باستخدام hash في الروابط
        search_query = """
            SELECT FILE_PATH, ORIGINAL_FILENAME, FILENAME, TITLE
            FROM PO_DOCUMENTS
            WHERE (NEXTCLOUD_SHARE_LINK LIKE :hash_pattern OR ONEDRIVE_SHARE_LINK LIKE :hash_pattern)
            AND IS_ACTIVE = 1
        """

        hash_pattern = f'%{file_hash}%'
        result = oracle_manager.execute_query(search_query, {'hash_pattern': hash_pattern})

        if not result:
            flash('الرابط غير صحيح أو منتهي الصلاحية', 'error')
            return redirect(url_for('main.index'))

        file_path = result[0][0]
        original_filename = result[0][1]
        filename = result[0][2]
        title = result[0][3]

        download_name = original_filename or filename or title

        if os.path.exists(file_path):
            # تحديث عداد التحميل
            try:
                update_download_query = """
                    UPDATE PO_DOCUMENTS
                    SET DOWNLOAD_COUNT = NVL(DOWNLOAD_COUNT, 0) + 1,
                        LAST_ACCESSED = SYSDATE
                    WHERE FILE_PATH = :file_path
                """
                oracle_manager.execute_update(update_download_query, {'file_path': file_path})
                oracle_manager.commit()
            except Exception as update_error:
                logger.warning(f"⚠️ خطأ في تحديث عداد التحميل: {update_error}")

            logger.info(f"✅ تم تحميل الملف المشترك: {download_name}")
            return send_file(file_path, as_attachment=True, download_name=download_name)
        else:
            flash('الملف غير موجود على الخادم', 'error')
            return redirect(url_for('main.index'))

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل الملف المشترك {file_hash}: {e}")
        flash('حدث خطأ في تحميل الملف', 'error')
        return redirect(url_for('main.index'))

@bp.route('/api/items-analytics-data')
@login_required
def api_items_analytics_data():
    """API لجلب بيانات تحليل الأصناف"""
    try:
        oracle_manager = get_oracle_manager()

        # معاملات التصفية
        category = request.args.get('category', '')
        supplier = request.args.get('supplier', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        abc_class = request.args.get('abc_class', '')

        # بناء الاستعلام الأساسي
        base_query = """
        SELECT
            ITEM_CODE,
            ITEM_NAME,
            po_status,
            SUPPLIER_NAME,
            CURRENCY,
            SUM(QUANTITY) as total_quantity,
            AVG(UNIT_PRICE) as avg_unit_price,
            SUM(TOTAL_PRICE) as total_value,
            COUNT(*) as order_count,
            MAX(PO_DATE) as last_order_date,
            expiry_status,
            delivery_status
        FROM V_PURCHASE_ITEMS_ANALYTICS
        WHERE 1=1
        """

        params = []

        # إضافة شروط التصفية
        if supplier:
            base_query += " AND UPPER(SUPPLIER_NAME) LIKE UPPER(?)"
            params.append(f"%{supplier}%")

        if date_from:
            base_query += " AND PO_DATE >= TO_DATE(?, 'YYYY-MM-DD')"
            params.append(date_from)

        if date_to:
            base_query += " AND PO_DATE <= TO_DATE(?, 'YYYY-MM-DD')"
            params.append(date_to)

        base_query += """
        GROUP BY ITEM_CODE, ITEM_NAME, po_status, SUPPLIER_NAME, CURRENCY, expiry_status, delivery_status
        ORDER BY total_value DESC
        """

        # تنفيذ الاستعلام
        results = oracle_manager.execute_query(base_query, params)

        # تحويل النتائج إلى JSON
        items_data = []
        for row in results:
            items_data.append({
                'item_code': row[0],
                'item_name': row[1],
                'po_status': row[2],
                'supplier_name': row[3],
                'currency': row[4],
                'total_quantity': float(row[5]) if row[5] else 0,
                'avg_unit_price': float(row[6]) if row[6] else 0,
                'total_value': float(row[7]) if row[7] else 0,
                'order_count': int(row[8]) if row[8] else 0,
                'last_order_date': row[9].strftime('%Y-%m-%d') if row[9] else '',
                'expiry_status': row[10],
                'delivery_status': row[11]
            })

        return jsonify({
            'success': True,
            'data': items_data,
            'total': len(items_data)
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API بيانات التحليل: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@bp.route('/api/abc-analysis-data')
@login_required
def api_abc_analysis_data():
    """API لجلب بيانات تحليل ABC"""
    try:
        oracle_manager = get_oracle_manager()

        # أولاً دعنا نتحقق من وجود البيانات في الجداول الأساسية
        check_data_query = """
        SELECT COUNT(*) as total_items,
               COUNT(DISTINCT poi.ITEM_CODE) as unique_items,
               SUM(poi.TOTAL_PRICE) as total_value
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE po.STATUS != 'ملغي'
        """

        data_check = oracle_manager.execute_query(check_data_query)
        logger.info(f"📊 فحص البيانات الأساسية: {data_check[0] if data_check else 'لا توجد بيانات'}")

        # التحقق من وجود الـ view
        view_check_query = """
        SELECT COUNT(*) FROM USER_VIEWS WHERE VIEW_NAME = 'V_ITEMS_ABC_ANALYSIS'
        """

        view_exists = oracle_manager.execute_query(view_check_query)
        logger.info(f"🔍 وجود الـ view: {'موجود' if view_exists and view_exists[0][0] > 0 else 'غير موجود'}")

        # إذا كان الـ view موجود ويحتوي على بيانات، استخدمه
        if view_exists and view_exists[0][0] > 0 and data_check and data_check[0][0] > 0:
            logger.info("✅ استخدام الـ view الموجود V_ITEMS_ABC_ANALYSIS")
            query = """
            SELECT
                ITEM_CODE,
                ITEM_NAME,
                total_value,
                total_quantity,
                order_count,
                avg_price,
                value_rank,
                value_percentage,
                cumulative_percentage,
                abc_classification,
                abc_description
            FROM V_ITEMS_ABC_ANALYSIS
            ORDER BY total_value DESC
            """
        else:
            logger.info("🔄 استخدام استعلام مباشر لإنشاء تحليل ABC")
            query = """
            WITH item_totals AS (
                SELECT
                    poi.ITEM_CODE,
                    poi.ITEM_NAME,
                    SUM(poi.TOTAL_PRICE) as total_value,
                    SUM(poi.QUANTITY) as total_quantity,
                    COUNT(*) as order_count,
                    AVG(poi.UNIT_PRICE) as avg_price
                FROM PO_ITEMS poi
                JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
                WHERE po.STATUS != 'ملغي'
                GROUP BY poi.ITEM_CODE, poi.ITEM_NAME
            ),
            ranked_items AS (
                SELECT *,
                    ROW_NUMBER() OVER (ORDER BY total_value DESC) as value_rank,
                    SUM(total_value) OVER (ORDER BY total_value DESC ROWS UNBOUNDED PRECEDING) as cumulative_value,
                    SUM(total_value) OVER () as grand_total
                FROM item_totals
            )
            SELECT
                ITEM_CODE,
                ITEM_NAME,
                total_value,
                total_quantity,
                order_count,
                ROUND(avg_price, 2) as avg_price,
                value_rank,
                ROUND(total_value / grand_total * 100, 2) as value_percentage,
                ROUND(cumulative_value / grand_total * 100, 2) as cumulative_percentage,
                CASE
                    WHEN cumulative_value / grand_total <= 0.8 THEN 'A'
                    WHEN cumulative_value / grand_total <= 0.95 THEN 'B'
                    ELSE 'C'
                END as abc_classification,
                CASE
                    WHEN cumulative_value / grand_total <= 0.8 THEN 'عالي القيمة - يحتاج مراقبة دقيقة'
                    WHEN cumulative_value / grand_total <= 0.95 THEN 'متوسط القيمة - مراقبة عادية'
                    ELSE 'منخفض القيمة - مراقبة أساسية'
                END as abc_description
            FROM ranked_items
            ORDER BY total_value DESC
            """

        results = oracle_manager.execute_query(query)
        logger.info(f"📊 عدد النتائج المسترجعة: {len(results)}")

        abc_data = []
        for i, row in enumerate(results):
            # طباعة السجل الأول للتشخيص
            if i == 0:
                logger.info(f"🔍 السجل الأول: {row}")
                logger.info(f"📋 عدد الأعمدة: {len(row)}")

            abc_item = {
                'item_code': row[0],
                'item_name': row[1],
                'total_value': float(row[2]) if row[2] else 0,
                'total_quantity': float(row[3]) if row[3] else 0,
                'order_count': int(row[4]) if row[4] else 0,
                'avg_price': float(row[5]) if row[5] else 0,
                'value_rank': int(row[6]) if row[6] else 0,
                'value_percentage': float(row[7]) if row[7] else 0,
                'cumulative_percentage': float(row[8]) if row[8] else 0,
                'abc_classification': row[9] if len(row) > 9 else 'A',  # قيمة افتراضية
                'abc_description': row[10] if len(row) > 10 else 'عالي القيمة'  # قيمة افتراضية
            }

            # طباعة تفاصيل السجل الأول
            if i == 0:
                logger.info(f"🎯 تصنيف ABC: {abc_item['abc_classification']}")
                logger.info(f"📝 وصف ABC: {abc_item['abc_description']}")
                logger.info(f"📦 الكمية الإجمالية: {abc_item['total_quantity']}")
                logger.info(f"🔢 عدد الطلبات: {abc_item['order_count']}")
                logger.info(f"💰 القيمة الإجمالية: {abc_item['total_value']}")

            abc_data.append(abc_item)

        return jsonify({
            'success': True,
            'data': abc_data
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API تحليل ABC: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@bp.route('/api/supplier-analysis-data')
@login_required
def api_supplier_analysis_data():
    """API لجلب بيانات تحليل الموردين"""
    try:
        oracle_manager = get_oracle_manager()

        item_code = request.args.get('item_code', '')

        query = """
        SELECT
            SUPPLIER_NAME,
            SUPPLIER_CODE,
            ITEM_CODE,
            ITEM_NAME,
            order_count,
            total_quantity,
            avg_unit_price,
            min_unit_price,
            max_unit_price,
            total_value,
            price_std_deviation,
            supplier_rank_for_item,
            last_order_date,
            avg_delivery_days
        FROM V_SUPPLIER_ITEMS_ANALYSIS
        WHERE 1=1
        """

        params = []
        if item_code:
            query += " AND ITEM_CODE = ?"
            params.append(item_code)

        query += " ORDER BY total_value DESC"

        results = oracle_manager.execute_query(query, params)

        supplier_data = []
        for row in results:
            supplier_data.append({
                'supplier_name': row[0],
                'supplier_code': row[1],
                'item_code': row[2],
                'item_name': row[3],
                'order_count': int(row[4]) if row[4] else 0,
                'total_quantity': float(row[5]) if row[5] else 0,
                'avg_unit_price': float(row[6]) if row[6] else 0,
                'min_unit_price': float(row[7]) if row[7] else 0,
                'max_unit_price': float(row[8]) if row[8] else 0,
                'total_value': float(row[9]) if row[9] else 0,
                'price_std_deviation': float(row[10]) if row[10] else 0,
                'supplier_rank': int(row[11]) if row[11] else 0,
                'last_order_date': row[12].strftime('%Y-%m-%d') if row[12] else '',
                'avg_delivery_days': float(row[13]) if row[13] else 0
            })

        return jsonify({
            'success': True,
            'data': supplier_data
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API تحليل الموردين: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@bp.route('/api/monthly-trends-data')
@login_required
def api_monthly_trends_data():
    """API لجلب بيانات الاتجاهات الشهرية"""
    try:
        oracle_manager = get_oracle_manager()

        months_back = int(request.args.get('months_back', 12))
        period_type = request.args.get('period_type', 'monthly')
        analysis_type = request.args.get('analysis_type', 'value')
        item_code = request.args.get('item_code', '')

        logger.info(f"🔍 طلب بيانات التطور الزمني: months_back={months_back}, period_type={period_type}, analysis_type={analysis_type}")

        # أولاً: التحقق من وجود البيانات بدون شروط
        basic_check_query = """
        SELECT COUNT(*) as total_po,
               COUNT(DISTINCT po.ID) as distinct_po,
               MIN(po.PO_DATE) as earliest_date,
               MAX(po.PO_DATE) as latest_date
        FROM PURCHASE_ORDERS po
        """

        basic_results = oracle_manager.execute_query(basic_check_query)
        if basic_results:
            total_po, distinct_po, earliest, latest = basic_results[0]
            logger.info(f"📊 إجمالي أوامر الشراء: {total_po}, مميزة: {distinct_po}")
            logger.info(f"📅 أقدم تاريخ: {earliest}, أحدث تاريخ: {latest}")

        # التحقق من أصناف أوامر الشراء
        items_check_query = """
        SELECT COUNT(*) as total_items
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        """

        items_results = oracle_manager.execute_query(items_check_query)
        total_items = items_results[0][0] if items_results else 0
        logger.info(f"📦 إجمالي أصناف أوامر الشراء: {total_items}")

        # التحقق مع الشروط
        check_query = f"""
        SELECT COUNT(*) as total_records
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE NVL(po.STATUS, 'نشط') != 'ملغي'
        AND po.PO_DATE >= ADD_MONTHS(SYSDATE, -{months_back})
        """

        check_results = oracle_manager.execute_query(check_query)
        total_records = check_results[0][0] if check_results else 0
        logger.info(f"📊 السجلات مع الشروط: {total_records}")

        # عرض عينة من البيانات الفعلية مع العملة
        sample_query = """
        SELECT po.ID, po.PO_NUMBER, po.PO_DATE, po.STATUS, po.CURRENCY, poi.ITEM_NAME, poi.TOTAL_PRICE
        FROM PURCHASE_ORDERS po
        JOIN PO_ITEMS poi ON po.ID = poi.PO_ID
        WHERE ROWNUM <= 5
        ORDER BY po.PO_DATE DESC
        """

        sample_results = oracle_manager.execute_query(sample_query)
        logger.info(f"📋 عينة من البيانات ({len(sample_results)} سجل):")
        for i, row in enumerate(sample_results):
            logger.info(f"  {i+1}. أمر: {row[1]}, تاريخ: {row[2]}, حالة: {row[3]}, عملة: {row[4]}, صنف: {row[5]}, قيمة: {row[6]}")

        # التحقق من العملات المستخدمة
        currency_query = """
        SELECT DISTINCT po.CURRENCY, COUNT(*) as count
        FROM PURCHASE_ORDERS po
        GROUP BY po.CURRENCY
        ORDER BY count DESC
        """

        currency_results = oracle_manager.execute_query(currency_query)
        logger.info(f"💰 العملات المستخدمة في أوامر الشراء:")
        for row in currency_results:
            currency = row[0] if row[0] else 'غير محدد'
            count = int(row[1]) if row[1] else 0
            logger.info(f"  العملة: {currency} - عدد الأوامر: {count}")

        # استعلام مبسط للاتجاهات الزمنية
        query = f"""
        SELECT
            TO_CHAR(po.PO_DATE, 'YYYY-MM') as order_month,
            EXTRACT(YEAR FROM po.PO_DATE) as order_year,
            EXTRACT(MONTH FROM po.PO_DATE) as order_month_num,
            COUNT(DISTINCT po.ID) as monthly_orders,
            SUM(poi.QUANTITY) as monthly_quantity,
            ROUND(SUM(poi.TOTAL_PRICE), 2) as monthly_value,
            ROUND(AVG(poi.UNIT_PRICE), 2) as monthly_avg_price,
            COUNT(DISTINCT poi.ITEM_CODE) as unique_items
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE NVL(po.STATUS, 'نشط') != 'ملغي'
        AND po.PO_DATE >= ADD_MONTHS(SYSDATE, -{months_back})
        """

        if item_code:
            query += f" AND poi.ITEM_CODE = '{item_code}'"

        query += """
        GROUP BY TO_CHAR(po.PO_DATE, 'YYYY-MM'), EXTRACT(YEAR FROM po.PO_DATE), EXTRACT(MONTH FROM po.PO_DATE)
        ORDER BY order_month
        """

        results = oracle_manager.execute_query(query)

        logger.info(f"📊 نتائج استعلام التطور الزمني: {len(results)} سجل")

        trends_data = []
        for i, row in enumerate(results):
            row_data = {
                'order_month': row[0],
                'order_year': int(row[1]) if row[1] else 0,
                'order_month_num': int(row[2]) if row[2] else 0,
                'monthly_orders': int(row[3]) if row[3] else 0,
                'monthly_quantity': float(row[4]) if row[4] else 0,
                'monthly_value': float(row[5]) if row[5] else 0,
                'monthly_avg_price': float(row[6]) if row[6] else 0,
                'unique_items': int(row[7]) if row[7] else 0
            }
            trends_data.append(row_data)

            # طباعة أول 3 سجلات للتحقق
            if i < 3:
                logger.info(f"📈 سجل {i+1}: {row_data['order_month']} - طلبات: {row_data['monthly_orders']} - قيمة: {row_data['monthly_value']:,.2f}")

        logger.info(f"📊 إجمالي البيانات المرسلة: {len(trends_data)} سجل")

        return jsonify({
            'success': True,
            'data': trends_data
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API الاتجاهات الشهرية: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@bp.route('/api/items-growth-analysis')
@login_required
def api_items_growth_analysis():
    """API لتحليل نمو وتراجع الأصناف"""
    try:
        oracle_manager = get_oracle_manager()
        months_back = int(request.args.get('months_back', 12))

        # تحليل نمو الأصناف
        growth_query = f"""
        WITH period_data AS (
            SELECT
                poi.ITEM_CODE,
                poi.ITEM_NAME,
                TO_CHAR(po.PO_DATE, 'YYYY-MM') as order_month,
                SUM(poi.TOTAL_PRICE) as monthly_value
            FROM PO_ITEMS poi
            JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
            WHERE NVL(po.STATUS, 'نشط') != 'ملغي'
            AND po.PO_DATE >= ADD_MONTHS(SYSDATE, -{months_back})
            GROUP BY poi.ITEM_CODE, poi.ITEM_NAME, TO_CHAR(po.PO_DATE, 'YYYY-MM')
        ),
        item_comparison AS (
            SELECT
                ITEM_CODE,
                ITEM_NAME,
                SUM(CASE WHEN order_month >= TO_CHAR(ADD_MONTHS(SYSDATE, -{months_back//2}), 'YYYY-MM')
                    THEN monthly_value ELSE 0 END) as recent_value,
                SUM(CASE WHEN order_month < TO_CHAR(ADD_MONTHS(SYSDATE, -{months_back//2}), 'YYYY-MM')
                    THEN monthly_value ELSE 0 END) as previous_value
            FROM period_data
            GROUP BY ITEM_CODE, ITEM_NAME
            HAVING SUM(monthly_value) > 0
        )
        SELECT
            ITEM_CODE,
            ITEM_NAME,
            ROUND(recent_value, 2) as current_value,
            ROUND(previous_value, 2) as previous_value,
            CASE
                WHEN previous_value > 0 THEN
                    ROUND((recent_value - previous_value) / previous_value * 100, 2)
                ELSE NULL
            END as growth_percentage
        FROM item_comparison
        WHERE previous_value > 0 AND recent_value > 0
        ORDER BY growth_percentage DESC NULLS LAST
        """

        results = oracle_manager.execute_query(growth_query)

        growing_items = []
        declining_items = []

        for row in results:
            item_data = {
                'item_code': row[0],
                'item_name': row[1],
                'current_value': float(row[2]) if row[2] else 0,
                'previous_value': float(row[3]) if row[3] else 0,
                'growth_percentage': float(row[4]) if row[4] else 0
            }

            if item_data['growth_percentage'] > 5:  # نمو أكثر من 5%
                growing_items.append(item_data)
            elif item_data['growth_percentage'] < -5:  # تراجع أكثر من 5%
                declining_items.append(item_data)

        # ترتيب النتائج
        growing_items.sort(key=lambda x: x['growth_percentage'], reverse=True)
        declining_items.sort(key=lambda x: x['growth_percentage'])

        return jsonify({
            'success': True,
            'growing_items': growing_items[:10],  # أعلى 10 أصناف نمواً
            'declining_items': declining_items[:10]  # أعلى 10 أصناف تراجعاً
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API تحليل نمو الأصناف: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@bp.route('/api/category-analysis-data')
@login_required
def api_category_analysis_data():
    """API لجلب بيانات تحليل الفئات"""
    try:
        oracle_manager = get_oracle_manager()

        query = """
        SELECT
            ic.CATEGORY_CODE,
            ic.CATEGORY_NAME_AR,
            ic.CATEGORY_NAME_EN,
            COUNT(DISTINCT poi.ITEM_CODE) as unique_items_count,
            COUNT(*) as total_orders,
            SUM(poi.QUANTITY) as total_quantity,
            SUM(poi.TOTAL_PRICE) as total_value,
            AVG(poi.UNIT_PRICE) as avg_unit_price,
            COUNT(DISTINCT po.SUPPLIER_NAME) as suppliers_count,
            AVG(po.DELIVERY_DATE - po.PO_DATE) as avg_delivery_days
        FROM ITEM_CATEGORIES ic
        LEFT JOIN PO_ITEMS poi ON SUBSTR(poi.ITEM_CODE, 1, LENGTH(ic.CATEGORY_CODE)) = ic.CATEGORY_CODE
        LEFT JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID AND NVL(po.STATUS, 'نشط') != 'ملغي'
        WHERE ic.IS_ACTIVE = 1
        GROUP BY ic.CATEGORY_CODE, ic.CATEGORY_NAME_AR, ic.CATEGORY_NAME_EN
        ORDER BY total_value DESC NULLS LAST
        """

        results = oracle_manager.execute_query(query)

        # حساب إجمالي القيمة للنسب المئوية
        total_value_all = sum([float(row[6]) if row[6] else 0 for row in results])

        category_data = []
        for row in results:
            total_value = float(row[6]) if row[6] else 0
            category_percentage = (total_value / total_value_all * 100) if total_value_all > 0 else 0

            category_data.append({
                'category_code': row[0],
                'category_name_ar': row[1],
                'category_name_en': row[2],
                'unique_items_count': int(row[3]) if row[3] else 0,
                'total_orders': int(row[4]) if row[4] else 0,
                'total_quantity': float(row[5]) if row[5] else 0,
                'total_value': total_value,
                'avg_unit_price': float(row[7]) if row[7] else 0,
                'suppliers_count': int(row[8]) if row[8] else 0,
                'avg_delivery_days': float(row[9]) if row[9] else 0,
                'category_percentage': round(category_percentage, 2)
            })

        return jsonify({
            'success': True,
            'data': category_data,
            'total_value': total_value_all
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API تحليل الفئات: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@bp.route('/api/top-items-chart-data')
@login_required
def api_top_items_chart_data():
    """API لجلب بيانات رسم أعلى الأصناف"""
    try:
        oracle_manager = get_oracle_manager()

        limit = int(request.args.get('limit', 10))
        chart_type = request.args.get('chart_type', 'value')  # value, quantity, orders

        if chart_type == 'quantity':
            order_by = 'SUM(poi.QUANTITY) DESC'
            value_field = 'SUM(poi.QUANTITY)'
        elif chart_type == 'orders':
            order_by = 'COUNT(*) DESC'
            value_field = 'COUNT(*)'
        else:  # value
            order_by = 'SUM(poi.TOTAL_PRICE) DESC'
            value_field = 'SUM(poi.TOTAL_PRICE)'

        query = f"""
        SELECT
            poi.ITEM_CODE,
            poi.ITEM_NAME,
            {value_field} as chart_value,
            SUM(poi.TOTAL_PRICE) as total_value,
            SUM(poi.QUANTITY) as total_quantity,
            COUNT(*) as order_count,
            AVG(poi.UNIT_PRICE) as avg_price
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE NVL(po.STATUS, 'نشط') != 'ملغي'
        GROUP BY poi.ITEM_CODE, poi.ITEM_NAME
        ORDER BY {order_by}
        FETCH FIRST {limit} ROWS ONLY
        """

        results = oracle_manager.execute_query(query)

        chart_data = []
        for row in results:
            chart_data.append({
                'item_code': row[0],
                'item_name': row[1],
                'chart_value': float(row[2]) if row[2] else 0,
                'total_value': float(row[3]) if row[3] else 0,
                'total_quantity': float(row[4]) if row[4] else 0,
                'order_count': int(row[5]) if row[5] else 0,
                'avg_price': float(row[6]) if row[6] else 0
            })

        return jsonify({
            'success': True,
            'data': chart_data,
            'chart_type': chart_type
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API رسم أعلى الأصناف: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@bp.route('/api/executive-summary-report')
@login_required
def api_executive_summary_report():
    """API لجلب التقرير التنفيذي الشامل"""
    try:
        oracle_manager = get_oracle_manager()

        period = request.args.get('period', 'monthly')  # monthly, quarterly, yearly
        months_back = int(request.args.get('months_back', 12))

        # تحديد فترة التجميع
        if period == 'quarterly':
            date_format = 'YYYY-Q'
            date_trunc = "TO_CHAR(po.PO_DATE, 'YYYY') || '-Q' || TO_CHAR(po.PO_DATE, 'Q')"
        elif period == 'yearly':
            date_format = 'YYYY'
            date_trunc = "TO_CHAR(po.PO_DATE, 'YYYY')"
        else:  # monthly
            date_format = 'YYYY-MM'
            date_trunc = "TO_CHAR(po.PO_DATE, 'YYYY-MM')"

        # الاستعلام الرئيسي للتقرير التنفيذي - مبسط
        summary_query = f"""
        SELECT
            {date_trunc} as period_key,
            COUNT(DISTINCT po.ID) as total_orders,
            COUNT(DISTINCT poi.ITEM_CODE) as unique_items,
            COUNT(DISTINCT po.SUPPLIER_NAME) as unique_suppliers,
            ROUND(SUM(poi.TOTAL_PRICE), 2) as total_value,
            SUM(poi.QUANTITY) as total_quantity,
            ROUND(AVG(poi.UNIT_PRICE), 2) as avg_unit_price,
            COUNT(*) as total_line_items,
            ROUND(SUM(CASE WHEN NVL(po.STATUS, 'نشط') = 'مكتمل' THEN poi.TOTAL_PRICE ELSE 0 END), 2) as completed_value,
            ROUND(SUM(CASE WHEN NVL(po.STATUS, 'نشط') = 'معلق' THEN poi.TOTAL_PRICE ELSE 0 END), 2) as pending_value,
            ROUND(AVG(CASE WHEN po.DELIVERY_DATE IS NOT NULL AND po.PO_DATE IS NOT NULL
                THEN po.DELIVERY_DATE - po.PO_DATE ELSE NULL END), 1) as avg_delivery_days,
            ROUND(MAX(poi.TOTAL_PRICE), 2) as max_item_value,
            0 as value_change_percentage,
            0 as orders_change_percentage,
            ROUND(SUM(CASE WHEN NVL(po.STATUS, 'نشط') = 'مكتمل' THEN poi.TOTAL_PRICE ELSE 0 END) /
                  NULLIF(SUM(poi.TOTAL_PRICE), 0) * 100, 2) as completion_rate,
            ROUND(SUM(poi.TOTAL_PRICE) / NULLIF(COUNT(DISTINCT po.ID), 0), 2) as avg_order_value
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE po.PO_DATE >= ADD_MONTHS(SYSDATE, -{months_back})
        AND NVL(po.STATUS, 'نشط') != 'ملغي'
        GROUP BY {date_trunc}
        ORDER BY period_key
        """

        results = oracle_manager.execute_query(summary_query)

        summary_data = []
        for row in results:
            summary_data.append({
                'period': row[0],
                'total_orders': int(row[1]) if row[1] else 0,
                'unique_items': int(row[2]) if row[2] else 0,
                'unique_suppliers': int(row[3]) if row[3] else 0,
                'total_value': float(row[4]) if row[4] else 0,
                'total_quantity': float(row[5]) if row[5] else 0,
                'avg_unit_price': float(row[6]) if row[6] else 0,
                'total_line_items': int(row[7]) if row[7] else 0,
                'completed_value': float(row[8]) if row[8] else 0,
                'pending_value': float(row[9]) if row[9] else 0,
                'avg_delivery_days': float(row[10]) if row[10] else 0,
                'max_item_value': float(row[11]) if row[11] else 0,
                'value_change_percentage': float(row[12]) if row[12] else 0,
                'orders_change_percentage': float(row[13]) if row[13] else 0,
                'completion_rate': float(row[14]) if row[14] else 0,
                'avg_order_value': float(row[15]) if row[15] else 0
            })

        # إحصائيات إضافية - استعلام مبسط
        kpi_query = f"""
        SELECT
            COUNT(DISTINCT poi.ITEM_CODE) as total_unique_items,
            COUNT(DISTINCT po.SUPPLIER_NAME) as total_suppliers,
            SUM(poi.TOTAL_PRICE) as grand_total_value,
            AVG(poi.UNIT_PRICE) as overall_avg_price
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE po.PO_DATE >= ADD_MONTHS(SYSDATE, -{months_back})
        AND NVL(po.STATUS, 'نشط') != 'ملغي'
        """

        kpi_results = oracle_manager.execute_query(kpi_query)

        # أعلى 5 أصناف - استعلام منفصل
        top_items_query = f"""
        SELECT poi.ITEM_NAME, SUM(poi.TOTAL_PRICE) as item_total
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE po.PO_DATE >= ADD_MONTHS(SYSDATE, -{months_back})
        AND NVL(po.STATUS, 'نشط') != 'ملغي'
        GROUP BY poi.ITEM_NAME
        ORDER BY item_total DESC
        FETCH FIRST 5 ROWS ONLY
        """

        top_items_results = oracle_manager.execute_query(top_items_query)

        # أعلى 5 موردين - استعلام منفصل
        top_suppliers_query = f"""
        SELECT po.SUPPLIER_NAME, SUM(poi.TOTAL_PRICE) as supplier_total
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE po.PO_DATE >= ADD_MONTHS(SYSDATE, -{months_back})
        AND NVL(po.STATUS, 'نشط') != 'ملغي'
        GROUP BY po.SUPPLIER_NAME
        ORDER BY supplier_total DESC
        FETCH FIRST 5 ROWS ONLY
        """

        top_suppliers_results = oracle_manager.execute_query(top_suppliers_query)
        kpi_data = kpi_results[0] if kpi_results else [0, 0, 0, 0]

        # تجميع أعلى 5 أصناف
        top_items_list = []
        if top_items_results:
            for row in top_items_results:
                if row[0]:  # اسم الصنف
                    top_items_list.append(str(row[0]))
        top_items_data = ', '.join(top_items_list)

        # تجميع أعلى 5 موردين
        top_suppliers_list = []
        if top_suppliers_results:
            for row in top_suppliers_results:
                if row[0]:  # اسم المورد
                    top_suppliers_list.append(str(row[0]))
        top_suppliers_data = ', '.join(top_suppliers_list)

        return jsonify({
            'success': True,
            'period_data': summary_data,
            'kpi_data': {
                'total_unique_items': int(kpi_data[0]) if kpi_data[0] else 0,
                'total_suppliers': int(kpi_data[1]) if kpi_data[1] else 0,
                'grand_total_value': float(kpi_data[2]) if kpi_data[2] else 0,
                'overall_avg_price': float(kpi_data[3]) if kpi_data[3] else 0,
                'top_5_items': top_items_data,
                'top_5_suppliers': top_suppliers_data
            },
            'period': period,
            'months_back': months_back
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API التقرير التنفيذي: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@bp.route('/api/savings-analysis-report')
@login_required
def api_savings_analysis_report():
    """API لجلب تقرير تحليل الوفورات"""
    try:
        oracle_manager = get_oracle_manager()

        months_back = int(request.args.get('months_back', 12))

        # تحليل الوفورات المحتملة
        savings_query = f"""
        WITH item_price_analysis AS (
            SELECT
                poi.ITEM_CODE,
                poi.ITEM_NAME,
                po.SUPPLIER_NAME,
                AVG(poi.UNIT_PRICE) as avg_price,
                MIN(poi.UNIT_PRICE) as min_price,
                MAX(poi.UNIT_PRICE) as max_price,
                SUM(poi.QUANTITY) as total_quantity,
                SUM(poi.TOTAL_PRICE) as total_spent,
                COUNT(*) as order_count
            FROM PO_ITEMS poi
            JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
            WHERE po.PO_DATE >= ADD_MONTHS(SYSDATE, -{months_back})
            AND NVL(po.STATUS, 'نشط') != 'ملغي'
            GROUP BY poi.ITEM_CODE, poi.ITEM_NAME, po.SUPPLIER_NAME
        ),
        best_prices AS (
            SELECT
                ITEM_CODE,
                ITEM_NAME,
                MIN(avg_price) as best_avg_price,
                SUM(total_quantity) as item_total_quantity,
                SUM(total_spent) as item_total_spent
            FROM item_price_analysis
            GROUP BY ITEM_CODE, ITEM_NAME
        ),
        savings_potential AS (
            SELECT
                ipa.ITEM_CODE,
                ipa.ITEM_NAME,
                ipa.SUPPLIER_NAME,
                ipa.avg_price,
                bp.best_avg_price,
                ipa.total_quantity,
                ipa.total_spent,
                bp.item_total_quantity,
                bp.item_total_spent,

                -- حساب الوفورات المحتملة
                (ipa.avg_price - bp.best_avg_price) as price_difference,
                (ipa.avg_price - bp.best_avg_price) * ipa.total_quantity as potential_savings,

                -- نسبة الوفورات
                CASE
                    WHEN bp.best_avg_price > 0 THEN
                        ROUND((ipa.avg_price - bp.best_avg_price) / bp.best_avg_price * 100, 2)
                    ELSE 0
                END as savings_percentage

            FROM item_price_analysis ipa
            JOIN best_prices bp ON ipa.ITEM_CODE = bp.ITEM_CODE
            WHERE ipa.avg_price > bp.best_avg_price
        )
        SELECT
            ITEM_CODE,
            ITEM_NAME,
            SUPPLIER_NAME,
            ROUND(avg_price, 2) as avg_price,
            ROUND(best_avg_price, 2) as best_avg_price,
            total_quantity,
            ROUND(total_spent, 2) as total_spent,
            ROUND(price_difference, 2) as price_difference,
            ROUND(potential_savings, 2) as potential_savings,
            savings_percentage
        FROM savings_potential
        WHERE potential_savings > 0
        ORDER BY potential_savings DESC
        """

        savings_results = oracle_manager.execute_query(savings_query)

        savings_data = []
        total_potential_savings = 0

        for row in savings_results:
            potential_savings = float(row[8]) if row[8] else 0
            total_potential_savings += potential_savings

            savings_data.append({
                'item_code': row[0],
                'item_name': row[1],
                'supplier_name': row[2],
                'avg_price': float(row[3]) if row[3] else 0,
                'best_avg_price': float(row[4]) if row[4] else 0,
                'total_quantity': float(row[5]) if row[5] else 0,
                'total_spent': float(row[6]) if row[6] else 0,
                'price_difference': float(row[7]) if row[7] else 0,
                'potential_savings': potential_savings,
                'savings_percentage': float(row[9]) if row[9] else 0
            })

        # إحصائيات الوفورات
        savings_summary_query = f"""
        SELECT
            COUNT(DISTINCT poi.ITEM_CODE) as items_with_savings_potential,
            COUNT(DISTINCT po.SUPPLIER_NAME) as suppliers_count,
            SUM(poi.TOTAL_PRICE) as total_spent_period,
            AVG(poi.UNIT_PRICE) as avg_price_all_items
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE po.PO_DATE >= ADD_MONTHS(SYSDATE, -{months_back})
        AND NVL(po.STATUS, 'نشط') != 'ملغي'
        """

        summary_results = oracle_manager.execute_query(savings_summary_query)
        summary_data = summary_results[0] if summary_results else [0, 0, 0, 0]

        return jsonify({
            'success': True,
            'savings_opportunities': savings_data,
            'summary': {
                'total_potential_savings': round(total_potential_savings, 2),
                'items_with_savings_potential': int(summary_data[0]) if summary_data[0] else 0,
                'suppliers_count': int(summary_data[1]) if summary_data[1] else 0,
                'total_spent_period': float(summary_data[2]) if summary_data[2] else 0,
                'avg_price_all_items': float(summary_data[3]) if summary_data[3] else 0,
                'savings_percentage_of_total': round(
                    (total_potential_savings / float(summary_data[2]) * 100) if summary_data[2] and summary_data[2] > 0 else 0, 2
                )
            },
            'months_back': months_back
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API تحليل الوفورات: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@bp.route('/api/export-items-data')
@login_required
def api_export_items_data():
    """API لتصدير بيانات الأصناف بصيغ مختلفة"""
    try:
        oracle_manager = get_oracle_manager()

        export_type = request.args.get('type', 'excel')  # excel, csv, json
        include_charts = request.args.get('include_charts', 'false').lower() == 'true'

        # جلب البيانات الأساسية
        items_query = """
        SELECT
            poi.ITEM_CODE,
            poi.ITEM_NAME,
            po.SUPPLIER_NAME,
            SUM(poi.QUANTITY) as total_quantity,
            AVG(poi.UNIT_PRICE) as avg_unit_price,
            SUM(poi.TOTAL_PRICE) as total_value,
            COUNT(*) as order_count,
            MAX(po.PO_DATE) as last_order_date,
            po.CURRENCY,
            CASE
                WHEN poi.EXPIRY_DATE IS NOT NULL AND poi.EXPIRY_DATE <= SYSDATE + 30 THEN 'منتهي الصلاحية قريباً'
                WHEN poi.EXPIRY_DATE IS NOT NULL AND poi.EXPIRY_DATE <= SYSDATE THEN 'منتهي الصلاحية'
                ELSE 'صالح'
            END as expiry_status,
            CASE
                WHEN po.DELIVERY_DATE < SYSDATE AND NVL(po.STATUS, 'نشط') NOT IN ('مكتمل', 'مستلم') THEN 'متأخر'
                WHEN po.DELIVERY_DATE BETWEEN SYSDATE AND SYSDATE + 7 THEN 'مستحق قريباً'
                ELSE 'في الموعد'
            END as delivery_status
        FROM PO_ITEMS poi
        JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
        WHERE NVL(po.STATUS, 'نشط') != 'ملغي'
        GROUP BY poi.ITEM_CODE, poi.ITEM_NAME, po.SUPPLIER_NAME, po.CURRENCY,
                 poi.EXPIRY_DATE, po.DELIVERY_DATE, po.STATUS
        ORDER BY total_value DESC
        """

        items_results = oracle_manager.execute_query(items_query)

        # تحويل البيانات
        export_data = []
        for row in items_results:
            export_data.append({
                'كود الصنف': row[0],
                'اسم الصنف': row[1],
                'المورد': row[2],
                'الكمية الإجمالية': float(row[3]) if row[3] else 0,
                'متوسط السعر': float(row[4]) if row[4] else 0,
                'القيمة الإجمالية': float(row[5]) if row[5] else 0,
                'عدد الطلبات': int(row[6]) if row[6] else 0,
                'آخر طلب': row[7].strftime('%Y-%m-%d') if row[7] else '',
                'العملة': row[8],
                'حالة الصلاحية': row[9],
                'حالة التسليم': row[10]
            })

        # إضافة بيانات إضافية إذا طُلبت
        additional_data = {}
        if include_charts:
            # بيانات تحليل ABC
            abc_query = """
            SELECT
                ITEM_CODE,
                ITEM_NAME,
                total_value,
                abc_classification,
                value_percentage
            FROM V_ITEMS_ABC_ANALYSIS
            ORDER BY total_value DESC
            FETCH FIRST 20 ROWS ONLY
            """

            abc_results = oracle_manager.execute_query(abc_query)
            additional_data['abc_analysis'] = []
            for row in abc_results:
                additional_data['abc_analysis'].append({
                    'كود الصنف': row[0],
                    'اسم الصنف': row[1],
                    'القيمة الإجمالية': float(row[2]) if row[2] else 0,
                    'تصنيف ABC': row[3],
                    'نسبة القيمة %': float(row[4]) if row[4] else 0
                })

            # إحصائيات الموردين
            suppliers_query = """
            SELECT
                SUPPLIER_NAME,
                COUNT(DISTINCT ITEM_CODE) as items_count,
                SUM(total_value) as supplier_total_value
            FROM V_SUPPLIER_ITEMS_ANALYSIS
            GROUP BY SUPPLIER_NAME
            ORDER BY supplier_total_value DESC
            FETCH FIRST 10 ROWS ONLY
            """

            suppliers_results = oracle_manager.execute_query(suppliers_query)
            additional_data['top_suppliers'] = []
            for row in suppliers_results:
                additional_data['top_suppliers'].append({
                    'المورد': row[0],
                    'عدد الأصناف': int(row[1]) if row[1] else 0,
                    'القيمة الإجمالية': float(row[2]) if row[2] else 0
                })

        return jsonify({
            'success': True,
            'data': export_data,
            'additional_data': additional_data,
            'export_type': export_type,
            'timestamp': datetime.now().isoformat(),
            'total_records': len(export_data)
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API التصدير: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@bp.route('/generate-pdf-report')
@login_required
def generate_pdf_report():
    """إنشاء تقرير PDF متقدم"""
    try:
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.lib import colors
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
        from reportlab.lib.units import inch
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import io

        oracle_manager = get_oracle_manager()

        # إنشاء buffer للـ PDF
        buffer = io.BytesIO()

        # إنشاء المستند
        doc = SimpleDocTemplate(
            buffer,
            pagesize=landscape(A4),
            rightMargin=0.5*inch,
            leftMargin=0.5*inch,
            topMargin=1*inch,
            bottomMargin=0.5*inch
        )

        # إعداد الأنماط
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # وسط
        )

        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            alignment=2  # يمين
        )

        # محتوى التقرير
        story = []

        # العنوان
        story.append(Paragraph("تقرير تحليل أصناف أوامر الشراء المتقدم", title_style))
        story.append(Paragraph(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}", styles['Normal']))
        story.append(Spacer(1, 20))

        # الإحصائيات العامة
        dashboard_query = """
        SELECT
            total_unique_items,
            total_item_orders,
            total_items_value,
            total_suppliers
        FROM V_ITEMS_DASHBOARD_STATS
        """

        dashboard_results = oracle_manager.execute_query(dashboard_query)
        if dashboard_results:
            stats = dashboard_results[0]

            story.append(Paragraph("الإحصائيات العامة", heading_style))

            stats_data = [
                ['البيان', 'القيمة'],
                ['إجمالي الأصناف الفريدة', f"{stats[0]:,}" if stats[0] else "0"],
                ['إجمالي طلبات الأصناف', f"{stats[1]:,}" if stats[1] else "0"],
                ['إجمالي قيمة الأصناف', f"{stats[2]:,.2f} ر.س" if stats[2] else "0.00 ر.س"],
                ['إجمالي الموردين', f"{stats[3]:,}" if stats[3] else "0"]
            ]

            stats_table = Table(stats_data, colWidths=[3*inch, 2*inch])
            stats_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(stats_table)
            story.append(Spacer(1, 20))

        # أعلى 10 أصناف
        top_items_query = """
        SELECT
            ITEM_CODE,
            ITEM_NAME,
            total_value,
            total_quantity,
            order_count
        FROM V_ITEMS_ABC_ANALYSIS
        WHERE ROWNUM <= 10
        ORDER BY total_value DESC
        """

        top_items_results = oracle_manager.execute_query(top_items_query)

        if top_items_results:
            story.append(Paragraph("أعلى 10 أصناف حسب القيمة", heading_style))

            items_data = [['#', 'كود الصنف', 'اسم الصنف', 'القيمة الإجمالية', 'الكمية', 'عدد الطلبات']]

            for i, row in enumerate(top_items_results, 1):
                items_data.append([
                    str(i),
                    row[0] or '',
                    row[1] or '',
                    f"{row[2]:,.2f} ر.س" if row[2] else "0.00 ر.س",
                    f"{row[3]:,.0f}" if row[3] else "0",
                    str(row[4]) if row[4] else "0"
                ])

            items_table = Table(items_data, colWidths=[0.5*inch, 1*inch, 3*inch, 1.5*inch, 1*inch, 1*inch])
            items_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 1), (-1, -1), 8)
            ]))

            story.append(items_table)

        # بناء المستند
        doc.build(story)

        # إعداد الاستجابة
        buffer.seek(0)

        response = make_response(buffer.getvalue())
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=تقرير_أصناف_أوامر_الشراء_{datetime.now().strftime("%Y%m%d_%H%M")}.pdf'

        return response

    except ImportError:
        flash('مكتبة ReportLab غير مثبتة. يرجى تثبيتها لاستخدام ميزة PDF', 'warning')
        return redirect(url_for('purchase_orders.items_analysis_advanced'))
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء تقرير PDF: {e}")
        flash(f'حدث خطأ في إنشاء التقرير: {str(e)}', 'error')
        return redirect(url_for('purchase_orders.items_analysis_advanced'))


@bp.route('/items-analysis-advanced')
@login_required
def items_analysis_advanced():
    """صفحة تحليل أصناف أوامر الشراء المتقدمة"""
    try:
        logger.info("🔍 عرض صفحة تحليل أصناف أوامر الشراء المتقدمة")

        oracle_manager = get_oracle_manager()

        # جلب إحصائيات لوحة المعلومات
        dashboard_query = """
        SELECT
            total_unique_items,
            total_item_orders,
            total_items_value,
            total_suppliers,
            last_updated
        FROM V_ITEMS_DASHBOARD_STATS
        """

        dashboard_stats = oracle_manager.execute_query(dashboard_query)
        dashboard_data = dashboard_stats[0] if dashboard_stats else [0, 0, 0, 0, None]

        # جلب أعلى 10 أصناف حسب القيمة من تحليل ABC
        top_items_query = """
        SELECT
            ITEM_CODE,
            ITEM_NAME,
            total_value,
            total_quantity,
            order_count,
            abc_classification,
            value_percentage
        FROM V_ITEMS_ABC_ANALYSIS
        WHERE ROWNUM <= 10
        ORDER BY total_value DESC
        """

        top_items = oracle_manager.execute_query(top_items_query)

        # جلب إحصائيات الفئات
        categories_query = """
        SELECT
            CATEGORY_CODE,
            CATEGORY_NAME_AR,
            COUNT(*) as items_count
        FROM ITEM_CATEGORIES
        WHERE IS_ACTIVE = 1
        GROUP BY CATEGORY_CODE, CATEGORY_NAME_AR
        ORDER BY CATEGORY_NAME_AR
        """

        categories = oracle_manager.execute_query(categories_query)

        return render_template('purchase_orders/items_analysis_advanced.html',
                             dashboard_data=dashboard_data,
                             top_items=top_items,
                             categories=categories)

    except Exception as e:
        logger.error(f"❌ خطأ في عرض صفحة التحليل المتقدم: {e}")
        flash('حدث خطأ في تحميل صفحة التحليل المتقدم', 'error')
        return redirect(url_for('purchase_orders.index'))

@bp.route('/items-analysis')
@login_required
def items_analysis():
    """صفحة تحليل أصناف أوامر الشراء"""
    try:
        logger.info("🔍 عرض صفحة تحليل أصناف أوامر الشراء")

        # إحصائيات أساسية سريعة
        oracle_manager = get_oracle_manager()

        # إحصائيات شاملة لجميع أوامر الشراء
        total_items_query = """
            SELECT
                COUNT(DISTINCT po.ID) as total_orders,
                COUNT(poi.ID) as total_items,
                COALESCE(SUM(po.TOTAL_AMOUNT), 0) as total_orders_value,
                COALESCE(AVG(po.TOTAL_AMOUNT), 0) as avg_order_value
            FROM PURCHASE_ORDERS po
            LEFT JOIN PO_ITEMS poi ON po.ID = poi.PO_ID
            WHERE NVL(po.STATUS, 'active') != 'cancelled'
        """

        # إحصائيات حسب العملة
        currency_stats_query = """
            SELECT
                NVL(po.CURRENCY, 'CNY') as currency,
                COUNT(DISTINCT po.ID) as orders_count,
                COALESCE(SUM(po.TOTAL_AMOUNT), 0) as total_value
            FROM PURCHASE_ORDERS po
            WHERE NVL(po.STATUS, 'active') != 'cancelled'
            GROUP BY po.CURRENCY
            ORDER BY total_value DESC
        """

        stats = oracle_manager.execute_query(total_items_query)
        currency_stats = oracle_manager.execute_query(currency_stats_query)

        quick_stats = {
            'total_items': 0,
            'total_orders': 0,
            'total_value': 0,
            'avg_price': 0
        }

        if stats and len(stats) > 0:
            quick_stats = {
                'total_orders': stats[0][0] or 0,
                'total_items': stats[0][1] or 0,
                'total_value': float(stats[0][2] or 0),
                'avg_order_value': float(stats[0][3] or 0)
            }

            # إضافة إحصائيات العملات
            currency_breakdown = []
            if currency_stats:
                for row in currency_stats:
                    currency_breakdown.append({
                        'currency': row[0],
                        'orders_count': row[1],
                        'total_value': float(row[2])
                    })

            quick_stats['currency_breakdown'] = currency_breakdown

        logger.info(f"✅ تم جلب الإحصائيات الأساسية: {quick_stats}")

        return render_template('purchase_orders/items_analysis.html',
                             quick_stats=quick_stats,
                             title="تحليل أصناف المشتريات")

    except Exception as e:
        logger.error(f"❌ خطأ في عرض صفحة تحليل الأصناف: {e}")
        flash('حدث خطأ في تحميل صفحة التحليل', 'error')
        return redirect(url_for('purchase_orders.index'))


@bp.route('/api/items/data')
@login_required
def api_items_data():
    """API لجلب بيانات الأصناف مع الفلاتر"""
    try:
        oracle_manager = get_oracle_manager()

        # الحصول على المعاملات من الطلب
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        supplier_id = request.args.get('supplier_id')
        status = request.args.get('status')
        category = request.args.get('category')
        search = request.args.get('search')

        # بناء الاستعلام الأساسي لعرض جميع الأصناف الفعلية
        base_query = """
            SELECT
                poi.ITEM_CODE,
                poi.ITEM_NAME,
                'صنف من أمر ' || po.PO_NUMBER as category,
                po.SUPPLIER_NAME,
                poi.QUANTITY as total_quantity,
                poi.UNIT_PRICE as avg_price,
                poi.TOTAL_PRICE as total_value,
                NVL(poi.UNIT, 'قطعة') as unit,
                po.CREATED_AT as last_order_date,
                NVL(po.STATUS, 'active') as status,
                NVL(po.CURRENCY, 'CNY') as currency
            FROM PO_ITEMS poi
            JOIN PURCHASE_ORDERS po ON poi.PO_ID = po.ID
            WHERE 1=1
        """

        # إضافة الفلاتر
        if start_date:
            base_query += " AND po.CREATED_AT >= TO_DATE(:start_date, 'YYYY-MM-DD')"

        if end_date:
            base_query += " AND po.CREATED_AT <= TO_DATE(:end_date, 'YYYY-MM-DD')"

        if supplier_id:
            base_query += " AND po.SUPPLIER_NAME = :supplier_id"

        if status:
            base_query += " AND NVL(po.STATUS, 'active') = :status"

        if search:
            base_query += " AND (UPPER(poi.ITEM_NAME) LIKE UPPER(:search) OR UPPER(poi.ITEM_CODE) LIKE UPPER(:search) OR UPPER(po.PO_NUMBER) LIKE UPPER(:search))"

        # إضافة ORDER BY
        base_query += """
            ORDER BY poi.TOTAL_PRICE DESC
        """

        # تحضير المعاملات
        params = {}
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date
        if supplier_id:
            params['supplier_id'] = supplier_id
        if status:
            params['status'] = status
        if search:
            params['search'] = f"%{search}%"

        # تنفيذ الاستعلام
        results = oracle_manager.execute_query(base_query, params)

        # تحويل النتائج إلى قائمة
        items_data = []
        if results:
            for row in results:
                items_data.append({
                    'item_code': row[0] or '',
                    'item_name': row[1] or '',
                    'category': row[2] or '',
                    'supplier_name': row[3] or 'غير محدد',
                    'total_quantity': float(row[4] or 0),
                    'avg_price': float(row[5] or 0),
                    'total_value': float(row[6] or 0),
                    'unit': row[7] or 'قطعة',
                    'last_order_date': row[8].strftime('%Y-%m-%d') if row[8] else '',
                    'status': row[9] or '',
                    'currency': row[10] or 'CNY'
                })

        logger.info(f"✅ تم جلب {len(items_data)} صنف")

        return jsonify({
            'success': True,
            'data': items_data,
            'total': len(items_data)
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API بيانات الأصناف: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/items/suppliers')
@login_required
def api_items_suppliers():
    """API لجلب قائمة الموردين"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب الموردين من أوامر الشراء
        suppliers_query = """
            SELECT DISTINCT po.SUPPLIER_NAME
            FROM PURCHASE_ORDERS po
            WHERE po.SUPPLIER_NAME IS NOT NULL
            ORDER BY po.SUPPLIER_NAME
        """

        results = oracle_manager.execute_query(suppliers_query)

        suppliers = []
        if results:
            for row in results:
                suppliers.append({
                    'value': row[0],
                    'label': row[0]
                })

        logger.info(f"✅ تم جلب {len(suppliers)} مورد")

        return jsonify({
            'success': True,
            'data': suppliers
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API الموردين: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route('/api/items/statuses')
@login_required
def api_items_statuses():
    """API لجلب قائمة الحالات"""
    try:
        oracle_manager = get_oracle_manager()

        # جلب الحالات من أوامر الشراء
        statuses_query = """
            SELECT DISTINCT NVL(po.STATUS, 'active') as status, COUNT(poi.ID) as items_count
            FROM PURCHASE_ORDERS po
            JOIN PO_ITEMS poi ON po.ID = poi.PO_ID
            GROUP BY po.STATUS
            ORDER BY status
        """

        results = oracle_manager.execute_query(statuses_query)

        statuses = []
        if results:
            for row in results:
                statuses.append({
                    'value': row[0],
                    'label': row[0],
                    'count': row[1]
                })

        logger.info(f"✅ تم جلب {len(statuses)} حالة")

        return jsonify({
            'success': True,
            'data': statuses
        })

    except Exception as e:
        logger.error(f"❌ خطأ في API الحالات: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
