# 🤖 نظام التحليل الذكي لجدول ITEM_MOVEMENT

## 📋 نظرة عامة

هذا النظام يستخدم أحدث تقنيات الذكاء الاصطناعي والتعلم الآلي لتحليل بيانات جدول `ITEM_MOVEMENT` في قاعدة البيانات Oracle وتقديم رؤى ذكية ومفيدة للمستخدمين.

## ✨ المميزات الرئيسية

### 🔍 التحليل الذكي
- **التحليل الإحصائي الشامل** للبيانات
- **كشف الأنماط والاتجاهات** باستخدام خوارزميات التعلم الآلي
- **اكتشاف الشذوذ والقيم غير الطبيعية** باستخدام Isolation Forest
- **تحليل الارتباطات** بين المتغيرات المختلفة
- **التنبؤ بالاتجاهات المستقبلية** باستخدام Random Forest

### 🎨 التصور التفاعلي
- **رسوم بيانية تفاعلية** باستخدام Plotly
- **خرائط حرارية للارتباطات**
- **رسوم التوزيع والصناديق**
- **السلاسل الزمنية** للبيانات الزمنية
- **تحليل المكونات الأساسية (PCA)**

### 🤖 تقنيات الذكاء الاصطناعي المستخدمة
- **K-Means Clustering** لتجميع البيانات
- **DBSCAN** لاكتشاف المجموعات الكثيفة
- **Isolation Forest** لكشف الشذوذ
- **Random Forest** للتنبؤ
- **Principal Component Analysis (PCA)** لتقليل الأبعاد

## 🛠️ التقنيات المستخدمة

### مكتبات تحليل البيانات
- **pandas** - معالجة وتحليل البيانات
- **numpy** - العمليات الرياضية والمصفوفات
- **scipy** - الحوسبة العلمية
- **statsmodels** - النمذجة الإحصائية

### مكتبات التعلم الآلي
- **scikit-learn** - خوارزميات التعلم الآلي
- **tensorflow** - التعلم العميق
- **torch** - PyTorch للشبكات العصبية

### مكتبات التصور
- **matplotlib** - الرسوم البيانية الأساسية
- **seaborn** - الرسوم الإحصائية
- **plotly** - الرسوم التفاعلية
- **streamlit** - تطبيقات الويب التفاعلية

### قاعدة البيانات
- **cx_Oracle** - الاتصال بقاعدة البيانات Oracle
- **SQLAlchemy** - ORM لقواعد البيانات

## 📁 هيكل المشروع

```
ai_analysis/
├── database_connector.py      # وحدة الاتصال بقاعدة البيانات
├── intelligent_analyzer.py    # محرك التحليل الذكي
├── visualization_engine.py    # محرك التصور
├── streamlit_app.py           # التطبيق التفاعلي
├── run_analysis.py            # سكريبت التشغيل الرئيسي
├── requirements.txt           # المكتبات المطلوبة
└── README.md                  # هذا الملف
```

## 🚀 طريقة التشغيل الآمنة

### ⚠️ **تحذير مهم:**
هذا النظام منفصل تماماً عن التطبيق الرئيسي ويعمل على منفذ مختلف لتجنب التضارب.

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التحليل الأساسي
```bash
python run_analysis.py
```

### 3. تشغيل لوحة التحكم الذكية (منفصلة)
```bash
python run_ai_dashboard.py
```
**الرابط:** http://localhost:8080 (أو منفذ آخر متاح)

### 4. البديل: تشغيل مباشر
```bash
python flask_app.py
```

## ⚙️ إعدادات قاعدة البيانات

```python
# معلومات الاتصال المستخدمة
SID = "ORCL"
USERNAME = "IAS20251"
PASSWORD = "ys123"
HOST = "localhost"
PORT = 1521
```

## 📊 أنواع التحليل المتاحة

### 1. التحليل الأساسي
- إحصائيات وصفية شاملة
- تحليل جودة البيانات
- كشف القيم المفقودة
- تحليل أنواع البيانات

### 2. التحليل المتقدم
- تحليل التجميع (Clustering)
- كشف الشذوذ (Anomaly Detection)
- تحليل الارتباطات
- تحليل الاتجاهات الزمنية
- التنبؤ بالقيم المستقبلية

### 3. التحليل الشامل
- جميع أنواع التحليل السابقة
- تقارير مفصلة
- توصيات ذكية

## 📈 مخرجات النظام

### ملفات النتائج
- `analysis_results_YYYYMMDD_HHMMSS.json` - نتائج التحليل الكاملة
- `data_sample_YYYYMMDD_HHMMSS.csv` - عينة من البيانات المعالجة
- `analysis_summary_YYYYMMDD_HHMMSS.txt` - ملخص التحليل
- `analysis_log_YYYYMMDD_HHMMSS.log` - سجل العمليات

### التطبيق التفاعلي
- لوحة تحكم شاملة
- رسوم بيانية تفاعلية
- إمكانية تخصيص التحليل
- تصدير النتائج

## 🔧 التخصيص والتطوير

### إضافة تحليلات جديدة
يمكن إضافة تحليلات جديدة عبر تعديل ملف `intelligent_analyzer.py`:

```python
def custom_analysis(self):
    """تحليل مخصص جديد"""
    # كود التحليل المخصص
    pass
```

### إضافة رسوم بيانية جديدة
يمكن إضافة رسوم جديدة عبر تعديل ملف `visualization_engine.py`:

```python
def create_custom_chart(self):
    """رسم بياني مخصص"""
    # كود الرسم المخصص
    pass
```

## 🐛 استكشاف الأخطاء

### مشاكل الاتصال بقاعدة البيانات
- تأكد من صحة معلومات الاتصال
- تأكد من تشغيل خدمة Oracle
- تحقق من إعدادات الشبكة والجدار الناري

### مشاكل المكتبات
- تأكد من تثبيت جميع المكتبات المطلوبة
- استخدم بيئة افتراضية منفصلة
- تحديث pip إلى أحدث إصدار

### مشاكل الأداء
- قلل من حجم البيانات المحللة
- استخدم عينة من البيانات للاختبار
- تأكد من توفر ذاكرة كافية

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع ملفات السجل للتفاصيل
- تحقق من رسائل الخطأ في وحدة التحكم
- استخدم وضع التصحيح للمزيد من المعلومات

## 📄 الترخيص

هذا المشروع مطور لأغراض تعليمية وتحليلية. يرجى مراجعة شروط الاستخدام قبل التطبيق في بيئة الإنتاج.

---

**تم تطوير هذا النظام باستخدام أحدث تقنيات الذكاء الاصطناعي لتقديم تحليل شامل وذكي لبيانات حركة الأصناف.**
