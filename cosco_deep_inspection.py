#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص عميق ودقيق لموقع COSCO لفهم آلية البحث الحقيقية
"""

import requests
import urllib3
import re
import json
import time
from urllib.parse import urljoin

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def deep_inspect_cosco():
    """فحص عميق لموقع COSCO"""
    
    print("🔬 فحص عميق ودقيق لموقع COSCO")
    print("=" * 60)
    
    session = requests.Session()
    session.verify = False
    
    # Headers مطابقة للمتصفح تماماً
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1'
    })
    
    try:
        # الخطوة 1: زيارة الصفحة الرئيسية
        print("📡 الخطوة 1: زيارة الصفحة الرئيسية...")
        url = "https://elines.coscoshipping.com/ebusiness/cargoTracking"
        
        response = session.get(url, timeout=30)
        print(f"✅ Status: {response.status_code}")
        print(f"🍪 Cookies: {len(session.cookies)}")
        print(f"📏 Content Length: {len(response.text)}")
        
        if response.status_code == 200:
            # حفظ الصفحة الرئيسية
            with open('cosco_main_detailed.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("💾 تم حفظ الصفحة الرئيسية في cosco_main_detailed.html")
            
            # الخطوة 2: تحليل مفصل للصفحة
            print("\n📡 الخطوة 2: تحليل مفصل للصفحة...")
            analyze_page_detailed(response.text, session)
            
            # الخطوة 3: تحليل JavaScript files
            print("\n📡 الخطوة 3: تحليل JavaScript files...")
            analyze_javascript_files(response.text, session)
            
            # الخطوة 4: محاولة البحث الفعلي
            print("\n📡 الخطوة 4: محاولة البحث الفعلي...")
            attempt_real_search(session, "6425375050")
            
        else:
            print(f"❌ فشل في الوصول: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

def analyze_page_detailed(html, session):
    """تحليل مفصل للصفحة"""
    
    print("🔍 تحليل مفصل للصفحة...")
    
    # 1. تحليل بنية HTML
    print("\n1️⃣ تحليل بنية HTML:")
    
    # البحث عن div#app
    if '<div id="app">' in html or '<div id="app"' in html:
        print("  ✅ وجد <div id='app'> - Single Page Application")
    
    # البحث عن Vue.js أو React
    frameworks = ['vue', 'react', 'angular']
    for framework in frameworks:
        if framework in html.lower():
            print(f"  ✅ وجد إشارة لـ {framework.upper()}")
    
    # 2. تحليل JavaScript files
    print("\n2️⃣ JavaScript files:")
    js_files = re.findall(r'src="([^"]*\.js[^"]*)"', html)
    print(f"  📜 عدد ملفات JS: {len(js_files)}")
    
    for js in js_files:
        print(f"    - {js}")
        
        # تحميل وتحليل main JS file
        if 'main' in js or 'app' in js:
            print(f"    🔍 تحليل الملف الرئيسي: {js}")
            analyze_main_js(js, session)
    
    # 3. تحليل CSS files
    print("\n3️⃣ CSS files:")
    css_files = re.findall(r'href="([^"]*\.css[^"]*)"', html)
    print(f"  🎨 عدد ملفات CSS: {len(css_files)}")
    
    # 4. البحث عن أي forms أو inputs
    print("\n4️⃣ Forms و Inputs:")
    forms = re.findall(r'<form[^>]*>', html)
    inputs = re.findall(r'<input[^>]*>', html)
    selects = re.findall(r'<select[^>]*>', html)
    buttons = re.findall(r'<button[^>]*>', html)
    
    print(f"  📝 Forms: {len(forms)}")
    print(f"  📋 Inputs: {len(inputs)}")
    print(f"  📋 Selects: {len(selects)}")
    print(f"  🔘 Buttons: {len(buttons)}")
    
    # 5. البحث عن API endpoints في HTML
    print("\n5️⃣ API endpoints في HTML:")
    api_patterns = [
        r'["\']([^"\']*api[^"\']*)["\']',
        r'["\']([^"\']*cargoTracking[^"\']*)["\']',
        r'["\']([^"\']*tracking[^"\']*)["\']'
    ]
    
    found_apis = set()
    for pattern in api_patterns:
        matches = re.findall(pattern, html, re.IGNORECASE)
        for match in matches:
            if len(match) > 5 and ('api' in match.lower() or 'tracking' in match.lower()):
                found_apis.add(match)
    
    print(f"  🔗 API endpoints found: {len(found_apis)}")
    for api in sorted(found_apis):
        print(f"    - {api}")

def analyze_main_js(js_path, session):
    """تحليل الملف الرئيسي للـ JavaScript"""
    
    try:
        if js_path.startswith('/'):
            js_url = f"https://elines.coscoshipping.com{js_path}"
        else:
            js_url = js_path
        
        print(f"    📡 تحميل: {js_url}")
        js_response = session.get(js_url, timeout=20)
        
        if js_response.status_code == 200:
            js_content = js_response.text
            print(f"    ✅ تم تحميل JS ({len(js_content)} حرف)")
            
            # حفظ الملف للفحص
            with open('cosco_main_js.js', 'w', encoding='utf-8') as f:
                f.write(js_content)
            print(f"    💾 تم حفظ JS في cosco_main_js.js")
            
            # تحليل المحتوى
            analyze_js_content_detailed(js_content)
            
        else:
            print(f"    ❌ فشل في تحميل JS: {js_response.status_code}")
            
    except Exception as e:
        print(f"    ❌ خطأ في تحليل JS: {e}")

def analyze_js_content_detailed(js_content):
    """تحليل مفصل لمحتوى JavaScript"""
    
    print(f"    🔍 تحليل محتوى JavaScript...")
    
    # 1. البحث عن API endpoints
    api_patterns = [
        r'["\']([^"\']*api[^"\']*)["\']',
        r'["\']([^"\']*cargoTracking[^"\']*)["\']',
        r'url\s*:\s*["\']([^"\']+)["\']',
        r'baseURL\s*:\s*["\']([^"\']+)["\']',
        r'endpoint\s*:\s*["\']([^"\']+)["\']'
    ]
    
    found_endpoints = set()
    for pattern in api_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        for match in matches:
            if 'api' in match.lower() or 'tracking' in match.lower() or 'cargo' in match.lower():
                found_endpoints.add(match)
    
    print(f"    🔗 API endpoints: {len(found_endpoints)}")
    for endpoint in sorted(found_endpoints)[:10]:  # أول 10
        print(f"      - {endpoint}")
    
    # 2. البحث عن HTTP methods
    http_methods = re.findall(r'\.(get|post|put|delete|patch)\s*\(', js_content, re.IGNORECASE)
    if http_methods:
        unique_methods = set(method.upper() for method in http_methods)
        print(f"    📤 HTTP methods: {unique_methods}")
    
    # 3. البحث عن data structures
    data_patterns = [
        r'bookingNo\s*[:=]\s*([^,}]+)',
        r'trackingType\s*[:=]\s*([^,}]+)',
        r'containerNo\s*[:=]\s*([^,}]+)',
        r'trackingNo\s*[:=]\s*([^,}]+)'
    ]
    
    print(f"    📊 Data patterns:")
    for pattern in data_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        if matches:
            print(f"      - {pattern}: {matches[:3]}")
    
    # 4. البحث عن functions مهمة
    function_patterns = [
        r'function\s+(\w*search\w*)\s*\(',
        r'function\s+(\w*track\w*)\s*\(',
        r'function\s+(\w*query\w*)\s*\(',
        r'(\w*search\w*)\s*:\s*function',
        r'(\w*track\w*)\s*:\s*function'
    ]
    
    found_functions = set()
    for pattern in function_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE)
        for match in matches:
            if isinstance(match, tuple):
                found_functions.add(match[0])
            else:
                found_functions.add(match)
    
    if found_functions:
        print(f"    🔧 Functions مهمة: {sorted(found_functions)}")

def analyze_javascript_files(html, session):
    """تحليل جميع ملفات JavaScript"""
    
    js_files = re.findall(r'src="([^"]*\.js[^"]*)"', html)
    
    for js_file in js_files:
        if any(keyword in js_file.lower() for keyword in ['main', 'app', 'chunk', 'vendor']):
            print(f"🔍 تحليل: {js_file}")
            analyze_main_js(js_file, session)

def attempt_real_search(session, booking_number):
    """محاولة البحث الفعلي"""
    
    print(f"🔍 محاولة البحث الفعلي لرقم: {booking_number}")
    
    # تحديث headers للبحث
    session.headers.update({
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': 'https://elines.coscoshipping.com',
        'Referer': 'https://elines.coscoshipping.com/ebusiness/cargoTracking'
    })
    
    # قائمة endpoints للاختبار
    test_endpoints = [
        "https://elines.coscoshipping.com/ebusiness/cargoTracking",
        "https://elines.coscoshipping.com/ebusiness/cargoTracking/search",
        "https://elines.coscoshipping.com/ebusiness/api/cargoTracking",
        "https://elines.coscoshipping.com/api/cargoTracking"
    ]
    
    # بيانات البحث
    search_data = {
        "bookingNo": booking_number,
        "trackingType": "2"
    }
    
    for endpoint in test_endpoints:
        try:
            print(f"  📤 اختبار: {endpoint}")
            
            # محاولة POST
            response = session.post(endpoint, json=search_data, timeout=15)
            print(f"    POST Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"    ✅ نجح POST!")
                
                # حفظ النتيجة
                filename = f"cosco_search_result_post.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"    💾 تم حفظ النتيجة في {filename}")
                
                # تحليل النتيجة
                if analyze_search_response(response.text, booking_number):
                    print(f"    🎯 وجد بيانات في النتيجة!")
                    return True
            
            # محاولة GET
            response = session.get(endpoint, params=search_data, timeout=15)
            print(f"    GET Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"    ✅ نجح GET!")
                
                # حفظ النتيجة
                filename = f"cosco_search_result_get.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"    💾 تم حفظ النتيجة في {filename}")
                
                # تحليل النتيجة
                if analyze_search_response(response.text, booking_number):
                    print(f"    🎯 وجد بيانات في النتيجة!")
                    return True
            
        except Exception as e:
            print(f"    ❌ خطأ: {e}")
    
    return False

def analyze_search_response(html, booking_number):
    """تحليل استجابة البحث"""
    
    # البحث عن رقم الحجز في النتيجة
    if booking_number in html:
        print(f"    ✅ وجد رقم الحجز في النتيجة")
        
        # البحث عن كلمات مفتاحية
        keywords = ['B/L', 'POL', 'POD', 'ETD', 'ETA', 'vessel', 'container', 'Shantou', 'Aden']
        found_keywords = [kw for kw in keywords if kw.lower() in html.lower()]
        
        if found_keywords:
            print(f"    🔍 وجد كلمات مفتاحية: {found_keywords}")
            return True
    
    # البحث عن JSON data
    try:
        # محاولة تحليل كـ JSON
        data = json.loads(html)
        print(f"    ✅ النتيجة JSON صالح")
        print(f"    📊 JSON keys: {list(data.keys()) if isinstance(data, dict) else 'list'}")
        return True
    except:
        pass
    
    # البحث عن تواريخ
    dates = re.findall(r'\d{4}-\d{2}-\d{2}', html)
    if dates:
        print(f"    📅 وجد تواريخ: {dates}")
        return True
    
    return False

def main():
    """الاختبار الرئيسي"""
    
    print("=" * 80)
    print("🔬 فحص عميق ودقيق لموقع COSCO")
    print("=" * 80)
    
    deep_inspect_cosco()
    
    print(f"\n" + "=" * 80)
    print("✅ انتهى الفحص العميق")
    print("=" * 80)

if __name__ == "__main__":
    main()
