#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات طلبات الشراء المحسنة - Enhanced Purchase Request Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, session
from flask_login import login_required, current_user
from sqlalchemy import and_, or_, desc, asc
from datetime import datetime, date
import json

from app import db
from app.models import PurchaseRequest, PurchaseRequestItem, Contract, User
from app.purchase_requests.forms import (
    PurchaseRequestForm, PurchaseRequestSearchForm, ContractSelectionForm,
    QuickNavigationForm, ExportForm
)

# إنشاء Blueprint
purchase_requests_bp = Blueprint('purchase_requests', __name__, url_prefix='/purchase_requests')

@purchase_requests_bp.route('/')
@purchase_requests_bp.route('/index')
@login_required
def index():
    """الصفحة الرئيسية لطلبات الشراء - نافذة بسيطة مثل عقود الشراء"""

    # الحصول على رقم الصفحة
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # بناء الاستعلام الأساسي
    query = PurchaseRequest.query

    # تطبيق الفلاتر
    if request.args.get('req_no'):
        req_no = f"%{request.args.get('req_no')}%"
        query = query.filter(PurchaseRequest.req_no.like(req_no))

    if request.args.get('requester_name'):
        requester_name = f"%{request.args.get('requester_name')}%"
        query = query.filter(PurchaseRequest.requester_name.like(requester_name))

    if request.args.get('status'):
        query = query.filter(PurchaseRequest.req_status == request.args.get('status'))

    if request.args.get('priority'):
        query = query.filter(PurchaseRequest.priority == request.args.get('priority'))

    # ترتيب النتائج
    query = query.order_by(desc(PurchaseRequest.created_at))

    # تطبيق التصفح
    requests = query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    # إحصائيات محسنة
    stats = {
        'total_requests': PurchaseRequest.query.count(),
        'approved_requests': PurchaseRequest.query.filter_by(req_status='معتمد').count(),
        'pending_requests': PurchaseRequest.query.filter_by(req_status='قيد المراجعة').count(),
        'total_value': db.session.query(db.func.sum(PurchaseRequest.total_amount)).scalar() or 0
    }

    return render_template('purchase_requests/simple_index.html',
                         requests=requests,
                         stats=stats,
                         pagination=requests,
                         title='طلبات الشراء')

@purchase_requests_bp.route('/enhanced')
@login_required
def enhanced():
    """النافذة المحسنة الرئيسية - Enhanced Main Window"""
    
    # الحصول على أول طلب أو إنشاء طلب جديد
    current_request = PurchaseRequest.query.first()
    
    # إحصائيات للتنقل
    total_records = PurchaseRequest.query.count()
    current_record = 1 if current_request else 0
    
    # نماذج الأشكال
    form = PurchaseRequestForm()
    search_form = PurchaseRequestSearchForm()
    navigation_form = QuickNavigationForm()
    export_form = ExportForm()
    
    # ملء النموذج بالبيانات الحالية
    if current_request:
        form = PurchaseRequestForm(obj=current_request)
        # ملء عناصر الطلب
        form.items.entries.clear()
        for item in current_request.items:
            item_form = form.items.append_entry()
            # ملء البيانات يدوياً
            item_form.item_name.data = item.item_name
            item_form.item_description.data = item.item_description
            item_form.quantity.data = item.quantity
            item_form.unit_price.data = item.unit_price
            item_form.total_price.data = item.total_price
            item_form.unit_name.data = item.unit_name
            item_form.notes.data = item.notes
    
    return render_template('purchase_requests/enhanced_window.html',
                         form=form,
                         search_form=search_form,
                         navigation_form=navigation_form,
                         export_form=export_form,
                         current_request=current_request,
                         current_record=current_record,
                         total_records=total_records,
                         title='نافذة طلبات الشراء المحسنة')

@purchase_requests_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new():
    """إنشاء طلب شراء جديد"""
    if request.method == 'POST':
        try:
            # إنشاء طلب جديد
            new_request = PurchaseRequest(
                req_no=request.form.get('req_no'),
                req_date=datetime.strptime(request.form.get('req_date'), '%Y-%m-%d').date() if request.form.get('req_date') else None,
                requester_name=request.form.get('requester_name'),
                department=request.form.get('department'),
                req_status=request.form.get('req_status', 'مسودة'),
                priority=request.form.get('priority', 'عادي'),
                purpose=request.form.get('purpose'),
                notes=request.form.get('notes'),
                total_amount=float(request.form.get('total_amount', 0)),
                currency=request.form.get('currency', 'ريال سعودي'),
                needed_date=datetime.strptime(request.form.get('needed_date'), '%Y-%m-%d').date() if request.form.get('needed_date') else None,
                created_by=current_user.id,
                created_at=datetime.utcnow()
            )

            db.session.add(new_request)
            db.session.flush()  # للحصول على ID

            # إضافة العناصر
            item_codes = request.form.getlist('item_code[]')
            item_names = request.form.getlist('item_name[]')
            quantities = request.form.getlist('quantity[]')
            unit_names = request.form.getlist('unit_name[]')
            unit_prices = request.form.getlist('unit_price[]')
            total_prices = request.form.getlist('total_price[]')

            for i in range(len(item_codes)):
                if item_codes[i] or item_names[i]:  # إضافة العنصر فقط إذا كان له كود أو اسم
                    item = PurchaseRequestItem(
                        purchase_request_id=new_request.id,
                        line_number=i + 1,
                        item_code=item_codes[i],
                        item_name=item_names[i],
                        quantity=float(quantities[i]) if quantities[i] else 0,
                        unit_name=unit_names[i],
                        unit_price=float(unit_prices[i]) if unit_prices[i] else 0,
                        total_price=float(total_prices[i]) if total_prices[i] else 0
                    )
                    db.session.add(item)

            db.session.commit()
            flash('تم إنشاء طلب الشراء بنجاح', 'success')
            return redirect(url_for('purchase_requests.view', id=new_request.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء طلب الشراء: {str(e)}', 'error')

    return render_template('purchase_requests/form.html',
                         title='إنشاء طلب شراء جديد',
                         action='new')

@purchase_requests_bp.route('/view/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل طلب الشراء"""
    request_obj = PurchaseRequest.query.get_or_404(id)
    return render_template('purchase_requests/view.html',
                         request=request_obj,
                         title=f'طلب الشراء - {request_obj.req_no}')

@purchase_requests_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل طلب الشراء"""
    request_obj = PurchaseRequest.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # تحديث البيانات الأساسية
            request_obj.req_no = request.form.get('req_no')
            request_obj.req_date = datetime.strptime(request.form.get('req_date'), '%Y-%m-%d').date() if request.form.get('req_date') else None
            request_obj.requester_name = request.form.get('requester_name')
            request_obj.department = request.form.get('department')
            request_obj.req_status = request.form.get('req_status')
            request_obj.priority = request.form.get('priority')
            request_obj.purpose = request.form.get('purpose')
            request_obj.notes = request.form.get('notes')
            request_obj.total_amount = float(request.form.get('total_amount', 0))
            request_obj.currency = request.form.get('currency')
            request_obj.needed_date = datetime.strptime(request.form.get('needed_date'), '%Y-%m-%d').date() if request.form.get('needed_date') else None
            request_obj.updated_at = datetime.utcnow()

            # حذف العناصر القديمة
            PurchaseRequestItem.query.filter_by(purchase_request_id=id).delete()

            # إضافة العناصر الجديدة
            item_codes = request.form.getlist('item_code[]')
            item_names = request.form.getlist('item_name[]')
            quantities = request.form.getlist('quantity[]')
            unit_names = request.form.getlist('unit_name[]')
            unit_prices = request.form.getlist('unit_price[]')
            total_prices = request.form.getlist('total_price[]')

            for i in range(len(item_codes)):
                if item_codes[i] or item_names[i]:
                    item = PurchaseRequestItem(
                        purchase_request_id=request_obj.id,
                        line_number=i + 1,
                        item_code=item_codes[i],
                        item_name=item_names[i],
                        quantity=float(quantities[i]) if quantities[i] else 0,
                        unit_name=unit_names[i],
                        unit_price=float(unit_prices[i]) if unit_prices[i] else 0,
                        total_price=float(total_prices[i]) if total_prices[i] else 0
                    )
                    db.session.add(item)

            db.session.commit()
            flash('تم تحديث طلب الشراء بنجاح', 'success')
            return redirect(url_for('purchase_requests.view', id=request_obj.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث طلب الشراء: {str(e)}', 'error')

    return render_template('purchase_requests/form.html',
                         request=request_obj,
                         title=f'تعديل طلب الشراء - {request_obj.req_no}',
                         action='edit')

@purchase_requests_bp.route('/delete/<int:id>')
@login_required
def delete_request(id):
    """حذف طلب الشراء"""
    request_obj = PurchaseRequest.query.get_or_404(id)

    try:
        # حذف العناصر المرتبطة أولاً
        PurchaseRequestItem.query.filter_by(purchase_request_id=id).delete()

        # حذف الطلب
        db.session.delete(request_obj)
        db.session.commit()

        flash('تم حذف طلب الشراء بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف طلب الشراء', 'error')

    return redirect(url_for('purchase_requests.index'))

@purchase_requests_bp.route('/search')
@login_required
def search_page():
    """صفحة البحث المتقدم"""
    return render_template('purchase_requests/search.html',
                         title='البحث المتقدم في طلبات الشراء')

@purchase_requests_bp.route('/reports')
@login_required
def reports_page():
    """صفحة التقارير"""
    return render_template('purchase_requests/reports.html',
                         title='تقارير طلبات الشراء')

@purchase_requests_bp.route('/api/navigate/<direction>')
@login_required
def navigate(direction):
    """التنقل بين السجلات - Navigation API"""
    
    current_id = request.args.get('current_id', type=int)
    
    if direction == 'first':
        record = PurchaseRequest.query.order_by(PurchaseRequest.id).first()
    elif direction == 'last':
        record = PurchaseRequest.query.order_by(desc(PurchaseRequest.id)).first()
    elif direction == 'next':
        if current_id:
            record = PurchaseRequest.query.filter(PurchaseRequest.id > current_id).order_by(PurchaseRequest.id).first()
        else:
            record = PurchaseRequest.query.order_by(PurchaseRequest.id).first()
    elif direction == 'previous':
        if current_id:
            record = PurchaseRequest.query.filter(PurchaseRequest.id < current_id).order_by(desc(PurchaseRequest.id)).first()
        else:
            record = PurchaseRequest.query.order_by(desc(PurchaseRequest.id)).first()
    else:
        return jsonify({'error': 'اتجاه غير صحيح'}), 400
    
    if not record:
        return jsonify({'error': 'لا توجد سجلات'}), 404
    
    # حساب رقم السجل الحالي
    current_record = PurchaseRequest.query.filter(PurchaseRequest.id <= record.id).count()
    total_records = PurchaseRequest.query.count()
    
    # إعداد بيانات الاستجابة
    response_data = {
        'id': record.id,
        'req_no': record.req_no,
        'req_serial': record.req_serial,
        'requester_name': record.requester_name,
        'department_name': record.department_name,
        'req_type': record.req_type,
        'req_priority': record.req_priority,
        'req_date': record.req_date.isoformat() if record.req_date else None,
        'needed_date': record.needed_date.isoformat() if record.needed_date else None,
        'req_status': record.req_status,
        'currency': record.currency,
        'total_amount': float(record.total_amount),
        'approval_status': record.approval_status,
        'contract_no': record.contract_no,
        'contract_serial': record.contract_serial,
        'contract_amount': float(record.contract_amount) if record.contract_amount else None,
        'description': record.description,
        'notes': record.notes,
        'attachments': record.attachments,
        'current_record': current_record,
        'total_records': total_records,
        'items': []
    }
    
    # إضافة عناصر الطلب
    for item in record.items:
        response_data['items'].append({
            'id': item.id,
            'line_number': item.line_number,
            'item_code': item.item_code,
            'item_name': item.item_name,
            'item_description': item.item_description,
            'quantity': float(item.quantity),
            'unit_name': item.unit_name,
            'unit_price': float(item.unit_price),
            'total_price': float(item.total_price),
            'specifications': item.specifications,
            'notes': item.notes
        })
    
    return jsonify(response_data)

@purchase_requests_bp.route('/api/save', methods=['POST'])
@login_required
def save():
    """حفظ طلب الشراء - Save Purchase Request"""
    
    try:
        data = request.get_json()
        
        # إنشاء أو تحديث الطلب
        if data.get('id'):
            record = PurchaseRequest.query.get_or_404(data['id'])
        else:
            record = PurchaseRequest()
            record.created_by = current_user.id
        
        # تحديث البيانات الأساسية
        record.req_no = data.get('req_no')
        record.req_serial = data.get('req_serial')
        record.requester_name = data.get('requester_name')
        record.department_name = data.get('department_name')
        record.req_type = data.get('req_type')
        record.req_priority = data.get('req_priority')
        record.req_date = datetime.strptime(data.get('req_date'), '%Y-%m-%d').date() if data.get('req_date') else None
        record.needed_date = datetime.strptime(data.get('needed_date'), '%Y-%m-%d').date() if data.get('needed_date') else None
        record.req_status = data.get('req_status')
        record.currency = data.get('currency')
        record.total_amount = data.get('total_amount', 0)
        record.approval_status = data.get('approval_status')
        record.contract_no = data.get('contract_no')
        record.contract_serial = data.get('contract_serial')
        record.contract_amount = data.get('contract_amount')
        record.description = data.get('description')
        record.notes = data.get('notes')
        record.attachments = data.get('attachments')
        
        # حفظ الطلب أولاً
        if not data.get('id'):
            db.session.add(record)
        db.session.flush()  # للحصول على ID
        
        # حذف العناصر القديمة وإضافة الجديدة
        if data.get('id'):
            PurchaseRequestItem.query.filter_by(purchase_request_id=record.id).delete()
        
        # إضافة العناصر الجديدة
        total_amount = 0
        for item_data in data.get('items', []):
            if item_data.get('item_name'):  # فقط العناصر التي لها اسم
                item = PurchaseRequestItem()
                item.purchase_request_id = record.id
                item.line_number = item_data.get('line_number', 1)
                item.item_code = item_data.get('item_code')
                item.item_name = item_data.get('item_name')
                item.item_description = item_data.get('item_description')
                item.quantity = item_data.get('quantity', 0)
                item.unit_name = item_data.get('unit_name', 'قطعة')
                item.unit_price = item_data.get('unit_price', 0)
                item.total_price = item_data.get('total_price', 0)
                item.specifications = item_data.get('specifications')
                item.notes = item_data.get('notes')
                
                # حساب الإجمالي
                item.calculate_total()
                total_amount += item.total_price
                
                db.session.add(item)
        
        # تحديث الإجمالي العام
        record.total_amount = total_amount
        
        # حفظ التغييرات
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حفظ الطلب بنجاح',
            'id': record.id,
            'total_amount': float(record.total_amount)
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في حفظ الطلب: {str(e)}'
        }), 500

@purchase_requests_bp.route('/api/search', methods=['POST'])
@login_required
def api_search():
    """البحث المتقدم - Advanced Search (15 معيار)"""

    try:
        data = request.get_json()

        # بناء الاستعلام الأساسي
        query = PurchaseRequest.query

        # القسم الأساسي (5 معايير)
        if data.get('req_no'):
            query = query.filter(PurchaseRequest.req_no.like(f"%{data['req_no']}%"))
        if data.get('req_serial'):
            query = query.filter(PurchaseRequest.req_serial == data['req_serial'])
        if data.get('requester_name'):
            query = query.filter(PurchaseRequest.requester_name.like(f"%{data['requester_name']}%"))
        if data.get('department_name'):
            query = query.filter(PurchaseRequest.department_name.like(f"%{data['department_name']}%"))
        if data.get('req_type'):
            query = query.filter(PurchaseRequest.req_type == data['req_type'])

        # القسم المالي (5 معايير)
        if data.get('currency'):
            query = query.filter(PurchaseRequest.currency == data['currency'])
        if data.get('amount_from'):
            query = query.filter(PurchaseRequest.total_amount >= data['amount_from'])
        if data.get('amount_to'):
            query = query.filter(PurchaseRequest.total_amount <= data['amount_to'])
        if data.get('approval_status'):
            query = query.filter(PurchaseRequest.approval_status == data['approval_status'])
        if data.get('contract_no'):
            query = query.filter(PurchaseRequest.contract_no.like(f"%{data['contract_no']}%"))

        # القسم الزمني (5 معايير)
        if data.get('req_date_from'):
            req_date_from = datetime.strptime(data['req_date_from'], '%Y-%m-%d').date()
            query = query.filter(PurchaseRequest.req_date >= req_date_from)
        if data.get('req_date_to'):
            req_date_to = datetime.strptime(data['req_date_to'], '%Y-%m-%d').date()
            query = query.filter(PurchaseRequest.req_date <= req_date_to)
        if data.get('needed_date_from'):
            needed_date_from = datetime.strptime(data['needed_date_from'], '%Y-%m-%d').date()
            query = query.filter(PurchaseRequest.needed_date >= needed_date_from)
        if data.get('needed_date_to'):
            needed_date_to = datetime.strptime(data['needed_date_to'], '%Y-%m-%d').date()
            query = query.filter(PurchaseRequest.needed_date <= needed_date_to)
        if data.get('req_status'):
            query = query.filter(PurchaseRequest.req_status == data['req_status'])

        # تنفيذ البحث مع الترتيب
        results = query.order_by(desc(PurchaseRequest.created_at)).limit(100).all()

        # تحويل النتائج إلى JSON
        search_results = []
        for record in results:
            search_results.append({
                'id': record.id,
                'req_no': record.req_no,
                'req_serial': record.req_serial,
                'requester_name': record.requester_name,
                'department_name': record.department_name,
                'req_type': record.req_type,
                'req_priority': record.req_priority,
                'req_date': record.req_date.isoformat() if record.req_date else None,
                'needed_date': record.needed_date.isoformat() if record.needed_date else None,
                'req_status': record.req_status,
                'currency': record.currency,
                'total_amount': float(record.total_amount),
                'approval_status': record.approval_status,
                'contract_no': record.contract_no,
                'formatted_total': record.formatted_total,
                'status_color': record.status_color,
                'priority_color': record.priority_color
            })

        return jsonify({
            'success': True,
            'results': search_results,
            'count': len(search_results),
            'message': f'تم العثور على {len(search_results)} نتيجة'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في البحث: {str(e)}'
        }), 500

@purchase_requests_bp.route('/api/contracts/search', methods=['POST'])
@login_required
def search_contracts():
    """البحث في العقود - Contract Search (8 معايير)"""

    try:
        data = request.get_json()

        # بناء الاستعلام الأساسي
        query = Contract.query

        # معايير البحث (8 معايير)
        if data.get('contract_no'):
            query = query.filter(Contract.contract_no.like(f"%{data['contract_no']}%"))
        if data.get('contract_title'):
            query = query.filter(Contract.contract_title.like(f"%{data['contract_title']}%"))
        if data.get('supplier_name'):
            query = query.filter(Contract.supplier_name.like(f"%{data['supplier_name']}%"))
        if data.get('contract_status'):
            query = query.filter(Contract.contract_status == data['contract_status'])
        if data.get('contract_type'):
            query = query.filter(Contract.contract_type == data['contract_type'])
        if data.get('amount_min'):
            query = query.filter(Contract.contract_amount >= data['amount_min'])
        if data.get('amount_max'):
            query = query.filter(Contract.contract_amount <= data['amount_max'])
        if data.get('currency'):
            query = query.filter(Contract.currency == data['currency'])

        # تنفيذ البحث
        results = query.filter(Contract.contract_status == 'نشط').order_by(Contract.contract_no).limit(50).all()

        # تحويل النتائج إلى JSON (12 عمود)
        contract_results = []
        for contract in results:
            # حساب المبلغ المتبقي ونسبة الاستخدام
            contract.calculate_remaining_amount()

            contract_results.append({
                'id': contract.id,
                'contract_no': contract.contract_no,  # 1
                'contract_title': contract.contract_title,  # 2
                'contract_type': contract.contract_type,  # 3
                'supplier_name': contract.supplier_name,  # 4
                'contract_amount': float(contract.contract_amount),  # 5
                'used_amount': float(contract.used_amount),  # 6
                'remaining_amount': float(contract.remaining_amount),  # 7
                'currency': contract.currency,  # 8
                'contract_status': contract.contract_status,  # 9
                'start_date': contract.start_date.isoformat() if contract.start_date else None,  # 10
                'end_date': contract.end_date.isoformat() if contract.end_date else None,  # 11
                'usage_percentage': float(contract.usage_percentage),  # 12
                'formatted_contract_amount': contract.formatted_contract_amount,
                'formatted_used_amount': contract.formatted_used_amount,
                'formatted_remaining_amount': contract.formatted_remaining_amount,
                'status_color': contract.status_color
            })

        return jsonify({
            'success': True,
            'results': contract_results,
            'count': len(contract_results),
            'message': f'تم العثور على {len(contract_results)} عقد'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في البحث عن العقود: {str(e)}'
        }), 500

@purchase_requests_bp.route('/api/link_contract', methods=['POST'])
@login_required
def link_contract():
    """ربط طلب بعقد - Link Request to Contract"""

    try:
        data = request.get_json()
        request_id = data.get('request_id')
        contract_id = data.get('contract_id')

        if not request_id or not contract_id:
            return jsonify({
                'success': False,
                'message': 'معرف الطلب والعقد مطلوبان'
            }), 400

        # الحصول على الطلب والعقد
        purchase_request = PurchaseRequest.query.get_or_404(request_id)
        contract = Contract.query.get_or_404(contract_id)

        # التحقق من صلاحية العقد
        if contract.contract_status != 'نشط':
            return jsonify({
                'success': False,
                'message': 'العقد غير نشط'
            }), 400

        if contract.remaining_amount < purchase_request.total_amount:
            return jsonify({
                'success': False,
                'message': 'المبلغ المتبقي في العقد غير كافي'
            }), 400

        # ربط الطلب بالعقد
        purchase_request.contract_no = contract.contract_no
        purchase_request.contract_serial = contract.id
        purchase_request.contract_amount = contract.contract_amount

        # تحديث المبلغ المستخدم في العقد
        contract.used_amount += purchase_request.total_amount
        contract.calculate_remaining_amount()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم ربط الطلب بالعقد بنجاح',
            'contract_info': {
                'contract_no': contract.contract_no,
                'contract_title': contract.contract_title,
                'remaining_amount': float(contract.remaining_amount)
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في ربط العقد: {str(e)}'
        }), 500

@purchase_requests_bp.route('/api/delete/<int:id>', methods=['DELETE'])
@login_required
def delete(id):
    """حذف طلب الشراء - Delete Purchase Request"""
    
    try:
        record = PurchaseRequest.query.get_or_404(id)
        
        # التحقق من الصلاحيات
        if record.created_by != current_user.id and not current_user.is_admin:
            return jsonify({
                'success': False,
                'message': 'ليس لديك صلاحية لحذف هذا الطلب'
            }), 403
        
        # حذف الطلب (العناصر ستحذف تلقائياً بسبب cascade)
        db.session.delete(record)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حذف الطلب بنجاح'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في حذف الطلب: {str(e)}'
        }), 500
