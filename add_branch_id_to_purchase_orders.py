#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة عمود BRANCH_ID إلى جدول PURCHASE_ORDERS
Add BRANCH_ID column to PURCHASE_ORDERS table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import OracleManager
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_branch_id_column():
    """إضافة عمود BRANCH_ID إلى جدول PURCHASE_ORDERS"""
    
    oracle = OracleManager()
    
    try:
        oracle.connect()
        print("🔗 تم الاتصال بقاعدة البيانات بنجاح")
        
        # 1. فحص وجود العمود
        print("\n1️⃣ فحص وجود عمود BRANCH_ID...")
        
        check_column_query = """
        SELECT COUNT(*) 
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = 'PURCHASE_ORDERS' 
        AND COLUMN_NAME = 'BRANCH_ID'
        """
        
        result = oracle.execute_query(check_column_query)
        column_exists = result[0][0] > 0 if result else False
        
        if column_exists:
            print("✅ عمود BRANCH_ID موجود بالفعل")
            return True
        
        # 2. إضافة العمود
        print("📝 إضافة عمود BRANCH_ID...")
        
        add_column_query = """
        ALTER TABLE PURCHASE_ORDERS 
        ADD BRANCH_ID NUMBER DEFAULT 21
        """
        
        oracle.execute_update(add_column_query)
        oracle.commit()
        print("✅ تم إضافة عمود BRANCH_ID بنجاح")
        
        # 3. إضافة تعليق على العمود
        print("📝 إضافة تعليق على العمود...")
        
        comment_query = """
        COMMENT ON COLUMN PURCHASE_ORDERS.BRANCH_ID 
        IS 'رقم الفرع - مرتبط بجدول BRANCHES'
        """
        
        oracle.execute_update(comment_query)
        oracle.commit()
        print("✅ تم إضافة التعليق بنجاح")
        
        # 4. إنشاء فهرس للأداء
        print("📝 إنشاء فهرس للأداء...")
        
        try:
            index_query = """
            CREATE INDEX IDX_PO_BRANCH_ID 
            ON PURCHASE_ORDERS(BRANCH_ID)
            """
            
            oracle.execute_update(index_query)
            oracle.commit()
            print("✅ تم إنشاء الفهرس بنجاح")
            
        except Exception as e:
            if "name is already used" in str(e).lower():
                print("ℹ️ الفهرس موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إنشاء الفهرس: {e}")
        
        # 5. تحديث السجلات الموجودة
        print("📝 تحديث السجلات الموجودة...")
        
        update_query = """
        UPDATE PURCHASE_ORDERS 
        SET BRANCH_ID = 21 
        WHERE BRANCH_ID IS NULL
        """
        
        oracle.execute_update(update_query)
        oracle.commit()
        print("✅ تم تحديث السجلات الموجودة")
        
        # 6. التحقق من النتيجة
        print("\n6️⃣ التحقق من النتيجة...")
        
        verify_query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(BRANCH_ID) as records_with_branch,
            MIN(BRANCH_ID) as min_branch,
            MAX(BRANCH_ID) as max_branch
        FROM PURCHASE_ORDERS
        """
        
        verify_result = oracle.execute_query(verify_query)
        if verify_result:
            total, with_branch, min_branch, max_branch = verify_result[0]
            print(f"📊 إجمالي السجلات: {total}")
            print(f"📊 السجلات مع فرع: {with_branch}")
            print(f"📊 أقل رقم فرع: {min_branch}")
            print(f"📊 أعلى رقم فرع: {max_branch}")
        
        print("\n✅ تم إنجاز جميع العمليات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة عمود BRANCH_ID: {e}")
        oracle.rollback()
        return False
        
    finally:
        oracle.disconnect()

def test_branch_integration():
    """اختبار التكامل مع جدول الفروع"""
    
    oracle = OracleManager()
    
    try:
        oracle.connect()
        print("\n🧪 اختبار التكامل مع جدول الفروع...")
        
        # فحص وجود جدول الفروع
        check_branches_query = """
        SELECT COUNT(*) 
        FROM USER_TABLES 
        WHERE TABLE_NAME = 'BRANCHES'
        """
        
        result = oracle.execute_query(check_branches_query)
        branches_table_exists = result[0][0] > 0 if result else False
        
        if not branches_table_exists:
            print("⚠️ جدول BRANCHES غير موجود")
            return False
        
        # جلب عينة من الفروع
        branches_query = """
        SELECT BRN_NO, BRN_LNAME 
        FROM BRANCHES 
        WHERE ROWNUM <= 5
        ORDER BY BRN_NO
        """
        
        branches_result = oracle.execute_query(branches_query)
        
        if branches_result:
            print("📋 الفروع المتاحة:")
            for branch in branches_result:
                print(f"   - {branch[0]}: {branch[1]}")
        else:
            print("⚠️ لا توجد فروع في الجدول")
        
        # اختبار الربط
        join_query = """
        SELECT 
            po.PO_NUMBER,
            po.BRANCH_ID,
            b.BRN_LNAME
        FROM PURCHASE_ORDERS po
        LEFT JOIN BRANCHES b ON po.BRANCH_ID = b.BRN_NO
        WHERE ROWNUM <= 3
        ORDER BY po.ID DESC
        """
        
        join_result = oracle.execute_query(join_query)
        
        if join_result:
            print("\n📋 اختبار الربط:")
            for row in join_result:
                po_number, branch_id, branch_name = row
                print(f"   - أمر {po_number}: فرع {branch_id} ({branch_name or 'غير محدد'})")
        
        print("✅ اختبار التكامل مكتمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False
        
    finally:
        oracle.disconnect()

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إضافة عمود BRANCH_ID إلى جدول PURCHASE_ORDERS")
    print("=" * 70)
    
    # إضافة العمود
    success = add_branch_id_column()
    
    if success:
        # اختبار التكامل
        test_branch_integration()
        
        print("\n" + "=" * 70)
        print("🎉 تم إنجاز جميع العمليات بنجاح!")
        print("✅ عمود BRANCH_ID جاهز للاستخدام في أوامر الشراء")
        
    else:
        print("\n" + "=" * 70)
        print("❌ فشل في إضافة عمود BRANCH_ID")

if __name__ == "__main__":
    main()
