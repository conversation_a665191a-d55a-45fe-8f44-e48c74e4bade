<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📞 دفتر العناوين - نظام البريد الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            direction: rtl;
            color: #ffffff;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .contacts-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .contact-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.5);
        }
        
        .contact-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.2rem;
        }
        
        .contact-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 0.3rem;
        }
        
        .contact-email {
            color: #a0a0a0;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .contact-info {
            color: #c0c0c0;
            font-size: 0.8rem;
        }
        
        .no-contacts {
            text-align: center;
            padding: 4rem 2rem;
        }
        
        .no-contacts-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .add-contact-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            margin-bottom: 2rem;
        }
        
        .add-contact-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .favorite-star {
            color: #ffc107;
        }
        
        /* شريط التنقل */
        .navbar-custom {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .navbar-brand {
            color: #ffffff !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar-nav .nav-link {
            color: #a0a0a0 !important;
            font-weight: 500;
            padding: 0.8rem 1.2rem !important;
            border-radius: 50px;
            transition: all 0.3s ease;
            margin: 0 0.2rem;
        }

        .navbar-nav .nav-link:hover {
            color: #ffffff !important;
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .navbar-nav .nav-link.active {
            color: #ffffff !important;
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .navbar-toggler {
            border: none;
            color: #ffffff;
        }

        .navbar-toggler:focus {
            box-shadow: none;
        }

        .breadcrumb-custom {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }

        .breadcrumb-custom .breadcrumb {
            margin: 0;
            background: none;
            padding: 0;
        }

        .breadcrumb-custom .breadcrumb-item {
            color: #a0a0a0;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: #ffffff;
            font-weight: 600;
        }

        .breadcrumb-custom .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .breadcrumb-custom .breadcrumb-item a:hover {
            color: #ffffff;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .page-title {
                font-size: 2rem;
            }

            .contact-card {
                padding: 1rem;
            }

            .navbar-nav {
                text-align: center;
            }

            .navbar-nav .nav-link {
                margin: 0.2rem 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('email.inbox') }}">
                <i class="fas fa-envelope me-2"></i>
                نظام البريد الإلكتروني
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.inbox') }}">
                            <i class="fas fa-inbox me-1"></i>
                            صندوق الوارد
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.compose') }}">
                            <i class="fas fa-edit me-1"></i>
                            إنشاء رسالة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.sent_box') }}">
                            <i class="fas fa-paper-plane me-1"></i>
                            صندوق الصادر
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.drafts_box') }}">
                            <i class="fas fa-file-text me-1"></i>
                            المسودات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.trash_box') }}">
                            <i class="fas fa-trash me-1"></i>
                            سلة المحذوفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('email.contacts') }}">
                            <i class="fas fa-address-book me-1"></i>
                            دفتر العناوين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.templates') }}">
                            <i class="fas fa-file-alt me-1"></i>
                            القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.analytics') }}">
                            <i class="fas fa-chart-line me-1"></i>
                            التحليلات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('email.settings') }}">
                            <i class="fas fa-cog me-1"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.username if current_user.is_authenticated else 'المستخدم' }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('main.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة المعلومات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <!-- Breadcrumb -->
        <div class="breadcrumb-custom">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('email.inbox') }}">
                            <i class="fas fa-envelope me-1"></i>
                            البريد الإلكتروني
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-address-book me-1"></i>
                        دفتر العناوين
                    </li>
                </ol>
            </nav>
        </div>

        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-address-book"></i>
                دفتر العناوين
            </h1>
            <p style="color: #a0a0a0; font-size: 1.1rem;">
                إدارة جهات اتصال البريد الإلكتروني
            </p>
        </div>
        
        <div class="contacts-container">
            <!-- Add Contact Button -->
            <div class="text-center">
                <button type="button" class="add-contact-btn" data-bs-toggle="modal" data-bs-target="#addContactModal">
                    <i class="fas fa-plus"></i>
                    إضافة جهة اتصال جديدة
                </button>
            </div>
            
            {% if contacts and contacts|length > 0 %}
                <!-- Contacts List -->
                <div class="row">
                    {% for contact in contacts %}
                    <div class="col-md-6 col-lg-4">
                        <div class="contact-card">
                            <div class="d-flex align-items-start">
                                <div class="contact-avatar me-3">
                                    {% if contact.display_name %}
                                        {{ contact.display_name[0].upper() }}
                                    {% else %}
                                        {{ contact.email_address[0].upper() }}
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <div class="contact-name">
                                                {{ contact.display_name or contact.email_address }}
                                                {% if contact.is_favorite %}
                                                    <i class="fas fa-star favorite-star ms-1"></i>
                                                {% endif %}
                                            </div>
                                            <div class="contact-email">
                                                <i class="fas fa-envelope me-1"></i>
                                                {{ contact.email_address }}
                                            </div>
                                            {% if contact.company %}
                                            <div class="contact-info">
                                                <i class="fas fa-building me-1"></i>
                                                {{ contact.company }}
                                            </div>
                                            {% endif %}
                                            {% if contact.phone %}
                                            <div class="contact-info">
                                                <i class="fas fa-phone me-1"></i>
                                                {{ contact.phone }}
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>تعديل</a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('email.compose') }}?to={{ contact.email_address }}"><i class="fas fa-envelope me-2"></i>إرسال رسالة</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteContact('{{ contact.id }}', '{{ contact.display_name or contact.email_address }}')"><i class="fas fa-trash me-2"></i>حذف</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    {% if contact.emails_sent or contact.emails_received %}
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-paper-plane me-1"></i>{{ contact.emails_sent or 0 }} مرسلة
                                            <i class="fas fa-inbox me-1 ms-2"></i>{{ contact.emails_received or 0 }} مستقبلة
                                        </small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- No Contacts Message -->
                <div class="no-contacts">
                    <div class="no-contacts-icon">
                        <i class="fas fa-address-book"></i>
                    </div>
                    <h3 style="color: #ffffff; margin-bottom: 1rem;">لا توجد جهات اتصال</h3>
                    <p style="color: #a0a0a0;">
                        لم يتم إضافة أي جهات اتصال بعد.<br>
                        ابدأ بإضافة جهات اتصالك لسهولة إرسال الرسائل.
                    </p>
                </div>
            {% endif %}
        </div>
        
        <!-- Back Button -->
        <div class="text-center">
            <a href="{{ url_for('email.inbox') }}" class="back-btn">
                <i class="fas fa-arrow-right"></i>
                العودة إلى صندوق الوارد
            </a>
        </div>
    </div>

    <!-- Add Contact Modal -->
    <div class="modal fade" id="addContactModal" tabindex="-1" aria-labelledby="addContactModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: linear-gradient(135deg, #1a1a2e, #16213e); border: 1px solid rgba(255, 255, 255, 0.2);">
                <div class="modal-header" style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                    <h5 class="modal-title" id="addContactModalLabel" style="color: #ffffff;">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة جهة اتصال جديدة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="addContactForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email_address" class="form-label" style="color: #ffffff;">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني *
                                </label>
                                <input type="email" class="form-control" id="email_address" name="email_address" required
                                       style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff;"
                                       placeholder="<EMAIL>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="display_name" class="form-label" style="color: #ffffff;">
                                    <i class="fas fa-user me-1"></i>
                                    الاسم المعروض
                                </label>
                                <input type="text" class="form-control" id="display_name" name="display_name"
                                       style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff;"
                                       placeholder="الاسم الكامل">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label" style="color: #ffffff;">
                                    <i class="fas fa-user me-1"></i>
                                    الاسم الأول
                                </label>
                                <input type="text" class="form-control" id="first_name" name="first_name"
                                       style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff;"
                                       placeholder="الاسم الأول">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label" style="color: #ffffff;">
                                    <i class="fas fa-user me-1"></i>
                                    الاسم الأخير
                                </label>
                                <input type="text" class="form-control" id="last_name" name="last_name"
                                       style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff;"
                                       placeholder="الاسم الأخير">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="company" class="form-label" style="color: #ffffff;">
                                    <i class="fas fa-building me-1"></i>
                                    الشركة
                                </label>
                                <input type="text" class="form-control" id="company" name="company"
                                       style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff;"
                                       placeholder="اسم الشركة">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label" style="color: #ffffff;">
                                    <i class="fas fa-phone me-1"></i>
                                    رقم الهاتف
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff;"
                                       placeholder="+966 50 123 4567">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label" style="color: #ffffff;">
                                <i class="fas fa-sticky-note me-1"></i>
                                ملاحظات
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                      style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #ffffff;"
                                      placeholder="ملاحظات إضافية..."></textarea>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_favorite" name="is_favorite">
                            <label class="form-check-label" for="is_favorite" style="color: #ffffff;">
                                <i class="fas fa-star text-warning me-1"></i>
                                إضافة إلى المفضلة
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer" style="border-top: 1px solid rgba(255, 255, 255, 0.1);">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ جهة الاتصال
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // معالج إضافة جهة اتصال جديدة
        document.getElementById('addContactForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const contactData = {
                email_address: formData.get('email_address'),
                display_name: formData.get('display_name'),
                first_name: formData.get('first_name'),
                last_name: formData.get('last_name'),
                company: formData.get('company'),
                phone: formData.get('phone'),
                notes: formData.get('notes'),
                is_favorite: formData.get('is_favorite') ? true : false
            };

            // إرسال البيانات إلى الخادم
            fetch('/email/api/contacts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(contactData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إغلاق النموذج
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addContactModal'));
                    modal.hide();

                    // إعادة تحميل الصفحة لإظهار جهة الاتصال الجديدة
                    location.reload();
                } else {
                    alert('خطأ: ' + (data.message || 'فشل في إضافة جهة الاتصال'));
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                alert('حدث خطأ في إضافة جهة الاتصال');
            });
        });

        // تحديث الاسم المعروض تلقائياً
        document.getElementById('first_name').addEventListener('input', updateDisplayName);
        document.getElementById('last_name').addEventListener('input', updateDisplayName);

        function updateDisplayName() {
            const firstName = document.getElementById('first_name').value;
            const lastName = document.getElementById('last_name').value;
            const displayNameField = document.getElementById('display_name');

            if (!displayNameField.value && (firstName || lastName)) {
                displayNameField.value = (firstName + ' ' + lastName).trim();
            }
        }

        // دالة حذف جهة اتصال
        function deleteContact(contactId, contactName) {
            if (confirm('هل أنت متأكد من حذف جهة الاتصال "' + contactName + '"؟')) {
                fetch('/email/api/contacts/' + contactId, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('خطأ: ' + (data.message || 'فشل في حذف جهة الاتصال'));
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    alert('حدث خطأ في حذف جهة الاتصال');
                });
            }
        }
    </script>
</body>
</html>
