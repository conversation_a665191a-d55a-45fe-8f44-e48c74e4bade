"""
خدمة WhatsApp Business API
لإرسال أوامر التسليم عبر WhatsApp
"""

import requests
import json
import logging
from typing import Dict, Optional, Tuple
from flask import current_app
import os
from datetime import datetime

class WhatsAppService:
    """خدمة إرسال الرسائل عبر WhatsApp Business API"""
    
    def __init__(self):
        """تهيئة خدمة WhatsApp"""
        # إعدادات API (يجب تعيينها في متغيرات البيئة)
        self.api_url = os.getenv('WHATSAPP_API_URL', 'https://graph.facebook.com/v18.0')
        self.access_token = os.getenv('WHATSAPP_ACCESS_TOKEN', '')
        self.phone_number_id = os.getenv('WHATSAPP_PHONE_NUMBER_ID', '')
        self.business_account_id = os.getenv('WHATSAPP_BUSINESS_ACCOUNT_ID', '')
        
        # إعدادات افتراضية للتطوير والاختبار
        self.test_mode = os.getenv('WHATSAPP_TEST_MODE', 'true').lower() == 'true'
        
        # إعداد السجل
        self.logger = logging.getLogger(__name__)
    
    def is_configured(self) -> bool:
        """التحقق من تكوين API"""
        required_fields = [self.access_token, self.phone_number_id]
        return all(field.strip() for field in required_fields)
    
    def format_phone_number(self, phone: str) -> str:
        """تنسيق رقم الهاتف للـ API"""
        # إزالة جميع الرموز غير الرقمية عدا +
        clean_phone = ''.join(c for c in phone if c.isdigit() or c == '+')
        
        # إزالة + من البداية إذا وجدت
        if clean_phone.startswith('+'):
            clean_phone = clean_phone[1:]
        
        # إضافة رمز السعودية إذا لم يكن موجوداً
        if not clean_phone.startswith('966') and len(clean_phone) == 9:
            clean_phone = '966' + clean_phone
        
        return clean_phone
    
    def create_delivery_order_message(self, order_data: Dict) -> str:
        """إنشاء نص رسالة أمر التسليم"""
        message = f"""🚚 *أمر تسليم جديد*

📋 *رقم الأمر:* {order_data.get('order_number', 'غير محدد')}
📦 *رقم التتبع:* {order_data.get('tracking_number', 'غير محدد')}
🚢 *رقم الحجز:* {order_data.get('booking_number', 'غير محدد')}
📍 *موقع التسليم:* {order_data.get('delivery_location', 'غير محدد')}
📅 *التاريخ المطلوب:* {order_data.get('expected_completion_date', 'غير محدد')}

📄 *أمر التسليم الكامل:*
{order_data.get('pdf_url', '')}

شكراً لتعاونكم 🙏"""

        # إضافة تفاصيل الشحنة إضافية إذا توفرت
        if order_data.get('total_weight'):
            message += f"\n⚖️ *الوزن الإجمالي:* {order_data['total_weight']} كيلو"

        if order_data.get('packages_count'):
            message += f"\n📦 *عدد الطرود:* {order_data['packages_count']}"

        if order_data.get('declared_value'):
            message += f"\n💎 *القيمة المعلنة:* {order_data['declared_value']} {order_data.get('currency', 'SAR')}"

        if order_data.get('estimated_cost'):
            message += f"\n💰 *التكلفة المقدرة:* {order_data['estimated_cost']} {order_data.get('currency', 'SAR')}"

        if order_data.get('priority') and order_data['priority'] != 'normal':
            priority_text = {
                'urgent': '🔴 عاجل',
                'high': '🟡 عالي',
                'low': '🟢 منخفض'
            }.get(order_data['priority'], order_data['priority'])
            message += f"\n⚡ *الأولوية:* {priority_text}"

        # معلومات الاتصال
        if order_data.get('contact_person'):
            message += f"\n👤 *الشخص المسؤول:* {order_data['contact_person']}"

        if order_data.get('contact_phone'):
            message += f"\n📞 *رقم الاتصال:* {order_data['contact_phone']}"

        if order_data.get('special_instructions'):
            message += f"\n📝 *تعليمات خاصة:* {order_data['special_instructions']}"

        message += f"""

📄 *المستند الرسمي:*
{current_app.config.get('BASE_URL', 'http://localhost:5000')}/shipments/delivery-order-pdf/{order_data.get('id')}

🔗 *رابط التفاصيل الكاملة:*
{current_app.config.get('BASE_URL', 'http://localhost:5000')}/shipments/delivery-order-preview/{order_data.get('id')}

يرجى مراجعة المستند الرسمي والرد بالموافقة.

---
🏢 شركة النقل والشحن المتطورة
📧 تم الإرسال تلقائياً من نظام إدارة الشحنات"""

        return message
    
    def send_text_message(self, phone: str, message: str) -> Tuple[bool, str, Optional[str]]:
        """إرسال رسالة نصية عبر WhatsApp API"""
        
        if not self.is_configured():
            return False, "WhatsApp API غير مكون بشكل صحيح", None
        
        # تنسيق رقم الهاتف
        formatted_phone = self.format_phone_number(phone)
        
        # في وضع الاختبار، نسجل فقط ولا نرسل
        if self.test_mode:
            self.logger.info(f"TEST MODE: Would send WhatsApp message to {formatted_phone}")
            self.logger.info(f"Message content: {message}")
            return True, f"تم الإرسال في وضع الاختبار إلى {formatted_phone}", f"test_msg_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # إعداد البيانات للإرسال
        url = f"{self.api_url}/{self.phone_number_id}/messages"
        
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "messaging_product": "whatsapp",
            "to": formatted_phone,
            "type": "text",
            "text": {
                "body": message
            }
        }
        
        try:
            # إرسال الطلب
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                response_data = response.json()
                message_id = response_data.get('messages', [{}])[0].get('id')
                
                self.logger.info(f"WhatsApp message sent successfully to {formatted_phone}, ID: {message_id}")
                return True, f"تم إرسال الرسالة بنجاح إلى {formatted_phone}", message_id
            
            else:
                error_msg = f"خطأ في إرسال الرسالة: {response.status_code} - {response.text}"
                self.logger.error(error_msg)
                return False, error_msg, None
                
        except requests.exceptions.Timeout:
            error_msg = "انتهت مهلة الاتصال مع WhatsApp API"
            self.logger.error(error_msg)
            return False, error_msg, None
            
        except requests.exceptions.RequestException as e:
            error_msg = f"خطأ في الاتصال مع WhatsApp API: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None
            
        except Exception as e:
            error_msg = f"خطأ غير متوقع: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None
    
    def send_delivery_order(self, order_data: Dict, phone: str) -> Tuple[bool, str, Optional[str]]:
        """إرسال أمر التسليم عبر WhatsApp"""
        
        # إنشاء نص الرسالة
        message = self.create_delivery_order_message(order_data)
        
        # إرسال الرسالة
        return self.send_text_message(phone, message)
    
    def send_template_message(self, phone: str, template_name: str, parameters: list) -> Tuple[bool, str, Optional[str]]:
        """إرسال رسالة باستخدام template معتمد"""
        
        if not self.is_configured():
            return False, "WhatsApp API غير مكون بشكل صحيح", None
        
        formatted_phone = self.format_phone_number(phone)
        
        if self.test_mode:
            self.logger.info(f"TEST MODE: Would send template {template_name} to {formatted_phone}")
            return True, f"تم الإرسال في وضع الاختبار", f"test_template_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        url = f"{self.api_url}/{self.phone_number_id}/messages"
        
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "messaging_product": "whatsapp",
            "to": formatted_phone,
            "type": "template",
            "template": {
                "name": template_name,
                "language": {
                    "code": "ar"
                },
                "components": [
                    {
                        "type": "body",
                        "parameters": [{"type": "text", "text": param} for param in parameters]
                    }
                ]
            }
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                response_data = response.json()
                message_id = response_data.get('messages', [{}])[0].get('id')
                return True, f"تم إرسال Template بنجاح", message_id
            else:
                return False, f"خطأ في إرسال Template: {response.text}", None
                
        except Exception as e:
            return False, f"خطأ في إرسال Template: {str(e)}", None
    
    def get_message_status(self, message_id: str) -> Tuple[bool, str, Optional[Dict]]:
        """الحصول على حالة الرسالة"""
        
        if not self.is_configured():
            return False, "WhatsApp API غير مكون بشكل صحيح", None
        
        if self.test_mode:
            return True, "تم التسليم (وضع اختبار)", {"status": "delivered", "timestamp": datetime.now().isoformat()}
        
        # يتطلب webhook لتتبع حالة الرسائل
        # هذه وظيفة متقدمة تحتاج إعداد webhook
        return True, "تتبع الحالة يتطلب إعداد webhook", None


# إنشاء instance عام للخدمة
whatsapp_service = WhatsAppService()


def send_delivery_order_whatsapp(order_data: Dict, phone: str) -> Tuple[bool, str, Optional[str]]:
    """دالة مساعدة لإرسال أمر التسليم"""
    return whatsapp_service.send_delivery_order(order_data, phone)


def is_whatsapp_configured() -> bool:
    """التحقق من تكوين WhatsApp API"""
    return whatsapp_service.is_configured()
