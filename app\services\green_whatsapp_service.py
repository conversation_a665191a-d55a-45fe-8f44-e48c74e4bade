# -*- coding: utf-8 -*-
"""
خدمة Green API WhatsApp
Green API WhatsApp Service for sending delivery orders
"""

import requests
import json
import logging
from typing import Dict, Optional, Tuple
import os
from datetime import datetime

class GreenWhatsAppService:
    """خدمة إرسال الرسائل عبر Green API WhatsApp"""
    
    def __init__(self):
        """تهيئة خدمة Green API WhatsApp"""
        # إعدادات API (يجب تعيينها في متغيرات البيئة)
        self.instance_id = os.getenv('GREEN_API_INSTANCE_ID', '')
        self.api_token = os.getenv('GREEN_API_TOKEN', '')
        self.api_url = os.getenv('GREEN_API_URL', 'https://api.green-api.com')
        
        # إعدادات افتراضية للتطوير والاختبار
        self.test_mode = os.getenv('GREEN_API_TEST_MODE', 'true').lower() == 'true'
        
        # إعداد السجل
        self.logger = logging.getLogger(__name__)
    
    def is_configured(self) -> bool:
        """التحقق من تكوين API"""
        required_fields = [self.instance_id, self.api_token]
        return all(field.strip() for field in required_fields)
    
    def format_phone_number(self, phone: str) -> str:
        """تنسيق رقم الهاتف لـ Green API"""
        # إزالة جميع الرموز غير الرقمية عدا +
        clean_phone = ''.join(c for c in phone if c.isdigit() or c == '+')
        
        # إزالة + من البداية إذا وجدت
        if clean_phone.startswith('+'):
            clean_phone = clean_phone[1:]
        
        # إضافة رمز اليمن إذا لم يكن موجوداً
        if not clean_phone.startswith('967') and len(clean_phone) == 9:
            clean_phone = '967' + clean_phone
        
        # Green API يحتاج @c.us في النهاية
        return clean_phone + '@c.us'
    
    def create_delivery_order_message(self, order_data: Dict) -> str:
        """إنشاء نص رسالة أمر التسليم"""
        message = f"""🚚 *أمر تسليم جديد*

📋 *رقم الأمر:* {order_data.get('order_number', 'غير محدد')}
🚢 *رقم الحجز:* {order_data.get('booking_number', 'غير محدد')}
📍 *موقع التسليم:* {order_data.get('delivery_location', 'غير محدد')}
📅 *التاريخ المطلوب:* {order_data.get('expected_completion_date', 'غير محدد')}

📄 *أمر التسليم الكامل:*
{order_data.get('pdf_url', '')}

شكراً لتعاونكم 🙏"""

        # إضافة تفاصيل الشحنة إضافية إذا توفرت
        if order_data.get('total_weight'):
            message += f"\n⚖️ *الوزن الإجمالي:* {order_data['total_weight']} كيلو"

        if order_data.get('packages_count'):
            message += f"\n📦 *عدد الطرود:* {order_data['packages_count']}"

        if order_data.get('declared_value'):
            message += f"\n💎 *القيمة المعلنة:* {order_data['declared_value']} {order_data.get('currency', 'YER')}"

        if order_data.get('estimated_cost'):
            message += f"\n💰 *التكلفة المقدرة:* {order_data['estimated_cost']} {order_data.get('currency', 'YER')}"

        if order_data.get('priority') and order_data['priority'] != 'normal':
            priority_text = {
                'urgent': '🔴 عاجل',
                'high': '🟡 عالي',
                'low': '🟢 منخفض'
            }.get(order_data['priority'], order_data['priority'])
            message += f"\n⚡ *الأولوية:* {priority_text}"

        # معلومات الاتصال
        if order_data.get('contact_person'):
            message += f"\n👤 *الشخص المسؤول:* {order_data['contact_person']}"

        if order_data.get('contact_phone'):
            message += f"\n📞 *رقم الاتصال:* {order_data['contact_phone']}"

        if order_data.get('special_instructions'):
            message += f"\n📝 *تعليمات خاصة:* {order_data['special_instructions']}"

        message += f"""

📄 *المستند الرسمي:*
http://localhost:5000/shipments/delivery-order-pdf/{order_data.get('id')}

🔗 *رابط التفاصيل الكاملة:*
http://localhost:5000/shipments/delivery-order-preview/{order_data.get('id')}

يرجى مراجعة المستند الرسمي والرد بالموافقة.

---
🏢 {order_data.get('branch_name', 'شركة النقل والشحن المتطورة')}
📧 تم الإرسال تلقائياً من نظام إدارة الشحنات"""

        return message
    
    def send_text_message(self, phone: str, message: str) -> Tuple[bool, str, Optional[str]]:
        """إرسال رسالة نصية عبر Green API"""
        
        if not self.is_configured():
            return False, "Green API غير مكون بشكل صحيح", None
        
        # تنسيق رقم الهاتف
        formatted_phone = self.format_phone_number(phone)
        
        # في وضع الاختبار، نسجل فقط ولا نرسل
        if self.test_mode:
            self.logger.info(f"TEST MODE: Would send Green API message to {formatted_phone}")
            self.logger.info(f"Message content: {message}")
            return True, f"تم الإرسال في وضع الاختبار إلى {formatted_phone}", f"test_msg_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # إعداد البيانات للإرسال
        url = f"{self.api_url}/waInstance{self.instance_id}/sendMessage/{self.api_token}"
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        payload = {
            "chatId": formatted_phone,
            "message": message
        }
        
        try:
            # إرسال الطلب
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                response_data = response.json()
                
                # Green API يرجع idMessage في الاستجابة
                message_id = response_data.get('idMessage', 'unknown')
                
                self.logger.info(f"Green API message sent successfully to {formatted_phone}, ID: {message_id}")
                return True, f"تم إرسال الرسالة بنجاح إلى {formatted_phone}", message_id
            
            else:
                error_msg = f"خطأ في إرسال الرسالة: {response.status_code} - {response.text}"
                self.logger.error(error_msg)
                return False, error_msg, None
                
        except requests.exceptions.Timeout:
            error_msg = "انتهت مهلة الاتصال مع Green API"
            self.logger.error(error_msg)
            return False, error_msg, None
            
        except requests.exceptions.RequestException as e:
            error_msg = f"خطأ في الاتصال مع Green API: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None
            
        except Exception as e:
            error_msg = f"خطأ غير متوقع: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None
    
    def send_delivery_order(self, order_data: Dict, phone: str, include_pdf: bool = True) -> Tuple[bool, str, Optional[str]]:
        """إرسال أمر التسليم عبر Green API WhatsApp"""

        if include_pdf:
            # إرسال مع ملف PDF
            return self.send_delivery_order_with_pdf(order_data, phone)
        else:
            # إرسال نص فقط
            # جلب معلومات الفرع أولاً
            order_data = self._get_branch_info(order_data)
            message = self.create_delivery_order_message(order_data)
            return self.send_text_message(phone, message)

    def _get_branch_info(self, order_data: Dict) -> Dict:
        """جلب معلومات الفرع من قاعدة البيانات"""
        try:
            # إضافة مسار قاعدة البيانات
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from database_manager import DatabaseManager

            db_manager = DatabaseManager()

            # جلب معلومات الفرع
            query = """
                SELECT
                    do.branch_id,
                    b.brn_lname as branch_name,
                    b.brn_ladd as branch_address
                FROM delivery_orders do
                LEFT JOIN branches b ON do.branch_id = b.brn_no
                WHERE do.id = :delivery_order_id
            """

            result = db_manager.execute_query(query, {'delivery_order_id': order_data['id']})

            if result and result[0]:
                row = result[0]
                order_data['branch_name'] = row[1] if row[1] else 'شركة النقل والشحن المتطورة'
                order_data['branch_address'] = row[2] if row[2] else ''

            db_manager.close()

        except Exception as e:
            self.logger.warning(f"فشل في جلب معلومات الفرع: {e}")
            # استخدام القيم الافتراضية
            if 'branch_name' not in order_data:
                order_data['branch_name'] = 'شركة النقل والشحن المتطورة'

        return order_data

    def send_delivery_order_with_pdf(self, order_data: Dict, phone: str) -> Tuple[bool, str, Optional[str]]:
        """إرسال أمر التسليم مع ملف PDF مرفق"""

        try:
            # جلب معلومات الفرع
            order_data = self._get_branch_info(order_data)

            # إنشاء ملف PDF مثالي باستخدام Puppeteer (نفس جودة صفحة المعاينة)
            try:
                from app.services.puppeteer_pdf_service import PuppeteerPDFService
                puppeteer_service = PuppeteerPDFService()
                pdf_path, pdf_message = puppeteer_service.generate_perfect_pdf(order_data['id'])
            except ImportError:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__)))
                from puppeteer_pdf_service import PuppeteerPDFService
                puppeteer_service = PuppeteerPDFService()
                pdf_path, pdf_message = puppeteer_service.generate_perfect_pdf(order_data['id'])

            if not pdf_path:
                # فشل في إنشاء PDF، إعادة المحاولة مرة واحدة
                self.logger.warning(f"فشل في إنشاء PDF: {pdf_message}")
                self.logger.info("🔄 إعادة محاولة إنشاء PDF...")

                # محاولة ثانية
                pdf_path, pdf_message = puppeteer_service.generate_perfect_pdf(order_data['id'])

                if not pdf_path:
                    # فشل مرة أخرى، إرجاع فشل بدلاً من إرسال نص
                    self.logger.error(f"فشل في إنشاء PDF بعد المحاولة الثانية: {pdf_message}")
                    return False, f"فشل في إنشاء PDF: {pdf_message}", None

            # إنشاء رسالة مختصرة مع الملف
            caption = self.create_pdf_caption(order_data)

            # إرسال الملف مباشرة
            return self.send_file_message(phone, pdf_path, caption)

        except Exception as e:
            self.logger.error(f"خطأ في إرسال أمر التسليم مع PDF: {e}")
            # إرجاع فشل بدلاً من إرسال نص
            return False, f"خطأ في إرسال أمر التسليم: {str(e)}", None

    def create_pdf_caption(self, order_data: Dict) -> str:
        """إنشاء تعليق مختصر للملف المرفق"""
        caption = f"""📋 *أمر تسليم جديد*

🚚 *رقم الأمر:* {order_data.get('order_number', 'غير محدد')}
🚢 *رقم الحجز:* {order_data.get('booking_number', 'غير محدد')}
📍 *موقع التسليم:* {order_data.get('delivery_location', 'غير محدد')}

📄 *المستند المرفق يحتوي على جميع التفاصيل*

يرجى مراجعة المستند والرد بالموافقة.

---
🏢 {order_data.get('branch_name', 'شركة النقل والشحن المتطورة')}
📧 تم الإرسال تلقائياً من نظام إدارة الشحنات"""

        return caption

    def _create_file_path(self, file_path: str) -> str:
        """إرجاع مسار الملف للرفع المباشر"""
        # إرجاع المسار كما هو للرفع المباشر
        return file_path
    
    def get_account_info(self) -> Tuple[bool, str, Optional[Dict]]:
        """الحصول على معلومات الحساب"""
        
        if not self.is_configured():
            return False, "Green API غير مكون بشكل صحيح", None
        
        if self.test_mode:
            return True, "معلومات الحساب (وضع اختبار)", {"status": "authorized", "phone": "************"}
        
        url = f"{self.api_url}/waInstance{self.instance_id}/getSettings/{self.api_token}"
        
        try:
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                account_info = response.json()
                return True, "تم جلب معلومات الحساب بنجاح", account_info
            else:
                return False, f"خطأ في جلب معلومات الحساب: {response.text}", None
                
        except Exception as e:
            return False, f"خطأ في الاتصال: {str(e)}", None
    
    def send_file_message(self, phone: str, file_path: str, caption: str = "") -> Tuple[bool, str, Optional[str]]:
        """إرسال ملف عبر Green API باستخدام الرفع المباشر"""

        if not self.is_configured():
            return False, "Green API غير مكون بشكل صحيح", None

        formatted_phone = self.format_phone_number(phone)

        if self.test_mode:
            self.logger.info(f"TEST MODE: Would send file to {formatted_phone}")
            return True, f"تم إرسال الملف في وضع الاختبار", f"test_file_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # التأكد من وجود الملف
        if not os.path.exists(file_path):
            return False, f"الملف غير موجود: {file_path}", None

        url = f"{self.api_url}/waInstance{self.instance_id}/sendFileByUpload/{self.api_token}"

        try:
            # رفع الملف مباشرة
            with open(file_path, 'rb') as file:
                files = {
                    'file': (os.path.basename(file_path), file, 'application/pdf')
                }

                data = {
                    'chatId': formatted_phone,
                    'caption': caption
                }

                response = requests.post(url, files=files, data=data, timeout=120)

            if response.status_code == 200:
                response_data = response.json()
                message_id = response_data.get('idMessage', 'unknown')
                file_url = response_data.get('urlFile', '')

                self.logger.info(f"Green API file uploaded successfully to {formatted_phone}, ID: {message_id}")
                return True, f"تم إرسال الملف بنجاح", message_id
            else:
                error_msg = f"خطأ في رفع الملف: {response.status_code} - {response.text}"
                self.logger.error(error_msg)
                return False, error_msg, None

        except Exception as e:
            error_msg = f"خطأ في رفع الملف: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None


# إنشاء instance عام للخدمة
green_whatsapp_service = GreenWhatsAppService()


def send_delivery_order_green_whatsapp(order_data: Dict, phone: str) -> Tuple[bool, str, Optional[str]]:
    """دالة مساعدة لإرسال أمر التسليم عبر Green API"""
    return green_whatsapp_service.send_delivery_order(order_data, phone)


def is_green_whatsapp_configured() -> bool:
    """التحقق من تكوين Green API"""
    return green_whatsapp_service.is_configured()
