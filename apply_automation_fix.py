#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق إصلاح الأتمتة
Apply Automation Fix
"""

import sys
import os

# إضافة مسار التطبيق
sys.path.append('.')
sys.path.append('app')

def apply_trigger_fix():
    """تطبيق إصلاح الـ trigger"""
    try:
        from database_manager import DatabaseManager
        db_manager = DatabaseManager()
        
        print("🔧 تطبيق إصلاح trigger الأتمتة...")
        
        # قراءة ملف SQL
        with open('fix_automation_trigger.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # تقسيم الـ SQL إلى statements منفصلة
        statements = []
        current_statement = ""
        
        for line in sql_content.split('\n'):
            line = line.strip()
            
            # تجاهل التعليقات والأسطر الفارغة
            if line.startswith('--') or line.startswith('PROMPT') or not line:
                continue
            
            current_statement += line + "\n"
            
            # إذا انتهى الـ statement
            if line.endswith(';') or line.endswith('/'):
                if current_statement.strip():
                    statements.append(current_statement.strip())
                current_statement = ""
        
        # تنفيذ كل statement
        success_count = 0
        for i, statement in enumerate(statements):
            try:
                if statement.endswith('/'):
                    statement = statement[:-1]  # إزالة /
                if statement.endswith(';'):
                    statement = statement[:-1]  # إزالة ;
                
                print(f"تنفيذ statement {i+1}/{len(statements)}...")
                
                # تنفيذ الـ statement
                if statement.strip().upper().startswith('SELECT'):
                    result = db_manager.execute_query(statement)
                    if result:
                        print("النتائج:")
                        for row in result:
                            print(f"  {row}")
                else:
                    result = db_manager.execute_update(statement)
                    print(f"✅ تم التنفيذ بنجاح")
                
                success_count += 1
                
            except Exception as e:
                print(f"⚠️ تحذير في statement {i+1}: {e}")
                # نتابع مع باقي الـ statements
        
        print(f"\n📊 تم تنفيذ {success_count} من أصل {len(statements)} statements")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق الإصلاح: {e}")
        return False

def test_trigger_after_fix():
    """اختبار الـ trigger بعد الإصلاح"""
    try:
        from database_manager import DatabaseManager
        db_manager = DatabaseManager()
        
        print("\n🧪 اختبار الـ trigger بعد الإصلاح...")
        
        # 1. فحص الشحنات الموجودة في CARGO_SHIPMENTS
        print("\n1️⃣ فحص الشحنات في CARGO_SHIPMENTS:")
        shipments_query = """
            SELECT id, shipment_number, shipment_status
            FROM cargo_shipments 
            WHERE ROWNUM <= 3
            ORDER BY id DESC
        """
        
        shipments = db_manager.execute_query(shipments_query)
        if shipments:
            print("   الشحنات المتاحة:")
            for ship in shipments:
                print(f"   - ID: {ship[0]}, رقم: {ship[1]}, حالة: {ship[2]}")
            
            # اختيار أول شحنة للاختبار
            test_shipment = shipments[0]
            shipment_id = test_shipment[0]
            current_status = test_shipment[2]
            
            print(f"\n2️⃣ اختبار الشحنة ID: {shipment_id}")
            print(f"   الحالة الحالية: {current_status}")
            
            # 3. فحص طابور الأتمتة قبل التغيير
            print("\n3️⃣ فحص طابور الأتمتة قبل التغيير:")
            queue_before_query = "SELECT COUNT(*) FROM automation_queue WHERE processed = 0"
            queue_before = db_manager.execute_query(queue_before_query)
            pending_before = queue_before[0][0] if queue_before else 0
            print(f"   عناصر معلقة قبل التغيير: {pending_before}")
            
            # 4. تغيير حالة الشحنة
            new_status = 'arrived_port' if current_status != 'arrived_port' else 'in_transit'
            
            print(f"\n4️⃣ تغيير حالة الشحنة من {current_status} إلى {new_status}:")
            
            update_query = """
                UPDATE cargo_shipments 
                SET shipment_status = :new_status, 
                    updated_at = SYSDATE
                WHERE id = :shipment_id
            """
            
            result = db_manager.execute_update(update_query, {
                'new_status': new_status,
                'shipment_id': shipment_id
            })
            
            print(f"   ✅ تم تحديث {result} سجل")
            
            # 5. فحص طابور الأتمتة بعد التغيير
            print("\n5️⃣ فحص طابور الأتمتة بعد التغيير:")
            
            import time
            time.sleep(2)  # انتظار للـ trigger
            
            queue_after_query = "SELECT COUNT(*) FROM automation_queue WHERE processed = 0"
            queue_after = db_manager.execute_query(queue_after_query)
            pending_after = queue_after[0][0] if queue_after else 0
            print(f"   عناصر معلقة بعد التغيير: {pending_after}")
            
            # 6. فحص آخر عناصر الطابور
            print("\n6️⃣ آخر عناصر طابور الأتمتة:")
            latest_queue_query = """
                SELECT id, shipment_id, old_status, new_status, created_at, processed
                FROM automation_queue 
                ORDER BY created_at DESC
                FETCH FIRST 3 ROWS ONLY
            """
            
            latest_queue = db_manager.execute_query(latest_queue_query)
            if latest_queue:
                for item in latest_queue:
                    status = 'معالج ✅' if item[5] == 1 else 'معلق ❌'
                    print(f"   - شحنة {item[1]}: {item[2]} → {item[3]} في {item[4]} ({status})")
            
            # 7. النتيجة
            print("\n7️⃣ النتيجة:")
            if pending_after > pending_before:
                print("   🎉 الـ Trigger يعمل بشكل مثالي! تم إضافة عنصر جديد للطابور")
                return True
            else:
                print("   ❌ الـ Trigger لا يزال لا يعمل")
                return False
        
        else:
            print("   ❌ لا توجد شحنات في CARGO_SHIPMENTS للاختبار")
            return False
        
        db_manager.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الـ trigger: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 إصلاح مشكلة الأتمتة - إصلاح الـ Trigger")
    print("=" * 60)
    
    # 1. تطبيق إصلاح الـ trigger
    if apply_trigger_fix():
        print("✅ تم تطبيق إصلاح الـ trigger بنجاح")
        
        # 2. اختبار الـ trigger
        if test_trigger_after_fix():
            print("\n🎉 تم إصلاح مشكلة الأتمتة نهائياً!")
            print("💡 الآن ستعمل الأتمتة عند تغيير حالة الشحنة في CARGO_SHIPMENTS")
        else:
            print("\n⚠️ الـ trigger لا يزال لا يعمل، قد تحتاج لفحص إضافي")
    else:
        print("❌ فشل في تطبيق إصلاح الـ trigger")

if __name__ == "__main__":
    main()
