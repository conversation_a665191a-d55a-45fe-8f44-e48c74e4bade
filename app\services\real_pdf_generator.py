# -*- coding: utf-8 -*-
"""
مولد PDF حقيقي من HTML باستخدام xhtml2pdf
Real PDF Generator from HTML using xhtml2pdf
"""

import os
import sys
import requests
import io
from datetime import datetime
from xhtml2pdf import pisa
import arabic_reshaper
from bidi.algorithm import get_display

# إضافة مسار قاعدة البيانات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class RealPDFGenerator:
    """مولد PDF حقيقي من HTML"""
    
    def __init__(self):
        """تهيئة مولد PDF"""
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
        self.base_url = 'http://127.0.0.1:5000'
    
    def generate_real_pdf_from_viewer(self, delivery_order_id):
        """إنشاء PDF حقيقي من صفحة المعاينة"""
        try:
            # إنشاء اسم الملف
            filename = f"delivery_order_real_{delivery_order_id}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # URL صفحة المعاينة
            viewer_url = f"{self.base_url}/shipments/delivery-order-viewer/{delivery_order_id}"
            
            print(f"🌐 تحميل HTML من: {viewer_url}")
            
            # تحميل HTML
            response = requests.get(viewer_url, timeout=15)
            if response.status_code != 200:
                return None, f"فشل في تحميل الصفحة: {response.status_code}"
            
            html_content = response.text
            
            # تنظيف HTML للـ PDF
            cleaned_html = self._clean_html_for_pdf(html_content)
            
            # تحويل إلى PDF باستخدام xhtml2pdf
            if self._convert_with_xhtml2pdf(cleaned_html, filepath):
                return filepath, "تم إنشاء PDF حقيقي بنجاح"
            
            # محاولة بديلة مع WeasyPrint
            if self._convert_with_weasyprint(cleaned_html, filepath):
                return filepath, "تم إنشاء PDF حقيقي بنجاح"
            
            return None, "فشل في تحويل HTML إلى PDF"
            
        except Exception as e:
            return None, f"خطأ في إنشاء PDF حقيقي: {str(e)}"
    
    def _clean_html_for_pdf(self, html_content):
        """تنظيف HTML للتحويل إلى PDF"""
        try:
            # إزالة الأزرار والعناصر غير المطلوبة للطباعة
            import re
            
            # إزالة أزرار التحكم
            html_content = re.sub(r'<div class="controls">.*?</div>', '', html_content, flags=re.DOTALL)
            
            # إزالة الـ scripts غير الضرورية (الاحتفاظ بـ jsPDF فقط)
            html_content = re.sub(r'<script(?![^>]*jspdf).*?</script>', '', html_content, flags=re.DOTALL)
            
            # تحسين CSS للطباعة
            pdf_css = """
            <style>
            @page {
                size: A4;
                margin: 1cm;
            }
            body {
                font-family: 'Noto Sans Arabic', Arial, sans-serif;
                font-size: 12px;
                line-height: 1.4;
                color: #333;
                direction: rtl;
            }
            .delivery-order {
                width: 100%;
                max-width: none;
            }
            .header-section {
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #007bff;
                padding-bottom: 15px;
            }
            .info-section {
                margin-bottom: 15px;
                page-break-inside: avoid;
            }
            .info-grid {
                display: table;
                width: 100%;
                border-collapse: collapse;
            }
            .info-row {
                display: table-row;
            }
            .info-label, .info-value {
                display: table-cell;
                padding: 8px;
                border: 1px solid #ddd;
                vertical-align: top;
            }
            .info-label {
                background-color: #f8f9fa;
                font-weight: bold;
                width: 30%;
            }
            .signature-section {
                margin-top: 30px;
                page-break-inside: avoid;
            }
            .no-print {
                display: none !important;
            }
            </style>
            """
            
            # إضافة CSS للطباعة
            html_content = html_content.replace('</head>', pdf_css + '</head>')
            
            return html_content
            
        except Exception as e:
            print(f"خطأ في تنظيف HTML: {e}")
            return html_content
    
    def _convert_with_xhtml2pdf(self, html_content, output_path):
        """تحويل HTML إلى PDF باستخدام xhtml2pdf"""
        try:
            print("📄 تحويل HTML إلى PDF باستخدام xhtml2pdf...")
            
            # إنشاء ملف PDF
            with open(output_path, 'wb') as result_file:
                # تحويل HTML إلى PDF
                pisa_status = pisa.CreatePDF(
                    html_content.encode('utf-8'),
                    dest=result_file,
                    encoding='utf-8'
                )
                
                # فحص النتيجة
                if not pisa_status.err:
                    file_size = os.path.getsize(output_path)
                    if file_size > 1000:  # على الأقل 1KB
                        print(f"✅ تم إنشاء PDF بنجاح! حجم الملف: {file_size} بايت")
                        return True
                    else:
                        print("❌ الملف المُنشأ صغير جداً")
                        return False
                else:
                    print(f"❌ خطأ في xhtml2pdf: {pisa_status.err}")
                    return False
                    
        except Exception as e:
            print(f"❌ فشل xhtml2pdf: {e}")
            return False
    
    def _convert_with_weasyprint(self, html_content, output_path):
        """تحويل HTML إلى PDF باستخدام WeasyPrint (كبديل)"""
        try:
            print("📄 محاولة تحويل HTML إلى PDF باستخدام WeasyPrint...")
            
            from weasyprint import HTML, CSS
            
            # إنشاء CSS إضافي للعربية
            arabic_css = CSS(string="""
                @font-face {
                    font-family: 'Arabic';
                    src: url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap');
                }
                body {
                    font-family: 'Arabic', 'Noto Sans Arabic', Arial, sans-serif;
                    direction: rtl;
                }
            """)
            
            # تحويل HTML إلى PDF
            html_doc = HTML(string=html_content, base_url=self.base_url)
            html_doc.write_pdf(output_path, stylesheets=[arabic_css])
            
            # فحص النتيجة
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                if file_size > 1000:
                    print(f"✅ تم إنشاء PDF بـ WeasyPrint! حجم الملف: {file_size} بايت")
                    return True
            
            return False
            
        except Exception as e:
            print(f"❌ فشل WeasyPrint: {e}")
            return False
    
    def test_pdf_generation(self, delivery_order_id):
        """اختبار إنشاء PDF"""
        print(f"🧪 اختبار إنشاء PDF حقيقي لأمر التسليم {delivery_order_id}...")
        
        pdf_path, message = self.generate_real_pdf_from_viewer(delivery_order_id)
        
        if pdf_path and os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"✅ نجح الاختبار!")
            print(f"📄 مسار الملف: {pdf_path}")
            print(f"📊 حجم الملف: {file_size} بايت ({file_size/1024:.1f} KB)")
            print(f"📋 الرسالة: {message}")
            
            # فحص إضافي للتأكد أنه PDF حقيقي
            with open(pdf_path, 'rb') as f:
                header = f.read(4)
                if header == b'%PDF':
                    print("✅ الملف هو PDF حقيقي!")
                    return True
                else:
                    print("❌ الملف ليس PDF حقيقي!")
                    return False
        else:
            print(f"❌ فشل الاختبار: {message}")
            return False


# إنشاء instance عام للمولد
real_pdf_generator = RealPDFGenerator()


def generate_real_pdf_from_viewer(delivery_order_id):
    """دالة مساعدة لإنشاء PDF حقيقي من صفحة المعاينة"""
    return real_pdf_generator.generate_real_pdf_from_viewer(delivery_order_id)


def test_real_pdf_generation(delivery_order_id):
    """اختبار إنشاء PDF حقيقي"""
    return real_pdf_generator.test_pdf_generation(delivery_order_id)
