{% extends "base.html" %}

{% block content %}
<!-- Hero Section -->
<div class="ns-hero-section">
    <div class="ns-hero-background"></div>
    <div class="ns-container">
        <div class="ns-hero-content">
            <div class="ns-hero-text">
                <div class="ns-hero-badge">
                    <i class="fas fa-star ns-icon"></i>
                    <span>نظام ERP متطور بتقنية NetSuite</span>
                </div>
                <h1 class="ns-hero-title">نظام إدارة الموارد المؤسسية</h1>
                <p class="ns-hero-subtitle">
                    نظام شامل ومتطور لإدارة المشتريات والمخزون والموردين مع تقارير تحليلية متقدمة
                    وسير عمل ذكي يدعم اللغة العربية بالكامل بتصميم NetSuite Oracle المتطور
                </p>
                <div class="ns-hero-actions">
                    {% if current_user.is_authenticated %}
                        <a href="{{ url_for('main.dashboard') }}" class="ns-btn ns-btn-primary ns-btn-lg">
                            <i class="fas fa-tachometer-alt ns-icon"></i>
                            لوحة المعلومات
                        </a>
                        <a href="{{ url_for('suppliers.index') }}" class="ns-btn ns-btn-orange ns-btn-lg">
                            <i class="fas fa-building ns-icon"></i>
                            إدارة الموردين
                        </a>
                    {% else %}
                        <a href="{{ url_for('auth.login') }}" class="ns-btn ns-btn-primary ns-btn-lg">
                            <i class="fas fa-sign-in-alt ns-icon"></i>
                            تسجيل الدخول
                        </a>
                        <a href="{{ url_for('auth.register') }}" class="ns-btn ns-btn-secondary ns-btn-lg">
                            <i class="fas fa-user-plus ns-icon"></i>
                            إنشاء حساب
                        </a>
                    {% endif %}
                </div>
            </div>
            <div class="ns-hero-visual">
                <div class="ns-hero-icon-container">
                    <i class="fas fa-chart-line ns-hero-icon"></i>
                    <div class="ns-hero-floating-elements">
                        <div class="ns-floating-element ns-floating-1">
                            <i class="fas fa-shopping-cart ns-icon"></i>
                        </div>
                        <div class="ns-floating-element ns-floating-2">
                            <i class="fas fa-boxes ns-icon"></i>
                        </div>
                        <div class="ns-floating-element ns-floating-3">
                            <i class="fas fa-building ns-icon"></i>
                        </div>
                        <div class="ns-floating-element ns-floating-4">
                            <i class="fas fa-chart-bar ns-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="ns-features-section">
    <div class="ns-container">
        <div class="ns-section-header">
            <h2 class="ns-section-title">مميزات النظام</h2>
            <p class="ns-section-subtitle">نظام متكامل يغطي جميع احتياجات إدارة المشتريات والمخزون بتقنية NetSuite المتطورة</p>
        </div>

        <div class="ns-features-grid">
            <!-- Purchase Management -->
            <div class="ns-feature-card">
                <div class="ns-feature-icon ns-feature-icon-primary">
                    <i class="fas fa-shopping-cart ns-icon"></i>
                </div>
                <div class="ns-feature-content">
                    <h3 class="ns-feature-title">إدارة المشتريات</h3>
                    <p class="ns-feature-description">
                        نظام شامل لإدارة طلبات الشراء وأوامر الشراء مع سير عمل ذكي للموافقات
                        وتتبع حالة الطلبات من البداية حتى النهاية
                    </p>
                    <div class="ns-feature-highlights">
                        <span class="ns-highlight">طلبات الشراء</span>
                        <span class="ns-highlight">أوامر الشراء</span>
                        <span class="ns-highlight">سير العمل</span>
                    </div>
                </div>
            </div>

            <!-- Inventory Management -->
            <div class="ns-feature-card">
                <div class="ns-feature-icon ns-feature-icon-success">
                    <i class="fas fa-boxes ns-icon"></i>
                </div>
                <div class="ns-feature-content">
                    <h3 class="ns-feature-title">إدارة المخزون</h3>
                    <p class="ns-feature-description">
                        تتبع دقيق للمخزون مع نظام تنبيهات للحد الأدنى والأقصى
                        وإدارة حركات المخزون والتسويات التلقائية
                    </p>
                    <div class="ns-feature-highlights">
                        <span class="ns-highlight">تتبع المخزون</span>
                        <span class="ns-highlight">التنبيهات</span>
                        <span class="ns-highlight">التسويات</span>
                    </div>
                </div>
            </div>

            <!-- Supplier Management -->
            <div class="ns-feature-card">
                <div class="ns-feature-icon ns-feature-icon-orange">
                    <i class="fas fa-building ns-icon"></i>
                </div>
                <div class="ns-feature-content">
                    <h3 class="ns-feature-title">إدارة الموردين</h3>
                    <p class="ns-feature-description">
                        قاعدة بيانات شاملة للموردين مع تقييم الأداء وإدارة العقود
                        ونظام تصنيف واعتماد الموردين
                    </p>
                    <div class="ns-feature-highlights">
                        <span class="ns-highlight">قاعدة الموردين</span>
                        <span class="ns-highlight">تقييم الأداء</span>
                        <span class="ns-highlight">العقود</span>
                    </div>
                </div>
            </div>

            <!-- Financial System -->
            <div class="ns-feature-card">
                <div class="ns-feature-icon ns-feature-icon-warning">
                    <i class="fas fa-money-bill-wave ns-icon"></i>
                </div>
                <div class="ns-feature-content">
                    <h3 class="ns-feature-title">النظام المالي</h3>
                    <p class="ns-feature-description">
                        إدارة الفواتير والمدفوعات مع ربط المشتريات بالحسابات
                        وتتبع الالتزامات المالية والتقارير المالية
                    </p>
                    <div class="ns-feature-highlights">
                        <span class="ns-highlight">الفواتير</span>
                        <span class="ns-highlight">المدفوعات</span>
                        <span class="ns-highlight">التقارير المالية</span>
                    </div>
                </div>
            </div>

            <!-- Reports & Analytics -->
            <div class="ns-feature-card">
                <div class="ns-feature-icon ns-feature-icon-info">
                    <i class="fas fa-chart-bar ns-icon"></i>
                </div>
                <div class="ns-feature-content">
                    <h3 class="ns-feature-title">التقارير والتحليل</h3>
                    <p class="ns-feature-description">
                        تقارير تفصيلية وتحليلات متقدمة مع مؤشرات الأداء الرئيسية
                        ولوحات معلومات تفاعلية لاتخاذ القرارات
                    </p>
                    <div class="ns-feature-highlights">
                        <span class="ns-highlight">التقارير التفصيلية</span>
                        <span class="ns-highlight">التحليلات</span>
                        <span class="ns-highlight">لوحات المعلومات</span>
                    </div>
                </div>
            </div>

            <!-- Workflow System -->
            <div class="ns-feature-card">
                <div class="ns-feature-icon ns-feature-icon-secondary">
                    <i class="fas fa-cogs ns-icon"></i>
                </div>
                <div class="ns-feature-content">
                    <h3 class="ns-feature-title">سير العمل الذكي</h3>
                    <p class="ns-feature-description">
                        أتمتة عمليات الموافقة مع تتبع المراحل والإشعارات التلقائية
                        وإدارة الصلاحيات المتقدمة حسب المستويات
                    </p>
                    <div class="ns-feature-highlights">
                        <span class="ns-highlight">الأتمتة</span>
                        <span class="ns-highlight">الموافقات</span>
                        <span class="ns-highlight">الصلاحيات</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Section -->
<div class="ns-stats-section">
    <div class="ns-container">
        <div class="ns-section-header">
            <h2 class="ns-section-title">النظام بالأرقام</h2>
            <p class="ns-section-subtitle">إحصائيات تؤكد تميز وموثوقية النظام</p>
        </div>

        <div class="ns-stats-grid">
            <div class="ns-stat-card">
                <div class="ns-stat-icon">
                    <i class="fas fa-language ns-icon"></i>
                </div>
                <div class="ns-stat-content">
                    <div class="ns-stat-number">100%</div>
                    <h4 class="ns-stat-title">دعم اللغة العربية</h4>
                    <p class="ns-stat-description">واجهة كاملة باللغة العربية مع دعم RTL متقدم</p>
                </div>
            </div>

            <div class="ns-stat-card">
                <div class="ns-stat-icon">
                    <i class="fas fa-clock ns-icon"></i>
                </div>
                <div class="ns-stat-content">
                    <div class="ns-stat-number">24/7</div>
                    <h4 class="ns-stat-title">متاح دائماً</h4>
                    <p class="ns-stat-description">نظام سحابي متاح على مدار الساعة بموثوقية عالية</p>
                </div>
            </div>

            <div class="ns-stat-card">
                <div class="ns-stat-icon">
                    <i class="fas fa-expand-arrows-alt ns-icon"></i>
                </div>
                <div class="ns-stat-content">
                    <div class="ns-stat-number">∞</div>
                    <h4 class="ns-stat-title">قابلية التوسع</h4>
                    <p class="ns-stat-description">يدعم أي حجم من المؤسسات من الصغيرة للكبيرة</p>
                </div>
            </div>

            <div class="ns-stat-card">
                <div class="ns-stat-icon">
                    <i class="fas fa-shield-alt ns-icon"></i>
                </div>
                <div class="ns-stat-content">
                    <div class="ns-stat-number">🔒</div>
                    <h4 class="ns-stat-title">أمان عالي</h4>
                    <p class="ns-stat-description">حماية متقدمة للبيانات مع تشفير من الدرجة المصرفية</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Technology Stack -->
<div class="ns-tech-section">
    <div class="ns-container">
        <div class="ns-section-header">
            <h2 class="ns-section-title">التقنيات المستخدمة</h2>
            <p class="ns-section-subtitle">مبني بأحدث التقنيات لضمان الأداء والموثوقية العالية</p>
        </div>

        <div class="ns-tech-grid">
            <div class="ns-tech-item">
                <div class="ns-tech-icon">
                    <i class="fab fa-python ns-icon"></i>
                </div>
                <h6 class="ns-tech-name">Python</h6>
                <p class="ns-tech-desc">لغة البرمجة الأساسية</p>
            </div>

            <div class="ns-tech-item">
                <div class="ns-tech-icon">
                    <i class="fas fa-flask ns-icon"></i>
                </div>
                <h6 class="ns-tech-name">Flask</h6>
                <p class="ns-tech-desc">إطار العمل الخلفي</p>
            </div>

            <div class="ns-tech-item">
                <div class="ns-tech-icon">
                    <i class="fas fa-database ns-icon"></i>
                </div>
                <h6 class="ns-tech-name">Oracle DB</h6>
                <p class="ns-tech-desc">قاعدة البيانات المؤسسية</p>
            </div>

            <div class="ns-tech-item">
                <div class="ns-tech-icon">
                    <i class="fab fa-js-square ns-icon"></i>
                </div>
                <h6 class="ns-tech-name">JavaScript</h6>
                <p class="ns-tech-desc">التفاعل الأمامي</p>
            </div>

            <div class="ns-tech-item">
                <div class="ns-tech-icon">
                    <i class="fas fa-chart-line ns-icon"></i>
                </div>
                <h6 class="ns-tech-name">Chart.js</h6>
                <p class="ns-tech-desc">الرسوم البيانية</p>
            </div>

            <div class="ns-tech-item">
                <div class="ns-tech-icon">
                    <i class="fas fa-cloud ns-icon"></i>
                </div>
                <h6 class="ns-tech-name">Cloud Ready</h6>
                <p class="ns-tech-desc">جاهز للسحابة</p>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action -->
{% if not current_user.is_authenticated %}
<div class="ns-cta-section">
    <div class="ns-container">
        <div class="ns-cta-content">
            <div class="ns-cta-text">
                <h2 class="ns-cta-title">ابدأ رحلتك الرقمية الآن</h2>
                <p class="ns-cta-subtitle">انضم إلى آلاف المؤسسات التي تستخدم نظامنا لإدارة مشترياتها بكفاءة وفعالية</p>
            </div>
            <div class="ns-cta-actions">
                <a href="{{ url_for('auth.register') }}" class="ns-btn ns-btn-primary ns-btn-lg">
                    <i class="fas fa-user-plus ns-icon"></i>
                    إنشاء حساب مجاني
                </a>
                <a href="{{ url_for('auth.login') }}" class="ns-btn ns-btn-secondary ns-btn-lg">
                    <i class="fas fa-sign-in-alt ns-icon"></i>
                    تسجيل الدخول
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Welcome notification
    setTimeout(() => {
        if (typeof nsNotifications !== 'undefined') {
            nsNotifications.info('مرحباً بك في نظام ERP المتطور بتقنية NetSuite!', { duration: 4000 });
        }
    }, 1000);

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('ns-animate-in');
            }
        });
    }, observerOptions);

    // Observe feature cards
    const featureCards = document.querySelectorAll('.ns-feature-card');
    featureCards.forEach(card => {
        card.classList.add('ns-animate-ready');
        observer.observe(card);
    });

    // Observe stat cards
    const statCards = document.querySelectorAll('.ns-stat-card');
    statCards.forEach(card => {
        card.classList.add('ns-animate-ready');
        observer.observe(card);
    });

    // Observe tech items
    const techItems = document.querySelectorAll('.ns-tech-item');
    techItems.forEach(item => {
        item.classList.add('ns-animate-ready');
        observer.observe(item);
    });

    // Floating elements animation
    const floatingElements = document.querySelectorAll('.ns-floating-element');
    floatingElements.forEach((element, index) => {
        element.style.animationDelay = `${index * 0.5}s`;
    });

    // Parallax effect for hero section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const heroBackground = document.querySelector('.ns-hero-background');
        if (heroBackground) {
            heroBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });

    // Counter animation for stats
    const animateCounters = () => {
        const counters = document.querySelectorAll('.ns-stat-number');
        counters.forEach(counter => {
            const target = counter.textContent;
            if (target.includes('%')) {
                animateNumber(counter, 0, 100, '%');
            }
        });
    };

    const animateNumber = (element, start, end, suffix = '') => {
        const duration = 2000;
        const increment = (end - start) / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                current = end;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current) + suffix;
        }, 16);
    };

    // Trigger counter animation when stats section is visible
    const statsSection = document.querySelector('.ns-stats-section');
    if (statsSection) {
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    statsObserver.unobserve(entry.target);
                }
            });
        });
        statsObserver.observe(statsSection);
    }

    console.log('NetSuite Homepage initialized');
});
</script>
{% endblock %}
