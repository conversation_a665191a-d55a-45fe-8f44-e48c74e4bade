#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحقيق مفصل حول أمر الشراء PO-2025-0011
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def investigate_po_2025_0011():
    """تحقيق مفصل حول أمر الشراء PO-2025-0011"""
    print("🔍 تحقيق مفصل حول أمر الشراء PO-2025-0011...")
    
    try:
        from oracle_manager import get_oracle_manager
        db_manager = get_oracle_manager()
        
        if not db_manager.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        # 1. فحص تفاصيل أمر الشراء PO-2025-0011
        po_details_query = """
            SELECT ID, PO_NUMBER, SUPPLIER_NAME, STATUS, TOTAL_AMOUNT,
                   IS_USED, USED_AT, USED_IN_SHIPMENT_ID, CREATED_AT
            FROM PURCHASE_ORDERS
            WHERE PO_NUMBER = 'PO-2025-0011'
        """
        
        po_details = db_manager.execute_query(po_details_query)
        
        if not po_details:
            print("❌ أمر الشراء PO-2025-0011 غير موجود!")
            return False
        
        po_data = po_details[0]
        po_id, po_number, supplier_name, status, total_amount, is_used, used_at, used_in_shipment, created_at = po_data
        
        print(f"\n📋 تفاصيل أمر الشراء PO-2025-0011:")
        print(f"   🆔 ID: {po_id}")
        print(f"   📋 رقم الأمر: {po_number}")
        print(f"   🏢 المورد: {supplier_name}")
        print(f"   📊 الحالة: {status}")
        print(f"   💰 المبلغ: {total_amount}")
        print(f"   ✅ مستخدم: {'نعم' if is_used == 1 else 'لا'}")
        print(f"   📅 تاريخ الاستخدام: {used_at or 'غير محدد'}")
        print(f"   📦 معرف الشحنة: {used_in_shipment or 'غير محدد'}")
        print(f"   📅 تاريخ الإنشاء: {created_at}")
        
        # 2. البحث عن جميع الشحنات المرتبطة بهذا الأمر
        shipments_query = """
            SELECT id, shipment_number, tracking_number, shipment_status, created_at
            FROM cargo_shipments
            WHERE purchase_order_id = :1
            ORDER BY created_at DESC
        """
        
        shipments = db_manager.execute_query(shipments_query, [po_id])
        
        print(f"\n📦 الشحنات المرتبطة بأمر الشراء PO-2025-0011:")
        print("-" * 70)
        
        if shipments:
            print(f"   🚨 تم العثور على {len(shipments)} شحنة مرتبطة!")
            for shipment in shipments:
                shipment_id, shipment_number, tracking_number, shipment_status, created_at = shipment
                print(f"   📦 الشحنة: {shipment_number} (ID: {shipment_id})")
                print(f"      🔍 رقم التتبع: {tracking_number}")
                print(f"      📊 الحالة: {shipment_status}")
                print(f"      📅 تاريخ الإنشاء: {created_at}")
                print()
        else:
            print("   ✅ لا توجد شحنات مرتبطة بهذا الأمر")
        
        # 3. فحص أصناف الشحنات المرتبطة بهذا الأمر
        items_query = """
            SELECT csi.id, csi.cargo_shipment_id, csi.item_name, csi.quantity, 
                   cs.shipment_number, cs.tracking_number
            FROM cargo_shipment_items csi
            INNER JOIN cargo_shipments cs ON csi.cargo_shipment_id = cs.id
            WHERE cs.purchase_order_id = :1
            ORDER BY csi.id DESC
        """
        
        items = db_manager.execute_query(items_query, [po_id])
        
        print(f"\n📋 أصناف الشحنات المرتبطة بأمر الشراء PO-2025-0011:")
        print("-" * 70)
        
        if items:
            print(f"   🚨 تم العثور على {len(items)} صنف في الشحنات!")
            for item in items:
                item_id, shipment_id, item_name, quantity, shipment_number, tracking_number = item
                print(f"   📋 الصنف: {item_name} (ID: {item_id})")
                print(f"      📦 الكمية: {quantity}")
                print(f"      📦 الشحنة: {shipment_number} ({tracking_number})")
                print()
        else:
            print("   ✅ لا توجد أصناف مرتبطة")
        
        # 4. فحص الحاويات المرتبطة بالشحنات
        containers_query = """
            SELECT cc.id, cc.container_number, cc.container_type, 
                   cs.shipment_number, cs.tracking_number
            FROM cargo_containers cc
            INNER JOIN cargo_shipments cs ON cc.cargo_shipment_id = cs.id
            WHERE cs.purchase_order_id = :1
            ORDER BY cc.id DESC
        """
        
        containers = db_manager.execute_query(containers_query, [po_id])
        
        print(f"\n📦 الحاويات المرتبطة بأمر الشراء PO-2025-0011:")
        print("-" * 70)
        
        if containers:
            print(f"   🚨 تم العثور على {len(containers)} حاوية!")
            for container in containers:
                container_id, container_number, container_type, shipment_number, tracking_number = container
                print(f"   📦 الحاوية: {container_number} (ID: {container_id})")
                print(f"      📏 النوع: {container_type}")
                print(f"      📦 الشحنة: {shipment_number} ({tracking_number})")
                print()
        else:
            print("   ✅ لا توجد حاويات مرتبطة")
        
        # 5. فحص سبب ظهور الأمر في API كغير مستخدم
        api_check_query = """
            SELECT po.ID, po.PO_NUMBER, po.IS_USED,
                   cs.shipment_count,
                   CASE 
                       WHEN cs.shipment_count IS NULL OR cs.shipment_count = 0 THEN 'سيظهر في API'
                       ELSE 'لن يظهر في API'
                   END as api_visibility
            FROM PURCHASE_ORDERS po
            LEFT JOIN (
                SELECT purchase_order_id,
                       COUNT(*) as shipment_count
                FROM cargo_shipments
                WHERE purchase_order_id IS NOT NULL
                GROUP BY purchase_order_id
            ) cs ON po.ID = cs.purchase_order_id
            WHERE po.PO_NUMBER = 'PO-2025-0011'
        """
        
        api_check = db_manager.execute_query(api_check_query)
        
        print(f"\n🔍 تحليل ظهور الأمر في API:")
        print("-" * 70)
        
        if api_check:
            api_data = api_check[0]
            po_id_api, po_number_api, is_used_api, shipment_count, api_visibility = api_data
            
            print(f"   📋 أمر الشراء: {po_number_api}")
            print(f"   ✅ IS_USED: {is_used_api}")
            print(f"   📦 عدد الشحنات الفعلية: {shipment_count or 0}")
            print(f"   👁️ ظهور في API: {api_visibility}")
            
            if shipment_count and shipment_count > 0 and api_visibility == 'سيظهر في API':
                print(f"\n   🚨 مشكلة: الأمر مستخدم في {shipment_count} شحنة ولكن ما زال يظهر في API!")
                print(f"   🔧 السبب: منطق API يعتمد على عدد الشحنات وليس على IS_USED")
        
        # 6. التوصيات
        print(f"\n💡 التوصيات:")
        print("-" * 70)
        
        if shipments:
            if is_used == 0:
                print("   🔧 يجب تحديث IS_USED = 1 لهذا الأمر")
                print("   🔧 يجب تحديث USED_AT و USED_IN_SHIPMENT_ID")
            
            print("   🔧 يجب تحديث منطق API ليعتمد على IS_USED بدلاً من عدد الشحنات")
            print("   🔧 أو تحديث جميع أوامر الشراء المستخدمة فعلياً")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقيق: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء التحقيق المفصل...")
    print("=" * 50)
    
    investigate_po_2025_0011()
    
    print("\n" + "=" * 50)
    print("🏁 انتهى التحقيق")
