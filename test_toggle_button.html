<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر التصفية المتقدمة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 25px auto;
            max-width: 800px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .advanced-filters {
            margin-top: 20px;
            animation: slideDown 0.3s ease-out;
        }
        
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .test-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h1 class="text-center mb-4">
                <i class="fas fa-bug me-2"></i>
                اختبار زر التصفية المتقدمة
            </h1>
            
            <!-- محاكاة الزر الأصلي -->
            <div class="row mb-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-0" placeholder="البحث في العقود...">
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-primary me-2">
                        <i class="fas fa-sync-alt me-2"></i>تحديث
                    </button>
                    <button class="btn btn-outline-info" id="advancedFiltersToggle" 
                            onclick="if(typeof toggleAdvancedFilters === 'function') toggleAdvancedFilters(); else console.log('الدالة غير جاهزة بعد');">
                        <i class="fas fa-filter me-2"></i>إظهار التصفية المتقدمة
                    </button>
                </div>
            </div>

            <!-- المرشحات المتقدمة -->
            <div id="advancedFilters" class="advanced-filters" style="display: none;">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-sliders-h me-2"></i>التصفية المتقدمة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label class="form-label">حالة العقد</label>
                                <select class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="مسودة">مسودة</option>
                                    <option value="معتمد">معتمد</option>
                                    <option value="نشط">نشط</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">حالة الاستخدام</label>
                                <select class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="0">غير مستخدم</option>
                                    <option value="1">مستخدم</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">المبلغ من</label>
                                <input type="number" class="form-control" placeholder="0.00">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">المبلغ إلى</label>
                                <input type="number" class="form-control" placeholder="0.00">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-outline-warning me-2">
                                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                                </button>
                                <button class="btn btn-outline-success">
                                    <i class="fas fa-download me-1"></i>تصدير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الاختبار -->
            <div class="row mt-4">
                <div class="col-12">
                    <h5>أزرار الاختبار:</h5>
                    <button class="btn btn-success me-2" onclick="testToggleFunction()">
                        <i class="fas fa-play me-2"></i>اختبار الدالة مباشرة
                    </button>
                    <button class="btn btn-info me-2" onclick="testButtonClick()">
                        <i class="fas fa-mouse-pointer me-2"></i>محاكاة النقر على الزر
                    </button>
                    <button class="btn btn-warning me-2" onclick="clearLog()">
                        <i class="fas fa-trash me-2"></i>مسح السجل
                    </button>
                    <button class="btn btn-secondary" onclick="checkElements()">
                        <i class="fas fa-search me-2"></i>فحص العناصر
                    </button>
                </div>
            </div>

            <!-- سجل الاختبار -->
            <div class="test-log" id="testLog">
                <div class="text-muted">سجل الاختبار سيظهر هنا...</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تبديل المرشحات المتقدمة - نفس الدالة من الملف الأصلي
        function toggleAdvancedFilters() {
            log('🔄 تبديل المرشحات المتقدمة...');
            
            const filtersDiv = document.getElementById('advancedFilters');
            const button = document.getElementById('advancedFiltersToggle');
            
            if (!filtersDiv) {
                log('❌ لم يتم العثور على عنصر المرشحات المتقدمة', 'error');
                return;
            }
            
            if (!button) {
                log('❌ لم يتم العثور على زر التبديل', 'error');
                return;
            }
            
            // التحقق من الحالة الحالية
            const isVisible = filtersDiv.style.display === 'block';

            log(`📊 الحالة الحالية: display="${filtersDiv.style.display}", isVisible=${isVisible}`);

            if (isVisible) {
                // إخفاء المرشحات
                filtersDiv.style.display = 'none';
                button.innerHTML = '<i class="fas fa-filter me-2"></i>إظهار التصفية المتقدمة';
                button.classList.remove('btn-warning');
                button.classList.add('btn-outline-info');
                log('🔽 تم إخفاء المرشحات المتقدمة', 'success');
            } else {
                // إظهار المرشحات
                filtersDiv.style.display = 'block';
                button.innerHTML = '<i class="fas fa-filter-circle-xmark me-2"></i>إخفاء التصفية المتقدمة';
                button.classList.remove('btn-outline-info');
                button.classList.add('btn-warning');
                log('🔼 تم إظهار المرشحات المتقدمة', 'success');
            }
        }

        // جعل الدالة متاحة عالمياً
        window.toggleAdvancedFilters = toggleAdvancedFilters;

        // دوال الاختبار
        function testToggleFunction() {
            log('🧪 اختبار الدالة مباشرة...');
            try {
                toggleAdvancedFilters();
                log('✅ نجح اختبار الدالة المباشر', 'success');
            } catch (error) {
                log(`❌ فشل اختبار الدالة: ${error.message}`, 'error');
            }
        }

        function testButtonClick() {
            log('🧪 محاكاة النقر على الزر...');
            const button = document.getElementById('advancedFiltersToggle');
            if (button) {
                button.click();
                log('✅ تم النقر على الزر بنجاح', 'success');
            } else {
                log('❌ لم يتم العثور على الزر', 'error');
            }
        }

        function checkElements() {
            log('🔍 فحص العناصر...');
            
            const filtersDiv = document.getElementById('advancedFilters');
            const button = document.getElementById('advancedFiltersToggle');
            
            log(`📋 عنصر المرشحات: ${filtersDiv ? '✅ موجود' : '❌ غير موجود'}`);
            log(`🔘 زر التبديل: ${button ? '✅ موجود' : '❌ غير موجود'}`);
            log(`🔧 دالة toggleAdvancedFilters: ${typeof toggleAdvancedFilters === 'function' ? '✅ متاحة' : '❌ غير متاحة'}`);
            log(`🌐 window.toggleAdvancedFilters: ${typeof window.toggleAdvancedFilters === 'function' ? '✅ متاحة' : '❌ غير متاحة'}`);
            
            if (filtersDiv) {
                log(`📊 حالة المرشحات: display="${filtersDiv.style.display}"`);
            }
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '<div class="text-muted">تم مسح السجل...</div>';
        }

        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colorClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-dark';
            
            const logEntry = document.createElement('div');
            logEntry.className = colorClass;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            if (logDiv.children.length === 1 && logDiv.children[0].classList.contains('text-muted')) {
                logDiv.innerHTML = '';
            }
            
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل صفحة الاختبار');
            
            // إضافة event listener للزر
            const button = document.getElementById('advancedFiltersToggle');
            if (button) {
                button.addEventListener('click', function() {
                    log('👆 تم النقر على الزر عبر event listener');
                });
            }
            
            // فحص العناصر تلقائياً
            setTimeout(checkElements, 500);
        });
    </script>
</body>
</html>
