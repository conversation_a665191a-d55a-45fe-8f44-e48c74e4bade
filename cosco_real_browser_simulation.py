#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محاكاة متصفح حقيقي لجلب البيانات من COSCO
"""

import requests
import urllib3
import re
import json
import time
from urllib.parse import urljoin, urlparse

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class COSCOBrowserSimulator:
    def __init__(self):
        self.session = requests.Session()
        self.session.verify = False
        
        # محاكاة متصفح Chrome حقيقي
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
        
        self.base_url = "https://elines.coscoshipping.com"
        self.tracking_url = f"{self.base_url}/ebusiness/cargoTracking"
    
    def get_real_tracking_data(self, booking_number):
        """جلب البيانات الحقيقية من COSCO"""
        
        print(f"🚀 بدء محاكاة المتصفح لرقم: {booking_number}")
        print("=" * 60)
        
        try:
            # الخطوة 1: زيارة الصفحة الرئيسية
            print("📡 الخطوة 1: زيارة الصفحة الرئيسية...")
            response = self.session.get(self.tracking_url, timeout=20)
            
            if response.status_code != 200:
                return self._error_result(f"فشل في الوصول للموقع: {response.status_code}")
            
            print(f"✅ تم الوصول للموقع (Status: {response.status_code})")
            print(f"🍪 Cookies: {len(self.session.cookies)} cookies")
            
            # الخطوة 2: تحليل الصفحة للعثور على form
            print("\n📡 الخطوة 2: تحليل الصفحة...")
            form_data = self._analyze_page(response.text)
            
            if not form_data:
                return self._error_result("لم يتم العثور على form البحث")
            
            # الخطوة 3: إرسال طلب البحث
            print("\n📡 الخطوة 3: إرسال طلب البحث...")
            search_result = self._submit_search(booking_number, form_data)
            
            if not search_result:
                return self._error_result("فشل في إرسال طلب البحث")
            
            # الخطوة 4: تحليل النتائج
            print("\n📡 الخطوة 4: تحليل النتائج...")
            tracking_data = self._parse_results(search_result, booking_number)
            
            return tracking_data
            
        except Exception as e:
            return self._error_result(f"خطأ عام: {str(e)}")
    
    def _analyze_page(self, html):
        """تحليل الصفحة للعثور على form البحث"""
        
        print("🔍 تحليل HTML للعثور على form...")
        
        # البحث عن forms
        forms = re.findall(r'<form[^>]*>(.*?)</form>', html, re.DOTALL | re.IGNORECASE)
        print(f"📝 وجد {len(forms)} form")
        
        # البحث عن input fields
        inputs = re.findall(r'<input[^>]*>', html, re.IGNORECASE)
        print(f"📋 وجد {len(inputs)} input field")
        
        # البحث عن JavaScript variables
        js_vars = self._extract_js_variables(html)
        if js_vars:
            print(f"🔧 وجد متغيرات JavaScript: {list(js_vars.keys())}")
        
        # البحث عن CSRF tokens
        csrf_token = self._extract_csrf_token(html)
        if csrf_token:
            print(f"🔑 وجد CSRF token: {csrf_token[:20]}...")
        
        # البحث عن API endpoints في JavaScript
        api_endpoints = self._extract_api_endpoints(html)
        if api_endpoints:
            print(f"🔗 وجد API endpoints: {api_endpoints}")
        
        return {
            'csrf_token': csrf_token,
            'js_vars': js_vars,
            'api_endpoints': api_endpoints,
            'has_forms': len(forms) > 0
        }
    
    def _extract_js_variables(self, html):
        """استخراج متغيرات JavaScript"""
        js_vars = {}
        
        patterns = [
            r'var\s+(\w+)\s*=\s*["\']([^"\']+)["\']',
            r'window\.(\w+)\s*=\s*["\']([^"\']+)["\']',
            r'(\w+)\s*:\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:
                    js_vars[match[0]] = match[1]
        
        return js_vars
    
    def _extract_csrf_token(self, html):
        """استخراج CSRF token"""
        patterns = [
            r'name=["\']_token["\'][^>]*value=["\']([^"\']+)["\']',
            r'value=["\']([^"\']+)["\'][^>]*name=["\']_token["\']',
            r'csrf["\']?\s*:\s*["\']([^"\']+)["\']',
            r'_token["\']?\s*:\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            if matches:
                return matches[0]
        
        return None
    
    def _extract_api_endpoints(self, html):
        """استخراج API endpoints من JavaScript"""
        endpoints = []
        
        patterns = [
            r'url\s*:\s*["\']([^"\']*api[^"\']*)["\']',
            r'["\']([^"\']*cargoTracking[^"\']*)["\']',
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'ajax\s*\(\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            for match in matches:
                if match.startswith('/') or 'api' in match.lower():
                    endpoints.append(match)
        
        return list(set(endpoints))
    
    def _submit_search(self, booking_number, form_data):
        """إرسال طلب البحث"""
        
        # محاولة طرق مختلفة للبحث
        search_methods = [
            self._try_ajax_search,
            self._try_get_search,
            self._try_post_search
        ]
        
        for method in search_methods:
            try:
                result = method(booking_number, form_data)
                if result:
                    return result
            except Exception as e:
                print(f"⚠️ فشل في {method.__name__}: {e}")
        
        return None
    
    def _try_ajax_search(self, booking_number, form_data):
        """محاولة AJAX search"""
        print("  🔍 محاولة AJAX search...")
        
        # تحديث headers للـ AJAX
        ajax_headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'Origin': self.base_url,
            'Referer': self.tracking_url
        }
        
        self.session.headers.update(ajax_headers)
        
        # بيانات البحث
        search_data = {
            "bookingNo": booking_number,
            "trackingType": "2"
        }
        
        # إضافة CSRF token إذا وجد
        if form_data.get('csrf_token'):
            search_data['_token'] = form_data['csrf_token']
        
        # محاولة endpoints مختلفة
        endpoints = form_data.get('api_endpoints', [])
        if not endpoints:
            endpoints = [
                '/ebusiness/cargoTracking/search',
                '/ebusiness/cargoTracking/api',
                '/ebusiness/cargoTracking/query'
            ]
        
        for endpoint in endpoints:
            try:
                url = urljoin(self.base_url, endpoint)
                print(f"    📤 POST إلى: {endpoint}")
                
                response = self.session.post(url, json=search_data, timeout=15)
                print(f"      Status: {response.status_code}")
                
                if response.status_code == 200:
                    return response.text
                    
            except Exception as e:
                print(f"      خطأ: {e}")
        
        return None
    
    def _try_get_search(self, booking_number, form_data):
        """محاولة GET search"""
        print("  🔍 محاولة GET search...")
        
        params = {
            'bookingNo': booking_number,
            'trackingType': '2'
        }
        
        try:
            response = self.session.get(self.tracking_url, params=params, timeout=15)
            print(f"    Status: {response.status_code}")
            
            if response.status_code == 200:
                return response.text
                
        except Exception as e:
            print(f"    خطأ: {e}")
        
        return None
    
    def _try_post_search(self, booking_number, form_data):
        """محاولة POST search"""
        print("  🔍 محاولة POST search...")
        
        # تحديث headers للـ form
        form_headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
        
        self.session.headers.update(form_headers)
        
        form_data_post = {
            'bookingNo': booking_number,
            'trackingType': '2'
        }
        
        # إضافة CSRF token إذا وجد
        if form_data.get('csrf_token'):
            form_data_post['_token'] = form_data['csrf_token']
        
        try:
            response = self.session.post(self.tracking_url, data=form_data_post, timeout=15)
            print(f"    Status: {response.status_code}")
            
            if response.status_code == 200:
                return response.text
                
        except Exception as e:
            print(f"    خطأ: {e}")
        
        return None
    
    def _parse_results(self, html, booking_number):
        """تحليل النتائج"""
        
        print("🔍 تحليل النتائج...")
        
        # حفظ النتائج للفحص
        with open(f'cosco_browser_result_{booking_number}.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print(f"💾 تم حفظ النتائج في cosco_browser_result_{booking_number}.html")
        
        # البحث عن JSON data
        json_data = self._extract_json_from_html(html)
        if json_data:
            print("✅ وجد JSON data في النتائج")
            return self._parse_json_data(json_data, booking_number)
        
        # البحث عن البيانات في HTML
        html_data = self._extract_data_from_html(html, booking_number)
        if html_data:
            print("✅ وجد بيانات في HTML")
            return html_data
        
        # فحص وجود رسائل خطأ
        error_indicators = ['no result', 'not found', 'invalid', 'error', 'لا توجد']
        for indicator in error_indicators:
            if indicator.lower() in html.lower():
                return self._error_result(f"الموقع أرجع: {indicator}")
        
        return self._error_result("لم يتم العثور على بيانات في النتائج")
    
    def _extract_json_from_html(self, html):
        """استخراج JSON من HTML"""
        patterns = [
            r'trackingData\s*[=:]\s*({[^;]+})',
            r'cargoData\s*[=:]\s*({[^;]+})',
            r'window\.__INITIAL_STATE__\s*=\s*({[^;]+})'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
            if matches:
                try:
                    return json.loads(matches[0])
                except:
                    continue
        
        return None
    
    def _extract_data_from_html(self, html, booking_number):
        """استخراج البيانات من HTML"""
        
        # البحث عن تواريخ
        dates = re.findall(r'\d{4}-\d{2}-\d{2}', html)
        
        # البحث عن B/L numbers
        bl_numbers = re.findall(r'B/L\s*No[:\s]*(\w+)', html, re.IGNORECASE)
        
        # البحث عن موانئ
        ports = re.findall(r'(Shantou|Aden|Shanghai|Hamburg)', html, re.IGNORECASE)
        
        # البحث عن حالة
        status_matches = re.findall(r'(vessel\s+departure|arrival|transit)', html, re.IGNORECASE)
        
        if dates or bl_numbers or ports:
            return {
                'success': True,
                'data': {
                    'booking_number': booking_number,
                    'dates_found': dates,
                    'bl_numbers': bl_numbers,
                    'ports': ports,
                    'status': status_matches,
                    'source': 'BROWSER_SIMULATION'
                },
                'message': 'تم استخراج بيانات من محاكاة المتصفح'
            }
        
        return None
    
    def _parse_json_data(self, data, booking_number):
        """تحليل JSON data"""
        # تحليل JSON وإرجاع البيانات المنظمة
        return {
            'success': True,
            'data': {
                'booking_number': booking_number,
                'json_data': data,
                'source': 'JSON_EXTRACTION'
            },
            'message': 'تم استخراج JSON data من الموقع'
        }
    
    def _error_result(self, message):
        """إرجاع نتيجة خطأ"""
        return {
            'success': False,
            'data': None,
            'message': message
        }

def main():
    """الاختبار الرئيسي"""
    
    print("=" * 80)
    print("🧪 محاكاة متصفح حقيقي لموقع COSCO")
    print("=" * 80)
    
    simulator = COSCOBrowserSimulator()
    booking_number = "6425375050"
    
    result = simulator.get_real_tracking_data(booking_number)
    
    print(f"\n" + "=" * 80)
    if result['success']:
        print("✅ نجحت محاكاة المتصفح!")
        print(f"📊 البيانات: {result['data']}")
        print(f"📝 الرسالة: {result['message']}")
    else:
        print("❌ فشلت محاكاة المتصفح")
        print(f"📝 الرسالة: {result['message']}")
    print("=" * 80)

if __name__ == "__main__":
    main()
