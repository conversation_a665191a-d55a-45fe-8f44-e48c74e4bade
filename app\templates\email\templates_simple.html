<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📝 قوالب البريد الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            direction: rtl;
            color: #ffffff;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .templates-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .no-templates {
            text-align: center;
            padding: 4rem 2rem;
        }
        
        .no-templates-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-file-alt"></i>
                قوالب البريد الإلكتروني
            </h1>
            <p style="color: #a0a0a0; font-size: 1.1rem;">
                إدارة قوالب الرسائل الجاهزة
            </p>
        </div>
        
        <div class="templates-container">
            {% if templates and templates|length > 0 %}
                <!-- Templates List -->
                <div class="row">
                    {% for template in templates %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card bg-dark text-white">
                            <div class="card-body">
                                <h5 class="card-title">{{ template.name }}</h5>
                                {% if template.subject %}
                                <p class="card-text">{{ template.subject }}</p>
                                {% endif %}
                                {% if template.body_text %}
                                <p class="card-text">{{ template.body_text[:100] }}...</p>
                                {% endif %}
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">
                                        {% if template.usage_count %}
                                            {{ template.usage_count }} استخدام
                                        {% endif %}
                                    </small>
                                    {% if template.is_public %}
                                        <span class="badge bg-warning">عام</span>
                                    {% else %}
                                        <span class="badge bg-primary">خاص</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- No Templates Message -->
                <div class="no-templates">
                    <div class="no-templates-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 style="color: #ffffff; margin-bottom: 1rem;">لا توجد قوالب</h3>
                    <p style="color: #a0a0a0;">
                        لم يتم إنشاء أي قوالب بعد.<br>
                        ابدأ بإنشاء قوالب لرسائلك المتكررة لتوفير الوقت.
                    </p>
                </div>
            {% endif %}
        </div>
        
        <!-- Back Button -->
        <div class="text-center">
            <a href="{{ url_for('email.inbox') }}" class="back-btn">
                <i class="fas fa-arrow-right"></i>
                العودة إلى صندوق الوارد
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
