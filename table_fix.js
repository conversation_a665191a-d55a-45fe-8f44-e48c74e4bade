
// حل مؤقت لمشكلة الجدول الفارغ
console.log('🔧 تطبيق الحل المؤقت...');

// التأكد من تحميل المكتبات
function waitForLibraries(callback) {
    let attempts = 0;
    const maxAttempts = 50;
    
    function check() {
        attempts++;
        
        if (typeof $ !== 'undefined' && typeof $.fn.DataTable !== 'undefined') {
            console.log('✅ المكتبات محملة');
            callback();
        } else if (attempts < maxAttempts) {
            console.log(`⏳ انتظار المكتبات... (${attempts}/${maxAttempts})`);
            setTimeout(check, 100);
        } else {
            console.error('❌ فشل في تحميل المكتبات');
            alert('خطأ: فشل في تحميل المكتبات المطلوبة');
        }
    }
    
    check();
}

// تهيئة الصفحة
function initializePage() {
    console.log('🚀 تهيئة الصفحة...');
    
    try {
        // تهيئة الجدول
        const table = $('#itemsTable').DataTable({
            destroy: true,
            processing: true,
            language: {
                processing: "جاري التحميل...",
                emptyTable: "لا توجد بيانات متاحة",
                zeroRecords: "لم يعثر على أية سجلات"
            },
            columns: [
                { data: 'item_code', title: 'كود الصنف' },
                { data: 'item_name', title: 'اسم الصنف' },
                { data: 'supplier_name', title: 'المورد' },
                { data: 'total_quantity', title: 'الكمية' },
                { data: 'avg_price', title: 'السعر' }
            ]
        });
        
        console.log('✅ تم تهيئة الجدول');
        
        // تحميل البيانات
        fetch('/purchase-orders/api/items/data')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    table.clear().rows.add(data.data).draw();
                    console.log(`✅ تم تحميل ${data.data.length} صنف`);
                } else {
                    console.warn('⚠️ لا توجد بيانات');
                }
            })
            .catch(error => {
                console.error('❌ خطأ في تحميل البيانات:', error);
            });
            
    } catch (error) {
        console.error('❌ خطأ في تهيئة الصفحة:', error);
    }
}

// بدء التهيئة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        waitForLibraries(initializePage);
    });
} else {
    waitForLibraries(initializePage);
}
