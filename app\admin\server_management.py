"""
إدارة الخادم والنشر
Server Management and Deployment
"""

import os
import json
import subprocess
import socket
from datetime import datetime
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required
import ssl
import requests

server_bp = Blueprint('server_management', __name__)

class ServerManager:
    def __init__(self):
        self.config_file = os.path.join('config', 'server_config.json')
        self.servers_file = os.path.join('config', 'servers_list.json')
        self.load_config()
        self.load_servers()

    def load_config(self):
        """تحميل إعدادات الخادم الحالي"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = self.get_default_config()
                self.save_config()
        except Exception as e:
            self.config = self.get_default_config()

    def load_servers(self):
        """تحميل قائمة الخوادم المحفوظة"""
        try:
            if os.path.exists(self.servers_file):
                with open(self.servers_file, 'r', encoding='utf-8') as f:
                    self.servers = json.load(f)
            else:
                self.servers = []
                self.save_servers()
        except Exception as e:
            self.servers = []

    def save_servers(self):
        """حفظ قائمة الخوادم"""
        try:
            os.makedirs(os.path.dirname(self.servers_file), exist_ok=True)
            with open(self.servers_file, 'w', encoding='utf-8') as f:
                json.dump(self.servers, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ قائمة الخوادم: {e}")

    def get_default_config(self):
        """الإعدادات الافتراضية"""
        return {
            'id': '',
            'name': '',
            'domain': '',
            'ssl_enabled': False,
            'ssl_cert_path': '',
            'ssl_key_path': '',
            'port': 5000,
            'host': '0.0.0.0',
            'environment': 'development',
            'auto_ssl': False,
            'nginx_enabled': False,
            'pm2_enabled': False,
            'backup_enabled': False,
            'monitoring_enabled': False,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
    
    def save_config(self):
        """حفظ إعدادات الخادم الحالي"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            self.config['updated_at'] = datetime.now().isoformat()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            return False

    def add_server(self, server_config):
        """إضافة خادم جديد"""
        import uuid
        server_config['id'] = str(uuid.uuid4())
        server_config['created_at'] = datetime.now().isoformat()
        server_config['updated_at'] = datetime.now().isoformat()

        self.servers.append(server_config)
        self.save_servers()
        return server_config['id']

    def update_server(self, server_id, server_config):
        """تحديث خادم موجود"""
        for i, server in enumerate(self.servers):
            if server['id'] == server_id:
                server_config['id'] = server_id
                server_config['created_at'] = server.get('created_at', datetime.now().isoformat())
                server_config['updated_at'] = datetime.now().isoformat()
                self.servers[i] = server_config
                self.save_servers()
                return True
        return False

    def delete_server(self, server_id):
        """حذف خادم"""
        self.servers = [s for s in self.servers if s['id'] != server_id]
        self.save_servers()
        return True

    def get_server(self, server_id):
        """الحصول على خادم محدد"""
        for server in self.servers:
            if server['id'] == server_id:
                return server
        return None

    def get_all_servers(self):
        """الحصول على جميع الخوادم"""
        return self.servers

    def load_server_config(self, server_id):
        """تحميل إعدادات خادم محدد كإعدادات حالية"""
        server = self.get_server(server_id)
        if server:
            self.config = server.copy()
            self.save_config()
            return True
        return False
    
    def check_domain_status(self, domain):
        """فحص حالة النطاق"""
        try:
            # فحص DNS
            ip = socket.gethostbyname(domain)
            
            # فحص HTTP
            try:
                response = requests.get(f'http://{domain}', timeout=5)
                http_status = response.status_code
            except:
                http_status = None
            
            # فحص HTTPS
            try:
                response = requests.get(f'https://{domain}', timeout=5)
                https_status = response.status_code
                ssl_valid = True
            except:
                https_status = None
                ssl_valid = False
            
            return {
                'ip': ip,
                'http_status': http_status,
                'https_status': https_status,
                'ssl_valid': ssl_valid
            }
        except Exception as e:
            return {'error': str(e)}
    
    def check_ssl_certificate(self, domain):
        """فحص شهادة SSL"""
        try:
            context = ssl.create_default_context()
            with socket.create_connection((domain, 443), timeout=5) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssock:
                    cert = ssock.getpeercert()
                    
                    # تحليل تاريخ انتهاء الصلاحية
                    not_after = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                    days_until_expiry = (not_after - datetime.now()).days
                    
                    return {
                        'valid': True,
                        'issuer': dict(x[0] for x in cert['issuer']),
                        'subject': dict(x[0] for x in cert['subject']),
                        'expires': cert['notAfter'],
                        'days_until_expiry': days_until_expiry
                    }
        except Exception as e:
            return {'valid': False, 'error': str(e)}
    
    def generate_nginx_config(self):
        """إنشاء إعدادات Nginx"""
        domain = self.config.get('domain', '')
        if not domain:
            return None
        
        config = f"""
server {{
    listen 80;
    server_name {domain} www.{domain};
    return 301 https://$server_name$request_uri;
}}

server {{
    listen 443 ssl http2;
    server_name {domain} www.{domain};

    ssl_certificate /etc/letsencrypt/live/{domain}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/{domain}/privkey.pem;
    
    # SSL Security Headers
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    location / {{
        proxy_pass http://127.0.0.1:{self.config.get('port', 5000)};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_redirect off;
    }}
    
    # Static files
    location /static {{
        alias /path/to/your/app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }}
}}
"""
        return config
    
    def generate_pm2_config(self):
        """إنشاء إعدادات PM2"""
        return {
            "apps": [{
                "name": "saserp",
                "script": "app.py",
                "cwd": "/path/to/your/app",
                "instances": "max",
                "exec_mode": "cluster",
                "env": {
                    "FLASK_ENV": "production",
                    "PORT": self.config.get('port', 5000)
                },
                "error_file": "/var/log/pm2/saserp-error.log",
                "out_file": "/var/log/pm2/saserp-out.log",
                "log_file": "/var/log/pm2/saserp.log",
                "time": True,
                "autorestart": True,
                "max_restarts": 10,
                "min_uptime": "10s"
            }]
        }

# إنشاء مثيل مدير الخادم
server_manager = ServerManager()

@server_bp.route('/server-management')
@login_required
def server_management():
    """صفحة إدارة الخادم"""
    return render_template('admin/server_management.html',
                         config=server_manager.config,
                         servers=server_manager.get_all_servers())

@server_bp.route('/api/server/status')
@login_required
def get_server_status():
    """الحصول على حالة الخادم"""
    domain = server_manager.config.get('domain')
    
    status = {
        'server_running': True,  # يمكن تحسينها
        'domain_configured': bool(domain),
        'ssl_enabled': server_manager.config.get('ssl_enabled', False),
        'nginx_running': False,  # يمكن فحصها
        'pm2_running': False     # يمكن فحصها
    }
    
    if domain:
        domain_status = server_manager.check_domain_status(domain)
        ssl_status = server_manager.check_ssl_certificate(domain)
        status.update({
            'domain_status': domain_status,
            'ssl_status': ssl_status
        })
    
    return jsonify(status)

@server_bp.route('/api/server/config', methods=['POST'])
@login_required
def update_server_config():
    """تحديث إعدادات الخادم"""
    try:
        data = request.get_json()
        
        # تحديث الإعدادات
        server_manager.config.update(data)
        
        if server_manager.save_config():
            return jsonify({'success': True, 'message': 'تم حفظ الإعدادات بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'فشل في حفظ الإعدادات'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@server_bp.route('/api/server/generate-configs')
@login_required
def generate_configs():
    """إنشاء ملفات الإعدادات"""
    try:
        nginx_config = server_manager.generate_nginx_config()
        pm2_config = server_manager.generate_pm2_config()

        return jsonify({
            'success': True,
            'nginx_config': nginx_config,
            'pm2_config': pm2_config
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@server_bp.route('/api/server/deploy')
@login_required
def deploy_app():
    """نشر التطبيق"""
    try:
        # محاكاة عملية النشر
        return jsonify({
            'success': True,
            'message': 'سيتم تطوير ميزة النشر التلقائي قريباً'
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@server_bp.route('/api/server/download-configs')
@login_required
def download_configs():
    """تحميل ملفات الإعدادات"""
    try:
        # محاكاة تحميل الإعدادات
        return jsonify({
            'success': True,
            'message': 'سيتم تطوير ميزة التحميل قريباً'
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@server_bp.route('/api/server/instructions')
@login_required
def show_instructions():
    """عرض تعليمات النشر"""
    try:
        instructions = """
        📚 تعليمات النشر السريع:

        1. إعداد الخادم:
           - Ubuntu 20.04+ أو CentOS 8+
           - 4GB RAM، 50GB SSD
           - عنوان IP ثابت

        2. إعداد النطاق:
           - شراء نطاق (مثل yourcompany.com)
           - إعداد DNS Records
           - A Record: yourcompany.com -> SERVER_IP

        3. تثبيت المتطلبات:
           sudo apt update && sudo apt upgrade -y
           sudo apt install -y python3 python3-pip nginx

        4. نشر التطبيق:
           - استخدم نص النشر التلقائي
           - أو اتبع الدليل المفصل

        5. إعداد SSL:
           sudo certbot --nginx -d yourcompany.com

        للمزيد من التفاصيل، راجع ملف DEPLOYMENT_GUIDE.md
        """

        return jsonify({
            'success': True,
            'instructions': instructions
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@server_bp.route('/api/server/upload-ssl', methods=['POST'])
@login_required
def upload_ssl_files():
    """رفع ملفات SSL إلى الخادم"""
    try:
        from werkzeug.utils import secure_filename

        # التحقق من وجود الملفات
        if 'cert_file' not in request.files or 'key_file' not in request.files:
            return jsonify({
                'success': False,
                'message': 'يرجى اختيار ملفي الشهادة والمفتاح'
            })

        cert_file = request.files['cert_file']
        key_file = request.files['key_file']

        if cert_file.filename == '' or key_file.filename == '':
            return jsonify({
                'success': False,
                'message': 'يرجى اختيار ملفات صالحة'
            })

        # التحقق من امتدادات الملفات
        cert_extensions = ['.crt', '.pem', '.cer']
        key_extensions = ['.key', '.pem']

        cert_ext = os.path.splitext(cert_file.filename)[1].lower()
        key_ext = os.path.splitext(key_file.filename)[1].lower()

        if cert_ext not in cert_extensions:
            return jsonify({
                'success': False,
                'message': f'امتداد ملف الشهادة غير صالح. الامتدادات المدعومة: {", ".join(cert_extensions)}'
            })

        if key_ext not in key_extensions:
            return jsonify({
                'success': False,
                'message': f'امتداد ملف المفتاح غير صالح. الامتدادات المدعومة: {", ".join(key_extensions)}'
            })

        # إنشاء مجلدات SSL إذا لم تكن موجودة
        ssl_cert_dir = '/etc/ssl/certs'
        ssl_key_dir = '/etc/ssl/private'

        # في بيئة التطوير، استخدم مجلدات محلية
        if not os.path.exists('/etc/ssl'):
            ssl_cert_dir = os.path.join('ssl', 'certs')
            ssl_key_dir = os.path.join('ssl', 'private')
            os.makedirs(ssl_cert_dir, exist_ok=True)
            os.makedirs(ssl_key_dir, exist_ok=True)

        # حفظ الملفات
        cert_filename = secure_filename(cert_file.filename)
        key_filename = secure_filename(key_file.filename)

        cert_path = os.path.join(ssl_cert_dir, cert_filename)
        key_path = os.path.join(ssl_key_dir, key_filename)

        cert_file.save(cert_path)
        key_file.save(key_path)

        # تحديث الصلاحيات (في بيئة الإنتاج)
        try:
            os.chmod(cert_path, 0o644)  # قراءة للجميع
            os.chmod(key_path, 0o600)   # قراءة للمالك فقط
        except:
            pass  # تجاهل أخطاء الصلاحيات في بيئة التطوير

        return jsonify({
            'success': True,
            'message': 'تم رفع ملفات SSL بنجاح',
            'cert_path': cert_path,
            'key_path': key_path
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في رفع ملفات SSL: {str(e)}'
        })

@server_bp.route('/api/server/generate-domain-ssl', methods=['POST'])
@login_required
def generate_domain_ssl():
    """إنشاء شهادة SSL للنطاق المحدد"""
    try:
        data = request.get_json()
        domain = data.get('domain', '').replace('https://', '').replace('http://', '')

        if not domain:
            return jsonify({
                'success': False,
                'message': 'يرجى تحديد النطاق'
            })

        # إنشاء مجلدات SSL
        ssl_cert_dir = os.path.join('ssl', 'certs')
        ssl_key_dir = os.path.join('ssl', 'private')
        os.makedirs(ssl_cert_dir, exist_ok=True)
        os.makedirs(ssl_key_dir, exist_ok=True)

        # مسارات الملفات
        cert_path = os.path.join(ssl_cert_dir, f'{domain}.crt')
        key_path = os.path.join(ssl_key_dir, f'{domain}.key')

        # إنشاء شهادة SSL باستخدام OpenSSL
        try:
            # إنشاء المفتاح الخاص
            key_cmd = f'openssl genrsa -out "{key_path}" 2048'
            subprocess.run(key_cmd, shell=True, check=True, capture_output=True)

            # إنشاء طلب الشهادة
            csr_path = os.path.join(ssl_cert_dir, f'{domain}.csr')
            csr_cmd = f'openssl req -new -key "{key_path}" -out "{csr_path}" -subj "/C=SA/ST=Riyadh/L=Riyadh/O=SAS ERP/CN={domain}"'
            subprocess.run(csr_cmd, shell=True, check=True, capture_output=True)

            # إنشاء الشهادة الموقعة ذاتياً
            cert_cmd = f'openssl x509 -req -days 365 -in "{csr_path}" -signkey "{key_path}" -out "{cert_path}" -extensions v3_req -extfile <(echo "[v3_req]"; echo "subjectAltName=DNS:{domain},DNS:*.{domain}")'
            subprocess.run(cert_cmd, shell=True, check=True, capture_output=True, executable='/bin/bash')

            # حذف ملف CSR
            if os.path.exists(csr_path):
                os.remove(csr_path)

            return jsonify({
                'success': True,
                'message': f'تم إنشاء شهادة SSL للنطاق {domain} بنجاح',
                'cert_path': cert_path,
                'key_path': key_path,
                'domain': domain
            })

        except subprocess.CalledProcessError as e:
            # إذا فشل OpenSSL، استخدم Python cryptography
            return generate_python_ssl_cert(domain, cert_path, key_path)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إنشاء شهادة SSL: {str(e)}'
        })

def generate_python_ssl_cert(domain, cert_path, key_path):
    """إنشاء شهادة SSL باستخدام Python cryptography"""
    try:
        from cryptography import x509
        from cryptography.x509.oid import NameOID, ExtendedKeyUsageOID
        from cryptography.hazmat.primitives import hashes, serialization
        from cryptography.hazmat.primitives.asymmetric import rsa
        import datetime

        # إنشاء المفتاح الخاص
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )

        # إنشاء الشهادة
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "SA"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Riyadh"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Riyadh"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "SAS ERP"),
            x509.NameAttribute(NameOID.COMMON_NAME, domain),
        ])

        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.datetime.utcnow()
        ).not_valid_after(
            datetime.datetime.utcnow() + datetime.timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName(domain),
                x509.DNSName(f"*.{domain}"),
            ]),
            critical=False,
        ).add_extension(
            x509.ExtendedKeyUsage([
                ExtendedKeyUsageOID.SERVER_AUTH,
            ]),
            critical=True,
        ).sign(private_key, hashes.SHA256())

        # حفظ المفتاح الخاص
        with open(key_path, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))

        # حفظ الشهادة
        with open(cert_path, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))

        return jsonify({
            'success': True,
            'message': f'تم إنشاء شهادة SSL للنطاق {domain} بنجاح (Python)',
            'cert_path': cert_path,
            'key_path': key_path,
            'domain': domain
        })

    except ImportError:
        return jsonify({
            'success': False,
            'message': 'مكتبة cryptography غير مثبتة. يرجى تثبيتها: pip install cryptography'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إنشاء شهادة SSL: {str(e)}'
        })

@server_bp.route('/api/server/restart', methods=['POST'])
@login_required
def restart_server():
    """إعادة تشغيل الخادم مع الإعدادات الجديدة"""
    try:
        print('🔄 طلب إعادة تشغيل الخادم...')

        # إعادة تشغيل الخادم بعد تأخير قصير
        import threading
        import time
        import sys

        def restart_after_delay():
            time.sleep(3)
            os.execv(sys.executable, ['python'] + sys.argv)

        thread = threading.Thread(target=restart_after_delay)
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'message': 'سيتم إعادة تشغيل الخادم...'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إعادة تشغيل الخادم: {str(e)}'
        })

# ===== إدارة الخوادم المتعددة =====

@server_bp.route('/api/servers', methods=['GET'])
@login_required
def get_servers():
    """الحصول على قائمة الخوادم"""
    try:
        print("🔄 طلب الحصول على قائمة الخوادم...")
        servers = server_manager.get_all_servers()
        print(f"📋 تم العثور على {len(servers)} خادم")
        return jsonify({
            'success': True,
            'servers': servers
        })
    except Exception as e:
        print(f"❌ خطأ في الحصول على قائمة الخوادم: {e}")
        return jsonify({'success': False, 'message': str(e)})

@server_bp.route('/api/servers', methods=['POST'])
@login_required
def add_server():
    """إضافة خادم جديد"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('name') or not data.get('domain'):
            return jsonify({
                'success': False,
                'message': 'اسم الخادم والنطاق مطلوبان'
            })

        # إضافة الخادم
        server_id = server_manager.add_server(data)

        return jsonify({
            'success': True,
            'message': 'تم إضافة الخادم بنجاح',
            'server_id': server_id
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@server_bp.route('/api/servers/<server_id>', methods=['PUT'])
@login_required
def update_server_by_id(server_id):
    """تحديث خادم موجود"""
    try:
        data = request.get_json()

        if server_manager.update_server(server_id, data):
            return jsonify({
                'success': True,
                'message': 'تم تحديث الخادم بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'الخادم غير موجود'
            })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@server_bp.route('/api/servers/<server_id>', methods=['DELETE'])
@login_required
def delete_server_by_id(server_id):
    """حذف خادم"""
    try:
        if server_manager.delete_server(server_id):
            return jsonify({
                'success': True,
                'message': 'تم حذف الخادم بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في حذف الخادم'
            })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@server_bp.route('/api/servers/<server_id>/load', methods=['POST'])
@login_required
def load_server_config_by_id(server_id):
    """تحميل إعدادات خادم محدد"""
    try:
        if server_manager.load_server_config(server_id):
            return jsonify({
                'success': True,
                'message': 'تم تحميل إعدادات الخادم بنجاح',
                'config': server_manager.config
            })
        else:
            return jsonify({
                'success': False,
                'message': 'الخادم غير موجود'
            })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})
