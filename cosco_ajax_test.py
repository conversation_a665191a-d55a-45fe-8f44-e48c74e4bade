#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محاولة محاكاة AJAX requests لـ COSCO
"""

import requests
import urllib3
import json
import time

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_cosco_ajax(booking_number):
    """محاولة محاكاة AJAX requests"""
    
    print(f"🔍 محاولة AJAX لرقم الحجز: {booking_number}")
    print("=" * 60)
    
    session = requests.Session()
    session.verify = False
    
    # الخطوة 1: زيارة الصفحة الرئيسية للحصول على cookies
    print("📡 الخطوة 1: زيارة الصفحة الرئيسية...")
    main_url = "https://elines.coscoshipping.com/ebusiness/cargoTracking"
    
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })
    
    try:
        response = session.get(main_url, timeout=20)
        print(f"✅ Status: {response.status_code}")
        print(f"🍪 Cookies: {len(session.cookies)} cookies")
        
        # الخطوة 2: محاولة AJAX requests مختلفة
        print("\n📡 الخطوة 2: محاولة AJAX requests...")
        
        # تحديث headers للـ AJAX
        session.headers.update({
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'Origin': 'https://elines.coscoshipping.com',
            'Referer': main_url
        })
        
        # الـ endpoints الصحيحة بناءً على الموقع الفعلي
        ajax_endpoints = [
            "/ebusiness/cargoTracking/search",
            "/ebusiness/cargoTracking/query",
            "/ebusiness/cargoTracking/api",
            "/ebusiness/cargoTracking/getTrackingInfo",
            "/ebusiness/cargoTracking/cargoTrackingDetail",
            "/ebusiness/cargoTracking/trackingResult",
            "/ebusiness/cargoTracking/getData"
        ]
        
        # بيانات البحث
        search_data = {
            "bookingNo": booking_number,
            "trackingType": "2"
        }
        
        for endpoint in ajax_endpoints:
            try:
                url = f"https://elines.coscoshipping.com{endpoint}"
                print(f"  🔗 اختبار: {endpoint}")
                
                # محاولة POST
                response = session.post(url, json=search_data, timeout=15)
                print(f"    POST Status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"    ✅ JSON Response: {str(data)[:200]}...")
                        
                        # تحليل البيانات
                        if analyze_ajax_response(data, booking_number):
                            return True
                            
                    except:
                        print(f"    📄 Text Response: {response.text[:200]}...")
                
                elif response.status_code not in [404, 405]:
                    print(f"    ⚠️ غير متوقع: {response.status_code}")
                    print(f"    📄 Response: {response.text[:100]}...")
                
            except Exception as e:
                print(f"    ❌ خطأ: {e}")
        
        # الخطوة 3: محاولة GET مع parameters مختلفة
        print("\n📡 الخطوة 3: محاولة GET parameters...")
        
        get_params_list = [
            {"bookingNo": booking_number, "trackingType": "2"},
            {"blNo": booking_number},
            {"containerNo": booking_number},
            {"trackingNumber": booking_number},
            {"number": booking_number, "type": "booking"}
        ]
        
        for params in get_params_list:
            try:
                print(f"  🔍 GET params: {params}")
                response = session.get(main_url, params=params, timeout=15)
                
                print(f"    Status: {response.status_code}")
                print(f"    URL: {response.url}")
                
                if response.status_code == 200:
                    # البحث عن JSON في response
                    if analyze_html_for_data(response.text, booking_number):
                        return True
                
            except Exception as e:
                print(f"    ❌ خطأ: {e}")
        
        # الخطوة 4: محاولة form submission
        print("\n📡 الخطوة 4: محاولة form submission...")
        
        session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        })
        
        form_data = {
            'bookingNo': booking_number,
            'trackingType': '2'
        }
        
        try:
            response = session.post(main_url, data=form_data, timeout=15)
            print(f"  Form POST Status: {response.status_code}")
            
            if response.status_code == 200:
                if analyze_html_for_data(response.text, booking_number):
                    return True
        
        except Exception as e:
            print(f"  ❌ خطأ في form: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def analyze_ajax_response(data, booking_number):
    """تحليل استجابة AJAX"""
    try:
        if isinstance(data, dict):
            # البحث عن مؤشرات النجاح
            if data.get('success') or data.get('status') == 'success':
                print(f"    ✅ استجابة ناجحة!")
                
                # البحث عن البيانات
                tracking_data = data.get('data') or data.get('result') or data.get('trackingInfo')
                
                if tracking_data:
                    print(f"    📊 بيانات التتبع: {tracking_data}")
                    return True
            
            # البحث عن تواريخ مباشرة
            etd = data.get('etd') or data.get('ETD') or data.get('departureDate')
            eta = data.get('eta') or data.get('ETA') or data.get('arrivalDate')
            
            if etd and eta:
                print(f"    📅 وجد تواريخ: ETD={etd}, ETA={eta}")
                return True
        
        return False
        
    except Exception as e:
        print(f"    ❌ خطأ في تحليل AJAX: {e}")
        return False

def analyze_html_for_data(html, booking_number):
    """تحليل HTML للبحث عن البيانات"""
    try:
        # البحث عن JSON مدفون
        import re
        
        json_patterns = [
            r'trackingData\s*[=:]\s*({[^;]+})',
            r'cargoData\s*[=:]\s*({[^;]+})',
            r'window\.__INITIAL_STATE__\s*=\s*({[^;]+})',
            r'"trackingResult"\s*:\s*({[^}]+})'
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
            if matches:
                try:
                    data = json.loads(matches[0])
                    print(f"    ✅ وجد JSON: {str(data)[:100]}...")
                    return analyze_ajax_response(data, booking_number)
                except:
                    continue
        
        # البحث عن تواريخ
        dates = re.findall(r'\d{4}-\d{2}-\d{2}', html)
        if len(dates) >= 2:
            print(f"    📅 وجد تواريخ في HTML: {dates}")
            return True
        
        # البحث عن مؤشرات البيانات
        indicators = ['B/L', 'POL', 'POD', 'vessel', 'container']
        found = [ind for ind in indicators if ind.lower() in html.lower()]
        
        if len(found) >= 3:
            print(f"    🔍 وجد مؤشرات: {found}")
            return True
        
        return False
        
    except Exception as e:
        print(f"    ❌ خطأ في تحليل HTML: {e}")
        return False

def main():
    """الاختبار الرئيسي"""
    
    print("=" * 80)
    print("🧪 اختبار AJAX لـ COSCO")
    print("=" * 80)
    
    booking_number = "6425375050"
    
    success = test_cosco_ajax(booking_number)
    
    print(f"\n" + "=" * 80)
    print(f"📊 النتيجة النهائية: {'✅ نجح' if success else '❌ فشل'}")
    print("=" * 80)

if __name__ == "__main__":
    main()
