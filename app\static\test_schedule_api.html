<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API جلب الجدولة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>🧪 اختبار API جلب جدولة الشحن</h3>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="mb-3">
                                <label class="form-label">شركة الشحن:</label>
                                <select class="form-select" id="shippingCompany">
                                    <option value="MAEU">Maersk (MAEU)</option>
                                    <option value="MSCU">MSC (MSCU)</option>
                                    <option value="CMAU">CMA CGM (CMAU)</option>
                                    <option value="COSU">COSCO (COSU)</option>
                                    <option value="HLCU">Hapag-Lloyd (HLCU)</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الرقم المرجعي:</label>
                                <input type="text" class="form-control" id="referenceNumber" 
                                       placeholder="مثال: MSCU1234567" value="MSCU1234567">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">نوع الرقم:</label>
                                <select class="form-select" id="referenceType">
                                    <option value="container">رقم الحاوية</option>
                                    <option value="booking">رقم الحجز</option>
                                    <option value="bol">رقم البوليصة</option>
                                </select>
                            </div>
                            
                            <button type="button" class="btn btn-primary" onclick="testAPI()">
                                <i class="fas fa-test"></i> اختبار API
                            </button>
                        </form>
                        
                        <hr>
                        
                        <div id="result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testAPI() {
            const shippingCompany = document.getElementById('shippingCompany').value;
            const referenceNumber = document.getElementById('referenceNumber').value;
            const referenceType = document.getElementById('referenceType').value;
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="alert alert-info">جاري الاختبار...</div>';
            
            fetch('/shipments/api/fetch-shipping-schedule', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    shipping_company: shippingCompany,
                    reference_number: referenceNumber,
                    reference_type: referenceType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h5>✅ نجح الاختبار!</h5>
                            <p><strong>الرسالة:</strong> ${data.message}</p>
                            <hr>
                            <h6>📅 بيانات الجدولة:</h6>
                            <ul>
                                <li><strong>ETD:</strong> ${data.schedule.etd || 'غير محدد'}</li>
                                <li><strong>ETA:</strong> ${data.schedule.eta || 'غير محدد'}</li>
                                <li><strong>السفينة:</strong> ${data.schedule.vessel_name || 'غير محدد'}</li>
                                <li><strong>رقم الرحلة:</strong> ${data.schedule.voyage_number || 'غير محدد'}</li>
                                <li><strong>ميناء التحميل:</strong> ${data.schedule.port_of_loading || 'غير محدد'}</li>
                                <li><strong>ميناء التفريغ:</strong> ${data.schedule.port_of_discharge || 'غير محدد'}</li>
                                <li><strong>مدة الشحن:</strong> ${data.schedule.transit_time_days || 'غير محدد'} يوم</li>
                                <li><strong>خط الشحن:</strong> ${data.schedule.service_name || 'غير محدد'}</li>
                                <li><strong>المصدر:</strong> ${data.schedule.source || 'تقديري'}</li>
                            </ul>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>❌ فشل الاختبار</h5>
                            <p><strong>الرسالة:</strong> ${data.message}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>❌ خطأ في الاتصال</h5>
                        <p><strong>الخطأ:</strong> ${error.message}</p>
                    </div>
                `;
            });
        }
    </script>
</body>
</html>
