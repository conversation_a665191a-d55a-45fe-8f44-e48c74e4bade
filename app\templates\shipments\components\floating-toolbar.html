<!-- زر إخفاء/إظهار الشريط العائم -->
<button class="toolbar-toggle" id="toolbarToggle" onclick="toggleFloatingToolbar()" title="إخفاء/إظهار الأدوات">
    <i class="fas fa-chevron-left"></i>
</button>

<!-- شريط الأدوات العائم -->
<div class="floating-toolbar" id="floatingToolbar">
    <button class="tool-btn" onclick="refreshDashboardData()" data-tooltip="تحديث البيانات">
        <i class="fas fa-sync-alt"></i>
    </button>

    <button class="tool-btn" onclick="exportShipmentsData()" data-tooltip="تصدير البيانات">
        <i class="fas fa-download"></i>
    </button>

    <button class="tool-btn" onclick="showAdvancedFilters()" data-tooltip="المرشحات المتقدمة">
        <i class="fas fa-filter"></i>
    </button>

    <button class="tool-btn" onclick="showQuickSearch()" data-tooltip="البحث السريع">
        <i class="fas fa-search"></i>
    </button>

    <button class="tool-btn" onclick="showNotifications()" data-tooltip="الإشعارات">
        <i class="fas fa-bell"></i>
        <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
    </button>
    
    <button class="tool-btn" onclick="toggleFloatingToolbar()" title="إخفاء/إظهار الشريط">
        <i class="fas fa-chevron-right"></i>
    </button>
</div>

<style>
/* تحسينات إضافية لشريط الأدوات */
.floating-toolbar .tool-btn {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.floating-toolbar.minimized {
    transform: translateY(-50%) translateX(50px);
}

.floating-toolbar.minimized .tool-btn:not(:last-child) {
    display: none;
}

.floating-toolbar.minimized .tool-btn:last-child i {
    transform: rotate(180deg);
}
</style>
