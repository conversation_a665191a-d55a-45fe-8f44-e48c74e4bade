{% extends "base.html" %}

{% block content %}
<style>
.cargo-form-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.cargo-form-section h5 {
    color: #007bff;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.container-type-card {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.container-type-card:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.container-type-card.selected {
    border-color: #007bff;
    background: #e3f2fd;
}

.port-selector {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
}

.cost-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
}

.step-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.step {
    flex: 1;
    text-align: center;
    position: relative;
}

.step.active .step-number {
    background: #007bff;
    color: white;
}

.step.completed .step-number {
    background: #28a745;
    color: white;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    font-weight: bold;
}

.step-title {
    font-size: 0.9rem;
    color: #6c757d;
}

.step.active .step-title {
    color: #007bff;
    font-weight: 600;
}
</style>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        {% if shipment_data %}
                            <i class="fas fa-edit text-warning me-2"></i>
                            تعديل الشحنة - {{ shipment_data.tracking_number }}
                        {% else %}
                            <i class="fas fa-ship text-primary me-2"></i>
                            حجز شحنة حاويات جديدة
                        {% endif %}
                    </h1>
                    <p class="text-muted mb-0">
                        {% if shipment_data %}
                            تعديل بيانات الشحنة الموجودة
                        {% else %}
                            إنشاء حجز جديد لشحن البضائع والحاويات
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('shipments.dashboard_fullscreen') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Step Indicator -->
    <div class="step-indicator">
        <div class="step active">
            <div class="step-number">1</div>
            <div class="step-title">معلومات الشحنة</div>
        </div>
        <div class="step">
            <div class="step-number">2</div>
            <div class="step-title">تفاصيل البضاعة</div>
        </div>
        <div class="step">
            <div class="step-number">3</div>
            <div class="step-title">الحاويات</div>
        </div>
        <div class="step">
            <div class="step-number">4</div>
            <div class="step-title">المراجعة والتأكيد</div>
        </div>
    </div>

    <form id="cargoShipmentForm" method="POST" action="{% if shipment_data %}{{ url_for('shipments.create_cargo_shipment', shipment_id=shipment_data.id) }}{% else %}{{ url_for('shipments.create_cargo_shipment') }}{% endif %}">

        <!-- حقول مخفية للتعديل -->
        {% if shipment_data %}
            <input type="hidden" name="is_edit" value="true">
            <input type="hidden" name="shipment_id" value="{{ shipment_data.id }}">
        {% endif %}

        <div class="row">
            <div class="col-12">



                <!-- 🚢 قسم الحاويات -->
                <div class="cargo-form-section">
                    <h5>
                        <i class="fas fa-shipping-fast me-2"></i>
                        الحاويات المطلوبة
                    </h5>

                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">
                                    <i class="fas fa-boxes me-2"></i>
                                    تفاصيل الحاويات
                                </h6>
                                <button type="button" class="btn btn-outline-success btn-sm" id="addContainerBtn">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة حاوية
                                </button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered" id="containersTable">
                                    <thead class="table-success">
                                        <tr>
                                            <th style="width: 15%">رقم الحاوية</th>
                                            <th style="width: 12%">النوع</th>
                                            <th style="width: 10%">الحجم</th>
                                            <th style="width: 12%">رقم الختم</th>
                                            <th style="width: 10%">الوزن الإجمالي</th>
                                            <th style="width: 10%">الوزن الصافي</th>
                                            <th style="width: 8%">مبرد</th>
                                            <th style="width: 15%">ملاحظات</th>
                                            <th style="width: 8%">إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="containersBody">
                                        <!-- سيتم إضافة الحاويات هنا تلقائياً -->
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-success">
                                            <td colspan="4" class="text-end"><strong>إجمالي الحاويات:</strong></td>
                                            <td><strong id="totalContainers">0</strong></td>
                                            <td colspan="4"></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <!-- حقل مخفي لحفظ بيانات الحاويات -->
                            <input type="hidden" name="containers" id="containersJson">
                        </div>
                    </div>
                </div>

                <!-- 📋 قسم بيانات الشحن الموحد -->
                <div class="cargo-form-section">
                    <h5>
                        <i class="fas fa-ship me-2"></i>
                        بيانات الشحن والمسار
                        <small class="text-muted ms-2">(معلومات شاملة للشحنة)</small>
                    </h5>

                    <!-- المعلومات الأساسية -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label class="form-label">
                                <i class="fas fa-ship text-primary me-1"></i>
                                رقم الحجز *
                            </label>
                            <input type="text" class="form-control" name="booking_number"
                                   placeholder="مثال: MSKU123456789"
                                   pattern="[A-Za-z0-9]+"
                                   title="رقم الحجز يجب أن يحتوي على أحرف وأرقام فقط"
                                   value="{% if shipment_data %}{{ shipment_data.booking_number or '' }}{% endif %}"
                                   required>
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                رقم الحجز المستلم من شركة الشحن (Booking Number)
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع الشحن *</label>
                            <select class="form-select" name="shipping_type" required>
                                <option value="">اختر نوع الشحن</option>
                                <option value="FCL" {% if shipment_data and shipment_data.shipping_type == 'FCL' %}selected{% endif %}>حاوية كاملة (FCL)</option>
                                <option value="LCL" {% if shipment_data and shipment_data.shipping_type == 'LCL' %}selected{% endif %}>حاوية مشتركة (LCL)</option>
                                <option value="BULK" {% if shipment_data and shipment_data.shipping_type == 'BULK' %}selected{% endif %}>بضائع سائبة</option>
                                <option value="BREAKBULK" {% if shipment_data and shipment_data.shipping_type == 'BREAKBULK' %}selected{% endif %}>بضائع عامة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">
                                خط الشحن *
                                <span class="badge bg-info ms-1">🤖 ذكي</span>
                            </label>
                            <select class="form-select" name="shipping_line_id" id="shippingLineSelect" required>
                                <option value="">جاري تحميل شركات الشحن...</option>
                            </select>
                            <div class="form-text">
                                <small class="text-muted">
                                    <i class="fas fa-robot me-1"></i>
                                    الشركات المميزة بـ 🤖 تدعم التتبع الذكي
                                </small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">أولوية الشحن</label>
                            <select class="form-select" name="priority">
                                <option value="">اختر الأولوية</option>
                                <option value="عادي" {{ 'selected' if shipment_data.priority == 'عادي' else '' }}>عادي</option>
                                <option value="عاجل" {{ 'selected' if shipment_data.priority == 'عاجل' else '' }}>عاجل</option>
                                <option value="طارئ" {{ 'selected' if shipment_data.priority == 'طارئ' else '' }}>طارئ</option>
                            </select>
                        </div>
                    </div>

                    <!-- الموانئ والمسار مع النظام الذكي -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="port-selector">
                                <label class="form-label">ميناء الشحن *</label>
                                <div class="input-group">
                                    <select class="form-select" name="origin_port_id" id="originPortSelect" required>
                                        <option value="">اختر ميناء الشحن</option>
                                        <!-- سيتم ملؤها من النظام الذكي -->
                                    </select>
                                    <button class="btn btn-primary" type="button" onclick="openSmartPortSearch('origin')" id="originPortBtn">
                                        <i class="fas fa-search"></i>
                                        بحث ذكي
                                    </button>
                                </div>
                                <small class="text-muted">
                                    💡 استخدم البحث الذكي لاختيار الموانئ أو اختر "تحميل الموانئ المفضلة"
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="port-selector">
                                <label class="form-label">ميناء الوصول *</label>
                                <div class="input-group">
                                    <select class="form-select" name="destination_port_id" id="destinationPortSelect" required>
                                        <option value="">اختر ميناء الوصول</option>
                                        {% if ports %}
                                            {% for port in ports %}
                                            <option value="{{ port.id }}"
                                                    {{ 'selected' if shipment_data and shipment_data.destination_port_id == port.id else '' }}>
                                                {{ port.port_name }}
                                            </option>
                                            {% endfor %}
                                        {% endif %}
                                        <!-- سيتم ملؤها من النظام الذكي -->
                                    </select>
                                    <button class="btn btn-primary" type="button" onclick="openSmartPortSearch('destination')" id="destinationPortBtn">
                                        <i class="fas fa-search"></i>
                                        بحث ذكي
                                    </button>
                                </div>
                                <small class="text-muted">
                                    💡 استخدم البحث الذكي لاختيار الموانئ أو اختر "تحميل الموانئ المفضلة"
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- التواريخ -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">
                                تاريخ المغادرة المتوقع (ETD)
                            </label>
                            <input type="datetime-local" class="form-control" name="etd" id="etd-input"
                                   value="{% if shipment_data and shipment_data.etd %}{{ shipment_data.etd }}T00:00{% endif %}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">تاريخ الوصول المتوقع (ETA)</label>
                            <input type="datetime-local" class="form-control" name="eta" id="eta-input"
                                   value="{% if shipment_data and shipment_data.eta %}{{ shipment_data.eta }}T00:00{% endif %}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">مدة الشحن (أيام)</label>
                            <input type="number" class="form-control" name="estimated_transit_time" id="transit-time-input"
                                   placeholder="7" min="1" readonly>
                        </div>
                    </div>

                    <!-- تم إزالة حالة جلب الجدولة -->

                    <!-- الوثائق الرئيسية -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">رقم البوليصة (B/L) *</label>
                            <input type="text" class="form-control" name="bill_of_lading_number"
                                   value="{{ shipment_data.bill_of_lading_number if shipment_data else '' }}"
                                   placeholder="MSKU123456789" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">
                                رقم التتبع
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2"
                                        id="fetch-tracking-btn" title="جلب رقم التتبع تلقائياً"
                                        {{ 'style="display:none"' if shipment_data else '' }}>
                                    <i class="fas fa-sync-alt"></i> جلب تلقائي
                                </button>
                                <span class="badge bg-success ms-1" title="نظام جلب رقم التتبع يعمل بكفاءة">
                                    <i class="fas fa-check-circle"></i> فعال
                                </span>
                            </label>
                            <input type="text" class="form-control" name="tracking_number" id="tracking_number"
                                   value="{{ shipment_data.tracking_number if shipment_data else '' }}"
                                   placeholder="TRK123456789"
                                   {{ 'readonly' if shipment_data else '' }}>
                            <div id="tracking-fetch-status" class="mt-1"></div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">رقم ختم الحاوية</label>
                            <input type="text" class="form-control" name="container_seal_number"
                                   value="{{ shipment_data.container_seal_number or '' }}"
                                   placeholder="SL123456" readonly>
                            <small class="text-muted">
                                <i class="fas fa-magic me-1"></i>
                                يتم تعبئته تلقائياً من بيانات الحاويات
                            </small>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">اسم السفينة</label>
                            <input type="text" class="form-control" name="vessel_name"
                                   value="{{ shipment_data.vessel_name or '' }}"
                                   placeholder="MSC OSCAR">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">رقم الرحلة</label>
                            <input type="text" class="form-control" name="voyage_number"
                                   value="{{ shipment_data.voyage_number or '' }}"
                                   placeholder="VOY001">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">وكيل الشحن</label>
                            <input type="text" class="form-control" name="freight_forwarder"
                                   value="{{ shipment_data.freight_forwarder or '' }}"
                                   placeholder="شركة الشحن الدولية">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label class="form-label">شروط التسليم (Incoterms)</label>
                            <select class="form-select" name="incoterms">
                                <option value="">اختر شروط التسليم</option>
                                <option value="FOB" {{ 'selected' if shipment_data.incoterms == 'FOB' else '' }}>FOB - Free On Board</option>
                                <option value="CIF" {{ 'selected' if shipment_data.incoterms == 'CIF' else '' }}>CIF - Cost, Insurance & Freight</option>
                                <option value="CFR" {{ 'selected' if shipment_data.incoterms == 'CFR' else '' }}>CFR - Cost & Freight</option>
                                <option value="EXW" {{ 'selected' if shipment_data.incoterms == 'EXW' else '' }}>EXW - Ex Works</option>
                                <option value="DDP" {{ 'selected' if shipment_data.incoterms == 'DDP' else '' }}>DDP - Delivered Duty Paid</option>
                                <option value="DDU" {{ 'selected' if shipment_data.incoterms == 'DDU' else '' }}>DDU - Delivered Duty Unpaid</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">ميناء التحميل</label>
                            <input type="text" class="form-control" name="port_of_loading"
                                   value="{{ shipment_data.port_of_loading or '' }}"
                                   placeholder="ميناء جدة الإسلامي">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">ميناء التفريغ</label>
                            <input type="text" class="form-control" name="port_of_discharge"
                                   value="{{ shipment_data.port_of_discharge or '' }}"
                                   placeholder="ميناء دبي">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">مدة الشحن (أيام)</label>
                            <input type="number" class="form-control" name="estimated_transit_time"
                                   placeholder="7" min="1">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">رقم البيان الجمركي</label>
                            <input type="text" class="form-control" name="customs_declaration_number"
                                   value="{{ shipment_data.customs_declaration_number or '' }}"
                                   placeholder="CUS123456789">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">رقم بوليصة التأمين</label>
                            <input type="text" class="form-control" name="insurance_policy_number"
                                   value="{{ shipment_data.insurance_policy_number or '' }}"
                                   placeholder="INS123456789">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-12">
                            <label class="form-label">تعليمات الشحن الخاصة</label>
                            <textarea class="form-control" name="shipping_instructions" rows="3"
                                      placeholder="أي تعليمات خاصة للشحن أو التعامل مع البضاعة..."></textarea>
                        </div>
                    </div>

                    <!-- الحقول المفقودة المهمة -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">شركة الشحن</label>
                            <input type="text" class="form-control" name="shipping_company"
                                   value="{{ shipment_data.shipping_company or '' }}"
                                   placeholder="COSCO Shipping">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">رقم الحاوية الرئيسية</label>
                            <input type="text" class="form-control" name="container_number"
                                   value="{{ shipment_data.container_number or '' }}"
                                   placeholder="GCXU6413257">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">رابط التتبع</label>
                            <input type="url" class="form-control" name="tracking_url"
                                   value="{{ shipment_data.tracking_url or '' }}"
                                   placeholder="https://tracking.cosco.com">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">ملاحظات التتبع</label>
                            <textarea class="form-control" name="tracking_notes" rows="2"
                                      placeholder="ملاحظات حول حالة التتبع...">{{ shipment_data.tracking_notes or '' }}</textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">ملاحظات الإفراج</label>
                            <textarea class="form-control" name="release_notes" rows="2"
                                      placeholder="ملاحظات حول عملية الإفراج...">{{ shipment_data.release_notes or '' }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- معلومات الموردين -->
                <div class="cargo-form-section">
                    <h5>
                        <i class="fas fa-building me-2"></i>
                        معلومات الموردين
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الشاحن (Shipper) *</label>

                            <!-- Dropdown الأصلي -->
                            <select class="form-select" name="shipper_id" id="shipperSelect" required>
                                <option value="">اختر الشاحن</option>
                                {% if suppliers %}
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}"
                                            {{ 'selected' if shipment_data and shipment_data.shipper_id == supplier.id else '' }}>
                                        {{ supplier.supplier_name }}
                                    </option>
                                    {% endfor %}
                                {% else %}
                                    <option value="" disabled>لا توجد موردين متاحين</option>
                                {% endif %}
                            </select>

                            <!-- البحث التفاعلي المتقدم -->
                            <div class="mt-2">
                                <div class="input-group input-group-sm">
                                    <input type="text" class="form-control" id="shipperSearch"
                                           placeholder="🔍 بحث متقدم في الموردين..." autocomplete="off">
                                    <button type="button" class="btn btn-outline-primary" onclick="clearSearch('shipper')">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div id="shipperResults" style="display: none; position: absolute; background: white; border: 1px solid #007bff; width: 95%; max-height: 150px; overflow-y: auto; z-index: 1000; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,123,255,0.15); margin-top: 1px; font-size: 13px;"></div>
                            </div>

                            <small class="text-muted">عدد الموردين: {{ suppliers|length if suppliers else 0 }} | 🔍 استخدم البحث المتقدم للعثور على موردين إضافيين</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المستلم (Consignee) *</label>

                            <!-- Dropdown الأصلي -->
                            <select class="form-select" name="consignee_id" id="consigneeSelect" required>
                                <option value="">اختر المستلم</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}"
                                        {{ 'selected' if shipment_data and shipment_data.consignee_id == supplier.id else '' }}>
                                    {{ supplier.supplier_name }}
                                </option>
                                {% endfor %}
                            </select>

                            <!-- البحث التفاعلي المتقدم -->
                            <div class="mt-2">
                                <div class="input-group input-group-sm">
                                    <input type="text" class="form-control" id="consigneeSearch"
                                           placeholder="🔍 بحث متقدم في الموردين..." autocomplete="off">
                                    <button type="button" class="btn btn-outline-success" onclick="clearSearch('consignee')">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div id="consigneeResults" style="display: none; position: absolute; background: white; border: 1px solid #28a745; width: 95%; max-height: 150px; overflow-y: auto; z-index: 1000; border-radius: 4px; box-shadow: 0 2px 8px rgba(40,167,69,0.15); margin-top: 1px; font-size: 13px;"></div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">جهة الإشعار (Notify Party)</label>
                            <select class="form-select" name="notify_party_id">
                                <option value="">اختر جهة الإشعار</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}">{{ supplier.supplier_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- 🪄 السحر: ربط أمر الشراء -->
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">
                                <i class="fas fa-shopping-cart me-2"></i>
                                أمر الشراء المرتبط:
                            </label>
                            <select class="form-select" name="purchase_order_id" id="purchaseOrderSelect">
                                <option value="">اختر أمر الشراء (اختياري)</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                عند اختيار أمر الشراء، سيتم تحميل تفاصيل البضاعة تلقائياً
                            </small>
                        </div>
                    </div>
                </div>



                <!-- تفاصيل البضاعة -->
                <div class="cargo-form-section">
                    <h5>
                        <i class="fas fa-boxes me-2"></i>
                        تفاصيل البضاعة
                    </h5>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label">وصف البضاعة *</label>
                            <textarea class="form-control" name="cargo_description" rows="3" placeholder="وصف مفصل للبضاعة المراد شحنها" required>{{ shipment_data.cargo_description if shipment_data else '' }}</textarea>
                        </div>
                    </div>

                    <!-- 🪄 جدول تفاصيل البضاعة السحري -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    تفاصيل البضاعة
                                    <small class="text-muted ms-2">(يتم تحميلها تلقائياً من أمر الشراء)</small>
                                </h6>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered" id="cargoItemsTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 10%">كود الصنف</th>
                                            <th style="width: 18%">اسم الصنف</th>
                                            <th style="width: 6%">الوحدة</th>
                                            <th style="width: 6%">الكمية</th>
                                            <th style="width: 8%">سعر الوحدة</th>
                                            <th style="width: 8%">الإجمالي</th>
                                            <th style="width: 10%">الحاوية</th>
                                            <th style="width: 12%">المستلم</th>
                                            <th style="width: 7%">تاريخ الإنتاج</th>
                                            <th style="width: 7%">تاريخ الانتهاء</th>
                                            <th style="width: 4%">إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="cargoItemsBody">
                                        <tr id="noItemsMessage">
                                            <td colspan="11" class="text-center text-muted py-4">
                                                <i class="fas fa-info-circle me-2"></i>
                                                اختر أمر الشراء أولاً لتحميل تفاصيل البضاعة تلقائياً
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <td colspan="5" class="text-end"><strong>الإجمالي الكلي:</strong></td>
                                            <td><strong id="totalAmount">0.00</strong></td>
                                            <td colspan="5"></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <!-- حقل مخفي لحفظ بيانات JSON -->
                            <input type="hidden" name="cargo_items" id="cargoItemsJson">
                        </div>
                    </div>



                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">نوع البضاعة</label>
                            <select class="form-select" name="cargo_type">
                                <option value="">اختر النوع</option>
                                <option value="عام" {{ 'selected' if shipment_data.cargo_type == 'عام' else '' }}>بضائع عامة</option>
                                <option value="خطرة" {{ 'selected' if shipment_data.cargo_type == 'خطرة' else '' }}>مواد خطرة</option>
                                <option value="مبردة" {{ 'selected' if shipment_data.cargo_type == 'مبردة' else '' }}>بضائع مبردة</option>
                                <option value="سائلة" {{ 'selected' if shipment_data.cargo_type == 'سائلة' else '' }}>مواد سائلة</option>
                                <option value="معدات" {{ 'selected' if shipment_data.cargo_type == 'معدات' else '' }}>معدات ثقيلة</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-weight-hanging me-1"></i>الوزن الإجمالي (كجم)
                                <span class="badge bg-info ms-1" title="يتم حسابه تلقائياً من جدول الحاويات">
                                    <i class="fas fa-calculator"></i> تلقائي
                                </span>
                            </label>
                            <input type="number" class="form-control bg-light" name="total_weight" step="0.01" placeholder="0.00" readonly>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                يتم حسابه تلقائياً من إجمالي الوزن الإجمالي للحاويات
                            </small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-balance-scale me-1"></i>الوزن الصافي (كجم)
                                <span class="badge bg-success ms-1" title="يتم حسابه تلقائياً من جدول الحاويات">
                                    <i class="fas fa-calculator"></i> تلقائي
                                </span>
                            </label>
                            <input type="number" class="form-control bg-light" name="net_weight" step="0.01" placeholder="0.00" readonly>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                يتم حسابه تلقائياً من إجمالي الوزن الصافي للحاويات
                            </small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-cube me-1"></i>الحجم الإجمالي (م³)
                            </label>
                            <input type="number" class="form-control" name="total_volume" step="0.01"
                                   value="{{ shipment_data.total_volume or '' }}"
                                   placeholder="0.00">
                            <small class="text-muted">الحجم الكلي للبضاعة</small>
                        </div>
                    </div>

                    <!-- أزرار التحكم في الوزن -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="toggleWeightMode">
                                    <i class="fas fa-edit me-1"></i>
                                    تحرير الأوزان يدوياً
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="updateTotalWeightsFromContainers()">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    إعادة حساب من الحاويات
                                </button>
                            </div>
                            <small class="text-muted d-block mt-1">
                                <i class="fas fa-lightbulb me-1"></i>
                                يمكنك تحرير الأوزان يدوياً أو تركها تُحسب تلقائياً من جدول الحاويات
                            </small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">عدد الطرود</label>
                            <input type="number" class="form-control" name="total_packages"
                                   value="{{ shipment_data.total_packages if shipment_data else '' }}"
                                   placeholder="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع التعبئة</label>
                            <select class="form-select" name="package_type">
                                <option value="">اختر نوع التعبئة</option>
                                <option value="صناديق" {{ 'selected' if shipment_data.package_type == 'صناديق' else '' }}>صناديق</option>
                                <option value="أكياس" {{ 'selected' if shipment_data.package_type == 'أكياس' else '' }}>أكياس</option>
                                <option value="براميل" {{ 'selected' if shipment_data.package_type == 'براميل' else '' }}>براميل</option>
                                <option value="منصات" {{ 'selected' if shipment_data.package_type == 'منصات' else '' }}>منصات خشبية</option>
                                <option value="أخرى" {{ 'selected' if shipment_data.package_type == 'أخرى' else '' }}>أخرى</option>
                            </select>
                        </div>
                    </div>

                    <!-- حالة الشحنة والتواريخ -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">حالة الشحنة</label>
                            <select class="form-select" name="shipment_status">
                                <option value="draft" {{ 'selected' if shipment_data.shipment_status == 'draft' else '' }}>📝 مسودة</option>
                                <option value="confirmed" {{ 'selected' if shipment_data.shipment_status == 'confirmed' else '' }}>✅ مؤكدة</option>
                                <option value="in_transit" {{ 'selected' if shipment_data.shipment_status == 'in_transit' else '' }}>🚢 قيد الشحن</option>
                                <option value="arrived_port" {{ 'selected' if shipment_data.shipment_status == 'arrived_port' else '' }}>🏭 وصلت للميناء</option>
                                <option value="customs_clearance" {{ 'selected' if shipment_data.shipment_status == 'customs_clearance' else '' }}>🚚 قيد التخليص</option>
                                <option value="ready_pickup" {{ 'selected' if shipment_data.shipment_status == 'ready_pickup' else '' }}>📦 جاهزة للاستلام</option>
                                <option value="delivered" {{ 'selected' if shipment_data.shipment_status == 'delivered' else '' }}>✅ تم التسليم</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ التسليم المتوقع</label>
                            <input type="date" class="form-control" name="estimated_delivery_date"
                                   value="{{ shipment_data.estimated_delivery_date or '' }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ التسليم الفعلي</label>
                            <input type="date" class="form-control" name="actual_delivery_date"
                                   value="{{ shipment_data.actual_delivery_date or '' }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_dangerous" id="is_dangerous"
                                       {{ 'checked' if shipment_data.is_dangerous else '' }}>
                                <label class="form-check-label" for="is_dangerous">
                                    مواد خطرة (Dangerous Goods)
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="temperature_controlled" id="temperature_controlled"
                                       {{ 'checked' if shipment_data.temperature_controlled else '' }}>
                                <label class="form-check-label" for="temperature_controlled">
                                    يتطلب تحكم في درجة الحرارة
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="requires_release" id="requires_release"
                                       {{ 'checked' if shipment_data.requires_release else '' }}>
                                <label class="form-check-label" for="requires_release">
                                    يتطلب إفراج جمركي
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">حالة الإفراج</label>
                            <select class="form-select" name="release_status">
                                <option value="">اختر حالة الإفراج</option>
                                <option value="pending" {{ 'selected' if shipment_data.release_status == 'pending' else '' }}>في الانتظار</option>
                                <option value="in_progress" {{ 'selected' if shipment_data.release_status == 'in_progress' else '' }}>قيد المعالجة</option>
                                <option value="released" {{ 'selected' if shipment_data.release_status == 'released' else '' }}>تم الإفراج</option>
                                <option value="held" {{ 'selected' if shipment_data.release_status == 'held' else '' }}>محتجز</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- تعليمات خاصة -->
                <div class="cargo-form-section">
                    <h5>
                        <i class="fas fa-clipboard-list me-2"></i>
                        تعليمات خاصة
                    </h5>
                    
                    <div class="mb-3">
                        <label class="form-label">تعليمات خاصة للشحن</label>
                        <textarea class="form-control" name="special_instructions" rows="3" placeholder="أي تعليمات خاصة للتعامل مع البضاعة"></textarea>
                    </div>
                </div>
            </div>


                
                <!-- 📄 قسم إرسال المستندات -->
                <div class="cargo-form-section">
                    <h5>
                        <i class="fas fa-file-export me-2"></i>
                        إرسال المستندات
                        <small class="text-muted ms-2">(اختياري - يمكن إضافته لاحقاً)</small>
                    </h5>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">شركة الشحن السريع</label>
                            <select class="form-select" name="courier_company">
                                <option value="">اختر شركة الشحن</option>
                                <option value="DHL" {{ 'selected' if shipment_data.courier_company == 'DHL' else '' }}>DHL Express</option>
                                <option value="FedEx" {{ 'selected' if shipment_data.courier_company == 'FedEx' else '' }}>FedEx</option>
                                <option value="UPS" {{ 'selected' if shipment_data.courier_company == 'UPS' else '' }}>UPS</option>
                                <option value="أرامكس" {{ 'selected' if shipment_data.courier_company == 'أرامكس' else '' }}>أرامكس</option>
                                <option value="سمسا" {{ 'selected' if shipment_data.courier_company == 'سمسا' else '' }}>سمسا إكسبريس</option>
                                <option value="البريد السعودي" {{ 'selected' if shipment_data.courier_company == 'البريد السعودي' else '' }}>البريد السعودي</option>
                                <option value="أخرى" {{ 'selected' if shipment_data.courier_company == 'أخرى' else '' }}>أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">رقم تتبع المستندات</label>
                            <input type="text" class="form-control" name="documents_tracking_number"
                                   value="{{ shipment_data.documents_tracking_number or '' }}"
                                   placeholder="1234567890">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">تاريخ الإرسال</label>
                            <input type="date" class="form-control" name="documents_sent_date"
                                   value="{{ shipment_data.documents_sent_date or '' }}">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">اسم المستلم</label>
                            <input type="text" class="form-control" name="documents_recipient_name"
                                   value="{{ shipment_data.documents_recipient_name or '' }}"
                                   placeholder="اسم الشخص أو الشركة المستلمة">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">حالة التسليم</label>
                            <select class="form-select" name="documents_delivery_status">
                                <option value="لم يتم الإرسال" {{ 'selected' if shipment_data.documents_delivery_status == 'لم يتم الإرسال' else '' }}>لم يتم الإرسال</option>
                                <option value="تم الإرسال" {{ 'selected' if shipment_data.documents_delivery_status == 'تم الإرسال' else '' }}>تم الإرسال</option>
                                <option value="في الطريق" {{ 'selected' if shipment_data.documents_delivery_status == 'في الطريق' else '' }}>في الطريق</option>
                                <option value="تم التسليم" {{ 'selected' if shipment_data.documents_delivery_status == 'تم التسليم' else '' }}>تم التسليم</option>
                                <option value="فشل التسليم" {{ 'selected' if shipment_data.documents_delivery_status == 'فشل التسليم' else '' }}>فشل التسليم</option>
                                <option value="إعادة المحاولة" {{ 'selected' if shipment_data.documents_delivery_status == 'إعادة المحاولة' else '' }}>إعادة المحاولة</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-12">
                            <label class="form-label">عنوان التسليم</label>
                            <textarea class="form-control" name="documents_recipient_address" rows="2"
                                      placeholder="العنوان الكامل لتسليم المستندات">{{ shipment_data.documents_recipient_address or '' }}</textarea>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-12">
                            <label class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control" name="documents_notes" rows="2"
                                      placeholder="أي ملاحظات خاصة بإرسال المستندات...">{{ shipment_data.documents_notes or '' }}</textarea>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>المستندات المطلوبة عادة:</strong>
                        البوليصة الأصلية (B/L)، الفاتورة التجارية، قائمة التعبئة، شهادة المنشأ، وثائق التأمين
                    </div>
                </div>

                <!-- معلومات مهمة -->
                <div class="cargo-form-section">
                    <h5>
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات مهمة
                    </h5>
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    يجب تقديم جميع المستندات المطلوبة
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    التأكد من صحة بيانات البضاعة
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    مراجعة شروط وأحكام الشحن
                                </li>
                                <li>
                                    <i class="fas fa-check text-success me-2"></i>
                                    التأكد من التأمين على البضاعة
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ملخص التكاليف في الأسفل -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-gradient-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            ملخص التكاليف والإجراءات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- ملخص التكاليف -->
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-light rounded">
                                            <h6 class="text-muted mb-1">تكلفة الشحن الأساسية</h6>
                                            <h4 class="mb-0 text-primary" id="baseCost">$0.00</h4>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-light rounded">
                                            <h6 class="text-muted mb-1">رسوم الميناء</h6>
                                            <h4 class="mb-0 text-info" id="portCharges">$0.00</h4>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-light rounded">
                                            <h6 class="text-muted mb-1">رسوم التأمين</h6>
                                            <h4 class="mb-0 text-warning" id="insuranceCharges">$0.00</h4>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-success text-white rounded">
                                            <h6 class="mb-1">إجمالي التكلفة</h6>
                                            <h4 class="mb-0" id="totalCost">$0.00</h4>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <label class="form-label">العملة</label>
                                        <select class="form-select" name="currency">
                                            <option value="">اختر العملة</option>
                                            <option value="USD" {{ 'selected' if shipment_data.currency == 'USD' else '' }}>دولار أمريكي (USD)</option>
                                            <option value="SAR" {{ 'selected' if shipment_data.currency == 'SAR' else '' }}>ريال سعودي (SAR)</option>
                                            <option value="EUR" {{ 'selected' if shipment_data.currency == 'EUR' else '' }}>يورو (EUR)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">تكلفة الشحن</label>
                                        <input type="number" class="form-control" name="freight_cost" step="0.01"
                                               value="{{ shipment_data.freight_cost or '' }}"
                                               placeholder="0.00" onchange="updateCostSummary()">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">رسوم أخرى</label>
                                        <input type="number" class="form-control" name="other_charges" step="0.01"
                                               value="{{ shipment_data.other_charges or '' }}"
                                               placeholder="0.00" onchange="updateCostSummary()">
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الإجراءات -->
                            <div class="col-lg-4">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        {% if shipment_data %}
                                            <i class="fas fa-save me-2"></i>
                                            حفظ التعديل
                                        {% else %}
                                            <i class="fas fa-check me-2"></i>
                                            تأكيد الحجز
                                        {% endif %}
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="saveDraft()">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ كمسودة
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="previewShipment()">
                                        <i class="fas fa-eye me-2"></i>
                                        معاينة الشحنة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- تضمين نافذة البحث الذكية للموانئ -->
{% include 'shipments/smart_port_selector.html' %}

<script>
// حساب التكاليف تلقائياً
function calculateCosts() {
    try {
        const weightEl = document.querySelector('[name="total_weight"]');
        const volumeEl = document.querySelector('[name="total_volume"]');
        const shippingTypeEl = document.querySelector('[name="shipping_type"]');

        const weight = weightEl ? parseFloat(weightEl.value) || 0 : 0;
        const volume = volumeEl ? parseFloat(volumeEl.value) || 0 : 0;
        const shippingType = shippingTypeEl ? shippingTypeEl.value : '';
    
    let baseCost = 0;
    let portCharges = 100;
    let insuranceCharges = 50;
    let additionalCharges = 0;
    
    // حساب التكلفة الأساسية حسب النوع
    if (shippingType === 'FCL') {
        baseCost = 2000; // تكلفة ثابتة للحاوية الكاملة
    } else if (shippingType === 'LCL') {
        baseCost = weight * 2 + volume * 100; // حسب الوزن والحجم
    } else {
        baseCost = weight * 1.5;
    }
    
    // رسوم إضافية للمواد الخطرة
    if (document.querySelector('[name="is_dangerous"]').checked) {
        additionalCharges += 200;
    }
    
    // رسوم إضافية للتحكم في درجة الحرارة
    if (document.querySelector('[name="temperature_controlled"]').checked) {
        additionalCharges += 300;
    }
    
    const totalCost = baseCost + portCharges + insuranceCharges + additionalCharges;
    
    // تحديث العرض (مع فحص وجود العناصر)
    const baseCostEl = document.getElementById('baseCost');
    const portChargesEl = document.getElementById('portCharges');
    const insuranceChargesEl = document.getElementById('insuranceCharges');
    const additionalChargesEl = document.getElementById('additionalCharges');
    const totalCostEl = document.getElementById('totalCost');

    if (baseCostEl) baseCostEl.textContent = `$${baseCost.toFixed(2)}`;
    if (portChargesEl) portChargesEl.textContent = `$${portCharges.toFixed(2)}`;
    if (insuranceChargesEl) insuranceChargesEl.textContent = `$${insuranceCharges.toFixed(2)}`;
    if (additionalChargesEl) additionalChargesEl.textContent = `$${additionalCharges.toFixed(2)}`;
    if (totalCostEl) totalCostEl.textContent = `$${totalCost.toFixed(2)}`;

    } catch (error) {
        console.warn('⚠️ خطأ في حساب التكاليف:', error);
    }
}

// ربط الأحداث
document.addEventListener('DOMContentLoaded', function() {
    // حساب التكاليف عند تغيير القيم
    const costInputs = ['total_weight', 'net_weight', 'total_volume', 'shipping_type', 'is_dangerous', 'temperature_controlled'];
    costInputs.forEach(name => {
        const element = document.querySelector(`[name="${name}"]`);
        if (element) {
            element.addEventListener('change', calculateCosts);
            element.addEventListener('input', calculateCosts);
        }
    });

    // إضافة validation للوزن
    const totalWeightInput = document.querySelector('[name="total_weight"]');
    const netWeightInput = document.querySelector('[name="net_weight"]');

    function validateWeights() {
        const totalWeight = parseFloat(totalWeightInput.value) || 0;
        const netWeight = parseFloat(netWeightInput.value) || 0;

        if (netWeight > totalWeight && totalWeight > 0) {
            netWeightInput.setCustomValidity('الوزن الصافي يجب أن يكون أقل من أو يساوي الوزن الإجمالي');
            netWeightInput.classList.add('is-invalid');
        } else {
            netWeightInput.setCustomValidity('');
            netWeightInput.classList.remove('is-invalid');
        }
    }

    if (totalWeightInput && netWeightInput) {
        totalWeightInput.addEventListener('input', validateWeights);
        netWeightInput.addEventListener('input', validateWeights);
    }
    
    // حساب أولي
    calculateCosts();
});

// تحميل موانئ الشحن
function loadOriginPorts() {
    fetch('/shipments/api/favorite-ports?purpose=origin')
        .then(response => response.json())
        .then(data => {
            const originSelect = document.getElementById('originPortSelect');
            if (data.success && data.ports && originSelect) {
                originSelect.innerHTML = '<option value="">اختر ميناء الشحن</option>';

                data.ports.forEach(port => {
                    const displayName = port.port_name_arabic ?
                        `${port.port_name_arabic} (${port.port_name}) - ${port.country_arabic || port.country}` :
                        `${port.port_name} - ${port.country}`;

                    const option = document.createElement('option');
                    option.value = port.id;
                    option.textContent = displayName;
                    option.dataset.portCode = port.port_code;

                    // تحديد القيمة المختارة في وضع التعديل
                    {% if shipment_data and shipment_data.origin_port_id %}
                    if (port.id == {{ shipment_data.origin_port_id }}) {
                        option.selected = true;
                    }
                    {% endif %}

                    originSelect.appendChild(option);
                });

                console.log(`✅ تم تحميل ${data.ports.length} ميناء شحن`);
            }
        })
        .catch(error => console.error('❌ خطأ في تحميل موانئ الشحن:', error));
}

// تحميل موانئ الوصول مع معالجة الأخطاء
function loadDestinationPorts(retryCount = 0) {
    fetch('/shipments/api/favorite-ports?purpose=destination')
        .then(response => {
            if (response.status === 429) {
                throw new Error('TOO_MANY_REQUESTS');
            }
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            const destinationSelect = document.getElementById('destinationPortSelect');
            if (data.success && data.ports && destinationSelect) {
                destinationSelect.innerHTML = '<option value="">اختر ميناء الوصول</option>';

                data.ports.forEach(port => {
                    const displayName = port.port_name_arabic ?
                        `${port.port_name_arabic} (${port.port_name}) - ${port.country_arabic || port.country}` :
                        `${port.port_name} - ${port.country}`;

                    const option = document.createElement('option');
                    option.value = port.id;
                    option.textContent = displayName;
                    option.dataset.portCode = port.port_code;

                    // تحديد القيمة المختارة في وضع التعديل
                    {% if shipment_data and shipment_data.destination_port_id %}
                    if (port.id == {{ shipment_data.destination_port_id }}) {
                        option.selected = true;
                    }
                    {% endif %}

                    destinationSelect.appendChild(option);
                });

                console.log(`✅ تم تحميل ${data.ports.length} ميناء وصول`);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل موانئ الوصول:', error);

            // إعادة المحاولة في حالة 429
            if (error.message === 'TOO_MANY_REQUESTS' && retryCount < 3) {
                console.log(`🔄 إعادة المحاولة ${retryCount + 1}/3 بعد ${(retryCount + 1) * 2} ثانية...`);
                setTimeout(() => {
                    loadDestinationPorts(retryCount + 1);
                }, (retryCount + 1) * 2000);
            }
        });
}

// ==================== 🤖 تحميل شركات الشحن الذكية ====================

function loadSmartShippingLines() {
    console.log('🚀 تحميل شركات الشحن من النظام الذكي...');

    fetch('/shipments/api/shipping-lines-for-booking')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                smartShippingLines = data.shipping_lines;
                populateShippingLinesSelect(data.shipping_lines);

                console.log(`✅ تم تحميل ${data.total} شركة شحن`);
                console.log(`📊 الإحصائيات:`);
                console.log(`  - من قاعدة البيانات: ${data.database_lines}`);
                console.log(`  - من النظام الذكي: ${data.smart_lines}`);
                console.log(`  - تدعم التتبع الذكي: ${data.smart_enabled_count}`);
            } else {
                console.error('❌ فشل في تحميل شركات الشحن:', data.message);
                showFallbackShippingLines();
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل شركات الشحن:', error);
            showFallbackShippingLines();
        });
}

function populateShippingLinesSelect(shippingLines) {
    const select = document.getElementById('shippingLineSelect');

    // مسح الخيارات الحالية
    select.innerHTML = '<option value="">اختر شركة الشحن</option>';

    // إضافة مجموعة للشركات الذكية
    const smartGroup = document.createElement('optgroup');
    smartGroup.label = '🤖 شركات الشحن الذكية (تدعم التتبع التلقائي)';

    // إضافة مجموعة للشركات العادية
    const regularGroup = document.createElement('optgroup');
    regularGroup.label = '📦 شركات الشحن العادية';

    let hasSmartCompanies = false;
    let hasRegularCompanies = false;

    shippingLines.forEach(line => {
        const option = document.createElement('option');
        option.value = line.id;
        option.textContent = line.display_name;

        // إضافة معلومات إضافية كـ data attributes
        option.setAttribute('data-smart-code', line.smart_company_code || '');
        option.setAttribute('data-has-tracking', line.has_smart_tracking);
        option.setAttribute('data-auto-tracking', line.auto_tracking_enabled);
        option.setAttribute('data-containers-count', line.containers_count);
        option.setAttribute('data-smart-code', line.smart_company_code || '');

        // تحديد القيمة المختارة في وضع التعديل
        {% if shipment_data and shipment_data.shipping_line_id %}
        if (line.id == {{ shipment_data.shipping_line_id }}) {
            option.selected = true;
        }
        {% endif %}

        // تلوين الخيارات حسب النوع
        if (line.has_smart_tracking) {
            option.style.background = '#e3f2fd';
            option.style.fontWeight = '600';
            smartGroup.appendChild(option);
            hasSmartCompanies = true;
        } else {
            regularGroup.appendChild(option);
            hasRegularCompanies = true;
        }
    });

    // إضافة المجموعات للقائمة
    if (hasSmartCompanies) {
        select.appendChild(smartGroup);
    }

    if (hasRegularCompanies) {
        select.appendChild(regularGroup);
    }

    // إضافة مستمع للتغيير
    select.addEventListener('change', onShippingLineChange);
}

function onShippingLineChange(event) {
    const selectedOption = event.target.selectedOptions[0];

    if (selectedOption && selectedOption.value) {
        const lineData = {
            id: selectedOption.value,
            name: selectedOption.textContent,
            smartCode: selectedOption.getAttribute('data-smart-code'),
            hasTracking: selectedOption.getAttribute('data-has-tracking') === 'true',
            autoTracking: selectedOption.getAttribute('data-auto-tracking') === 'true',
            containersCount: parseInt(selectedOption.getAttribute('data-containers-count')) || 0
        };

        console.log('📦 تم اختيار شركة الشحن:', lineData);

        // إظهار معلومات الشركة
        showShippingLineInfo(lineData);

        // حفظ الاختيار
        selectedShippingLines = [lineData];
    }
}

function showShippingLineInfo(lineData) {
    // إزالة أي معلومات سابقة
    const existingInfo = document.querySelector('.shipping-line-info');
    if (existingInfo) {
        existingInfo.remove();
    }

    // إنشاء عنصر المعلومات
    const infoDiv = document.createElement('div');
    infoDiv.className = 'shipping-line-info mt-2 p-2 rounded';

    if (lineData.hasTracking) {
        infoDiv.style.background = '#d4edda';
        infoDiv.style.border = '1px solid #c3e6cb';
        infoDiv.innerHTML = `
            <small class="text-success">
                <i class="fas fa-check-circle me-1"></i>
                <strong>شركة ذكية:</strong> تدعم التتبع التلقائي والكشف الذكي للحاويات
                ${lineData.containersCount > 0 ? `<br><i class="fas fa-cube me-1"></i>تم شحن ${lineData.containersCount} حاوية سابقاً` : ''}
            </small>
        `;
    } else {
        infoDiv.style.background = '#fff3cd';
        infoDiv.style.border = '1px solid #ffeaa7';
        infoDiv.innerHTML = `
            <small class="text-warning">
                <i class="fas fa-info-circle me-1"></i>
                <strong>شركة عادية:</strong> لا تدعم التتبع التلقائي حالياً
                ${lineData.containersCount > 0 ? `<br><i class="fas fa-cube me-1"></i>تم شحن ${lineData.containersCount} حاوية سابقاً` : ''}
            </small>
        `;
    }

    // إضافة المعلومات بعد القائمة المنسدلة
    const selectContainer = document.getElementById('shippingLineSelect').parentNode;
    selectContainer.appendChild(infoDiv);
}

function showFallbackShippingLines() {
    console.log('🔄 استخدام شركات الشحن الاحتياطية...');

    const fallbackLines = [
        {
            id: 'FALLBACK_MSC',
            line_code: 'MSC',
            line_name: 'MSC Mediterranean',
            smart_company_code: 'MSCU',
            has_smart_tracking: true,
            display_name: 'MSC Mediterranean (MSCU) 🤖',
            auto_tracking_enabled: true,
            containers_count: 0
        },
        {
            id: 'FALLBACK_MAERSK',
            line_code: 'MAERSK',
            line_name: 'Maersk Line',
            smart_company_code: 'MAEU',
            has_smart_tracking: true,
            display_name: 'Maersk Line (MAEU) 🤖',
            auto_tracking_enabled: true,
            containers_count: 0
        },
        {
            id: 'FALLBACK_COSCO',
            line_code: 'COSCO',
            line_name: 'COSCO Shipping',
            smart_company_code: 'COSU',
            has_smart_tracking: true,
            display_name: 'COSCO Shipping (COSU) 🤖',
            auto_tracking_enabled: true,
            containers_count: 0
        }
    ];

    populateShippingLinesSelect(fallbackLines);
}

// دالة لتحديث الميناء المختار من النظام الذكي
function updateSelectedPort(portId, portCode, portName, country, city, type) {
    console.log('🔄 تحديث الميناء المختار:', { portId, portCode, portName, country, city, type });

    const selectElement = type === 'origin' ?
        document.getElementById('originPortSelect') :
        document.getElementById('destinationPortSelect');

    if (!selectElement) {
        console.error('❌ لم يتم العثور على عنصر الاختيار:', type);
        return;
    }

    // البحث عن الخيار الموجود بالكود أولاً، ثم بالمعرف
    let option = selectElement.querySelector(`option[data-port-code="${portCode}"]`) ||
                 selectElement.querySelector(`option[value="${portId}"]`);

    if (!option) {
        // إنشاء خيار جديد إذا لم يكن موجوداً
        console.log('➕ إنشاء خيار جديد للميناء:', portCode);
        option = document.createElement('option');
        option.value = portId || portCode;
        option.textContent = `${portName} - ${country} ${city ? '(' + city + ')' : ''}`;
        option.dataset.portCode = portCode;
        option.dataset.portName = portName;
        option.dataset.country = country;
        option.dataset.city = city || '';

        // إضافة الخيار في المقدمة (بعد الخيار الافتراضي)
        if (selectElement.children.length > 1) {
            selectElement.insertBefore(option, selectElement.children[1]);
        } else {
            selectElement.appendChild(option);
        }

        // تمييز الخيار الجديد
        option.style.backgroundColor = '#e3f2fd';
        option.style.fontWeight = 'bold';
        option.title = 'تم إضافته من النظام الذكي';
    }

    // اختيار الميناء
    selectElement.value = option.value;

    // التأكد من أن الاختيار تم بنجاح
    if (selectElement.value === option.value) {
        console.log('✅ تم اختيار الميناء بنجاح:', option.value, option.textContent);

        // إظهار رسالة نجاح
        const typeText = type === 'origin' ? 'الشحن' : 'الوصول';
        showNotification(`✅ تم اختيار ${portName} كميناء ${typeText}`, 'success');

        // تمييز الخيار المختار
        option.style.backgroundColor = '#e3f2fd';
        option.style.fontWeight = 'bold';

        // إعادة حساب التكاليف
        calculateCosts();

        // تحديث الحقول المرتبطة
        if (type === 'origin') {
            const portOfLoadingInput = document.querySelector('input[name="port_of_loading"]');
            if (portOfLoadingInput) {
                portOfLoadingInput.value = option.textContent;
            }
        } else if (type === 'destination') {
            const portOfDischargeInput = document.querySelector('input[name="port_of_discharge"]');
            if (portOfDischargeInput) {
                portOfDischargeInput.value = option.textContent;
            }
        }

        console.log('✅ تم تحديث الميناء والحقول المرتبطة بنجاح');
    } else {
        console.error('❌ فشل في اختيار الميناء:', option.value);
        showNotification(`❌ فشل في اختيار الميناء ${portName}`, 'danger');
    }
}

// دالة لإظهار الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة للصفحة
    document.body.appendChild(notification);

    // إزالة تلقائية بعد 3 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// التحقق من Bootstrap عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل صفحة حجز الشحنة...');

    // إعداد مستمع الرسائل للنظام الذكي
    setupMessageListener();

    // تحميل الموانئ المختارة مسبقاً
    loadOriginPorts();
    loadDestinationPorts();

    // لا حاجة لمعالجات إضافية - الموانئ تحمل تلقائياً

    if (typeof bootstrap === 'undefined') {
        console.error('Bootstrap is not loaded!');
        // تعطيل أزرار البحث الذكي
        const originBtn = document.getElementById('originPortBtn');
        const destBtn = document.getElementById('destinationPortBtn');

        if (originBtn) {
            originBtn.disabled = true;
            originBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Bootstrap غير محمل';
            originBtn.className = 'btn btn-danger';
        }

        if (destBtn) {
            destBtn.disabled = true;
            destBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Bootstrap غير محمل';
            destBtn.className = 'btn btn-danger';
        }
    } else {
        console.log('Bootstrap loaded successfully:', bootstrap);
    }
});

// فتح نافذة البحث الذكي للموانئ
function openSmartPortSearch(type) {
    console.log('🔍 فتح النظام الذكي للموانئ - النوع:', type);

    // تحديد نوع البحث (origin أو destination)
    window.portSearchType = type;

    // حفظ نوع البحث في localStorage للتواصل مع النافذة الجديدة
    localStorage.setItem('portSearchType', type);
    localStorage.setItem('parentWindow', 'cargo_shipment');

    console.log('💾 تم حفظ معلومات البحث:', { type, parentWindow: 'cargo_shipment' });

    // فتح النافذة في تبويب جديد
    const searchUrl = '/shipments/smart-ports';
    const newWindow = window.open(searchUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

    if (!newWindow) {
        showNotification('❌ فشل في فتح نافذة البحث الذكي. تأكد من السماح للنوافذ المنبثقة.', 'error');
        return;
    }

    console.log('🪟 تم فتح نافذة البحث الذكي');
}

// إضافة مستمع الرسائل عند تحميل الصفحة
function setupMessageListener() {
    // إزالة المستمع القديم إذا وجد
    if (window.portMessageListener) {
        window.removeEventListener('message', window.portMessageListener);
    }

    // إنشاء مستمع جديد
    window.portMessageListener = function(event) {
        console.log('📨 تم استلام رسالة:', event.data);

        if (event.data && event.data.type === 'portSelected') {
            const { portId, portCode, portName, country, city, searchType } = event.data;
            console.log('🚢 تم اختيار ميناء:', { portId, portCode, portName, country, city, searchType });

            // إعادة تحميل القوائم بعد الاختيار
            if (searchType === 'origin') {
                loadOriginPorts();
            } else if (searchType === 'destination') {
                loadDestinationPorts();
            }

            // تحديث الميناء المختار بعد إعادة التحميل
            setTimeout(() => {
                updateSelectedPort(portId, portCode, portName, country, city, searchType);
            }, 500);
        }
    };

    // إضافة المستمع
    window.addEventListener('message', window.portMessageListener);
    console.log('👂 تم إعداد مستمع الرسائل');
}

// إرسال النموذج
document.getElementById('cargoShipmentForm').addEventListener('submit', function(e) {
    e.preventDefault(); // منع الإرسال العادي

    console.log('🚀 بدء إرسال النموذج...');

    // تحديث البيانات المخفية قبل الإرسال
    console.log('🔄 تحديث البيانات قبل الإرسال...');
    updateCargoItemsJson();
    updateContainersJson();

    // التحقق من البيانات المرسلة
    const cargoItemsValue = document.getElementById('cargoItemsJson').value;
    const containersValue = document.getElementById('containersJson').value;

    console.log('📤 البيانات التي سيتم إرسالها:');
    console.log('🔍 cargoItems:', cargoItemsValue);
    console.log('🔍 containers:', containersValue);

    // التحقق من رقم البوليصة
    const blNumber = document.querySelector('input[name="bill_of_lading_number"]').value;
    if (!blNumber.trim()) {
        alert('رقم البوليصة مطلوب!');
        return false;
    }

    // التحقق من وجود أصناف
    if (cargoItems.length === 0) {
        if (!confirm('لا توجد أصناف في الشحنة. هل تريد المتابعة؟')) {
            return false;
        }
    }

    // إنشاء FormData وإرسالها
    const formData = new FormData(this);

    // طباعة البيانات للتشخيص
    console.log('📋 بيانات النموذج:');
    for (let [key, value] of formData.entries()) {
        console.log(`  ${key}: ${value}`);
    }

    // إرسال البيانات بـ fetch
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.redirected) {
            window.location.href = response.url;
        } else if (response.ok) {
            return response.json();
        } else {
            throw new Error('خطأ في الخادم');
        }
    })
    .then(data => {
        if (data && data.success) {
            // إظهار رسالة نجاح مع تفاصيل الشحنة
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'success',
                    title: 'تم حفظ الشحنة بنجاح!',
                    text: `رقم الشحنة: ${data.shipment_number}`,
                    confirmButtonText: 'موافق'
                }).then(() => {
                    // التوجيه المناسب حسب نوع العملية
                    {% if shipment_data %}
                        // في وضع التعديل - العودة للوحة الشحنات
                        window.location.href = '/shipments/dashboard';
                    {% else %}
                        // في وضع الإنشاء - العودة للوحة الشحنات أيضاً
                        window.location.href = '/shipments/dashboard';
                    {% endif %}
                });
            } else {
                alert(`تم حفظ الشحنة بنجاح!\nرقم الشحنة: ${data.shipment_number}`);
                // التوجيه المناسب حسب نوع العملية
                {% if shipment_data %}
                    // في وضع التعديل - العودة للوحة الشحنات
                    window.location.href = '/shipments/dashboard';
                {% else %}
                    // في وضع الإنشاء - العودة للوحة الشحنات أيضاً
                    window.location.href = '/shipments/dashboard';
                {% endif %}
            }
        } else if (data && data.message) {
            // إظهار رسالة خطأ
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في حفظ الشحنة',
                    text: data.message,
                    confirmButtonText: 'موافق'
                });
            } else {
                alert('خطأ: ' + data.message);
            }
        }
    })
    .catch(error => {
        console.error('❌ خطأ:', error);

        // إظهار رسالة خطأ محسنة
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'خطأ في حفظ الشحنة',
                text: 'حدث خطأ غير متوقع أثناء حفظ الشحنة. يرجى المحاولة مرة أخرى.',
                confirmButtonText: 'موافق'
            });
        } else {
            alert('حدث خطأ في حفظ الشحنة. يرجى المحاولة مرة أخرى.');
        }
    });
});

// ==================== 🪄 السحر: نظام أوامر الشراء ====================

// متغيرات السحر
let cargoItems = [];
let itemCounter = 0;
let containers = [];
let containerCounter = 0;

// متغيرات النظام الذكي للموانئ
let selectedPorts = [];
let selectedShippingLines = [];
let smartShippingLines = [];

// تحميل أوامر الشراء عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadPurchaseOrders();
    loadSmartShippingLines();

    // مستمع تغيير أمر الشراء
    document.getElementById('purchaseOrderSelect').addEventListener('change', function() {
        const poId = this.value;
        if (poId) {
            loadPurchaseOrderItems(poId);
        } else {
            clearCargoItems();
        }
    });

    // مستمع إضافة حاوية
    document.getElementById('addContainerBtn').addEventListener('click', function() {
        addEmptyContainer();
    });

    // مستمع تبديل وضع تحرير الأوزان
    document.getElementById('toggleWeightMode').addEventListener('click', function() {
        toggleWeightEditMode();
    });
});

// تحميل أوامر الشراء
function loadPurchaseOrders() {
    fetch('/shipments/api/purchase-orders', {
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('purchaseOrderSelect');

            if (data.success) {
                data.purchase_orders.forEach(po => {
                    const option = document.createElement('option');
                    option.value = po.id;
                    option.textContent = `${po.po_number} - ${po.supplier_name} (${po.total_amount.toLocaleString()} ريال)`;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل أوامر الشراء:', error);
        });
}

// تحميل تفاصيل أمر الشراء (السحر!)
function loadPurchaseOrderItems(poId) {
    fetch(`/shipments/api/purchase-orders/${poId}/items`, {
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // مسح الجدول الحالي
                document.getElementById('cargoItemsBody').innerHTML = '';
                cargoItems = [];

                // إضافة العناصر من أمر الشراء
                data.items.forEach(item => {
                    addCargoItem(item);
                });

                // تحديث قوائم الحاويات والمستلمين في الأصناف
                updateContainerSelects();
                updateRecipientSelects();

                // تحديث وصف البضاعة
                updateCargoDescription(data.items);

                // تحديث الإجمالي وعدد الطرود
                updateTotalAmount();
                updateCargoItemsJson();
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل تفاصيل أمر الشراء:', error);
        });
}

// إضافة عنصر للجدول
function addCargoItem(item = null) {
    itemCounter++;
    const tbody = document.getElementById('cargoItemsBody');

    const row = document.createElement('tr');
    row.id = `item-${itemCounter}`;

    const itemData = item || {
        id: null,
        item_code: '',
        item_name: '',
        unit: '',
        quantity: 0,
        unit_price: 0,
        total_price: 0,
        production_date: '',
        expiry_date: ''
    };

    row.innerHTML = `
        <td>
            <input type="text" class="form-control form-control-sm"
                   value="${itemData.item_code}" onchange="updateItem(${itemCounter})">
        </td>
        <td>
            <input type="text" class="form-control form-control-sm"
                   value="${itemData.item_name}" onchange="updateItem(${itemCounter})" required>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm"
                   value="${itemData.unit}" onchange="updateItem(${itemCounter})">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm"
                   value="${itemData.quantity}" step="0.01" onchange="updateItem(${itemCounter})" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm"
                   value="${itemData.unit_price}" step="0.01" onchange="updateItem(${itemCounter})" required>
        </td>
        <td>
            <span class="total-price">${itemData.total_price.toFixed(2)}</span>
        </td>
        <td>
            <select class="form-select form-select-sm container-select" onchange="updateItem(${itemCounter})">
                <option value="">اختر الحاوية</option>
            </select>
        </td>
        <td>
            <select class="form-select form-select-sm recipient-select" onchange="updateItem(${itemCounter})">
                <option value="">اختر المستلم</option>
                {% if customers %}
                    {% for customer in customers %}
                    <option value="{{ customer.id }}">{{ customer.company_name }}</option>
                    {% endfor %}
                {% endif %}
            </select>
        </td>
        <td>
            <input type="date" class="form-control form-control-sm"
                   value="${itemData.production_date}" onchange="updateItem(${itemCounter})">
        </td>
        <td>
            <input type="date" class="form-control form-control-sm"
                   value="${itemData.expiry_date}" onchange="updateItem(${itemCounter})">
        </td>
        <td>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(${itemCounter})">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    tbody.appendChild(row);

    // إضافة البيانات للمصفوفة
    cargoItems.push({
        id: itemCounter,
        po_item_id: itemData.id,
        item_code: itemData.item_code,
        item_name: itemData.item_name,
        unit: itemData.unit,
        quantity: itemData.quantity,
        unit_price: itemData.unit_price,
        total_price: itemData.total_price,
        container_id: itemData.container_id || null,
        recipient_id: itemData.recipient_id || null,
        recipient_name: itemData.recipient_name || '',
        container_number: itemData.container_number || '',
        production_date: itemData.production_date,
        expiry_date: itemData.expiry_date
    });

    updateTotalAmount();

    // تحديث قوائم الحاويات والمستلمين للعنصر الجديد
    updateContainerSelects();
    updateRecipientSelects();

    // تحديد القيم المحفوظة في القوائم
    setTimeout(() => {
        if (itemData.container_id) {
            const containerSelect = row.querySelector('.container-select');
            if (containerSelect) {
                containerSelect.value = itemData.container_id;
            }
        }

        if (itemData.recipient_id) {
            const recipientSelect = row.querySelector('.recipient-select');
            if (recipientSelect) {
                recipientSelect.value = itemData.recipient_id;
            }
        }
    }, 100);
}

// ملاحظة: تم إزالة إضافة العناصر يدوياً - يتم التحميل من أمر الشراء فقط

// تحديث عنصر
function updateItem(itemId) {
    try {
        const row = document.getElementById(`item-${itemId}`);
        if (!row) {
            console.warn(`⚠️ لم يتم العثور على الصف: item-${itemId}`);
            return;
        }

        const inputs = row.querySelectorAll('input, select');
        if (inputs.length < 5) {
            console.warn(`⚠️ عدد غير كافٍ من المدخلات في الصف: ${inputs.length}`);
            return;
        }

        const quantity = parseFloat(inputs[3]?.value) || 0;
        const unitPrice = parseFloat(inputs[4]?.value) || 0;
        const totalPrice = quantity * unitPrice;

        // تحديث الإجمالي في الجدول
        const totalPriceEl = row.querySelector('.total-price');
        if (totalPriceEl) {
            totalPriceEl.textContent = totalPrice.toFixed(2);
        }

        // تحديث البيانات في المصفوفة
        const itemIndex = cargoItems.findIndex(item => item.id === itemId);
        if (itemIndex !== -1) {
            // طباعة جميع القيم للتشخيص
            inputs.forEach((input, index) => {
                console.log(`🔍 Input[${index}]: ${input.tagName} = "${input.value}" (type: ${input.type || input.tagName})`);
            });

            // جلب بيانات المستلم بطريقة آمنة
            let recipientId = null;
            let recipientName = '';

            // البحث عن select المستلم بالكلاس
            const recipientSelect = row.querySelector('.recipient-select');

            if (recipientSelect && recipientSelect.value && recipientSelect.value !== '') {
                recipientId = recipientSelect.value;
                if (recipientSelect.selectedIndex > 0) {
                    recipientName = recipientSelect.options[recipientSelect.selectedIndex].text;
                }
            }

            console.log(`🔍 DEBUG updateItem: recipientId = ${recipientId}, recipientName = ${recipientName}`);

            // البحث عن select الحاوية بالكلاس
            const containerSelect = row.querySelector('.container-select');
            const containerId = containerSelect ? containerSelect.value : null;

            const updatedItem = {
                ...cargoItems[itemIndex],
                item_code: inputs[0]?.value || '',
                item_name: inputs[1]?.value || '',
                unit: inputs[2]?.value || '',
                quantity: quantity,
                unit_price: unitPrice,
                total_price: totalPrice,
                container_id: containerId,
                recipient_id: recipientId,
                recipient_name: recipientName,
                production_date: inputs[7]?.value || '', // تاريخ الإنتاج (input رقم 7)
                expiry_date: inputs[8]?.value || ''      // تاريخ الانتهاء (input رقم 8)
            };

            cargoItems[itemIndex] = updatedItem;

            console.log(`✅ تم تحديث الصنف ${itemId}:`, {
                container_id: updatedItem.container_id,
                recipient_id: updatedItem.recipient_id,
                recipient_name: updatedItem.recipient_name,
                production_date: updatedItem.production_date,
                expiry_date: updatedItem.expiry_date
            });
        } else {
            console.error(`❌ لم يتم العثور على الصنف ${itemId} في المصفوفة`);
        }

        updateTotalAmount();
        updateCargoItemsJson();

    } catch (error) {
        console.error('❌ خطأ في تحديث العنصر:', error);
    }
}

// حذف عنصر
function removeItem(itemId) {
    document.getElementById(`item-${itemId}`).remove();
    cargoItems = cargoItems.filter(item => item.id !== itemId);
    updateTotalAmount();
    updateCargoItemsJson();
}

// مسح جميع العناصر
function clearCargoItems() {
    document.getElementById('cargoItemsBody').innerHTML = `
        <tr id="noItemsMessage">
            <td colspan="11" class="text-center text-muted py-4">
                <i class="fas fa-info-circle me-2"></i>
                اختر أمر الشراء أولاً لتحميل تفاصيل البضاعة تلقائياً
            </td>
        </tr>
    `;
    cargoItems = [];
    updateTotalAmount();
    updateCargoItemsJson();
}

// تحديث الإجمالي الكلي وعدد الطرود
function updateTotalAmount() {
    console.log(`🔄 تحديث الإجمالي - عدد الأصناف: ${cargoItems.length}`);

    const total = cargoItems.reduce((sum, item) => sum + item.total_price, 0);
    const totalAmountEl = document.getElementById('totalAmount');
    if (totalAmountEl) {
        totalAmountEl.textContent = total.toFixed(2);
    }

    // حساب إجمالي الكميات وتحديث عدد الطرود تلقائياً
    const totalQuantity = cargoItems.reduce((sum, item) => {
        const qty = parseFloat(item.quantity) || 0;
        console.log(`📦 صنف: ${item.item_name} - كمية: ${qty}`);
        return sum + qty;
    }, 0);

    console.log(`📊 إجمالي الكميات المحسوب: ${totalQuantity}`);

    const packagesField = document.querySelector('input[name="total_packages"]');
    console.log(`🔍 البحث عن حقل total_packages:`, packagesField);

    if (packagesField) {
        const packagesCount = Math.ceil(totalQuantity);
        packagesField.value = packagesCount;
        console.log(`✅ تم تحديث عدد الطرود: ${packagesCount}`);

        // إضافة تأثير بصري لتأكيد التحديث
        packagesField.style.backgroundColor = '#d4edda';
        setTimeout(() => {
            packagesField.style.backgroundColor = '';
        }, 1000);
    } else {
        console.error('❌ لم يتم العثور على حقل عدد الطرود (total_packages)');
        // البحث عن جميع حقول input للتشخيص
        const allInputs = document.querySelectorAll('input[type="number"]');
        console.log('🔍 جميع حقول الأرقام الموجودة:');
        allInputs.forEach((input, index) => {
            console.log(`  ${index}: name="${input.name}" placeholder="${input.placeholder}"`);
        });
    }
}

// تحديث JSON المخفي
function updateCargoItemsJson() {
    const jsonValue = JSON.stringify(cargoItems);
    document.getElementById('cargoItemsJson').value = jsonValue;
    console.log(`🔍 تحديث cargoItems JSON: ${cargoItems.length} عنصر`);

    // طباعة تفاصيل كل صنف للتشخيص
    cargoItems.forEach((item, index) => {
        console.log(`📦 الصنف ${index + 1}:`, {
            id: item.id,
            item_name: item.item_name,
            container_id: item.container_id,
            recipient_id: item.recipient_id,
            recipient_name: item.recipient_name,
            production_date: item.production_date,
            expiry_date: item.expiry_date
        });
    });

    console.log(`📋 JSON الكامل: ${jsonValue}`);
}

// تم حذف دالة إضافة الصنف التجريبي - لا حاجة لها

// تحديث وصف البضاعة تلقائياً
function updateCargoDescription(items) {
    const descriptions = items.map(item => `${item.item_name} (${item.quantity} ${item.unit})`);
    const description = descriptions.join(', ');
    document.querySelector('textarea[name="cargo_description"]').value = description;
}

// ==================== 🚢 إدارة الحاويات ====================

// إضافة حاوية فارغة
function addEmptyContainer() {
    addContainer();
}

// إضافة حاوية للجدول
function addContainer(containerData = null) {
    containerCounter++;
    const tbody = document.getElementById('containersBody');

    const row = document.createElement('tr');
    row.id = `container-${containerCounter}`;

    const data = containerData || {
        container_number: '',
        container_type: '20GP',
        container_size: '20',
        seal_number: '',
        total_weight: 0,
        net_weight: 0,
        temperature_controlled: false,
        notes: ''
    };

    row.innerHTML = `
        <td>
            <input type="text" class="form-control form-control-sm"
                   value="${data.container_number}" onchange="updateContainer(${containerCounter})"
                   placeholder="MSKU1234567" required>
        </td>
        <td>
            <select class="form-select form-select-sm" onchange="updateContainer(${containerCounter})">
                <option value="20GP" ${data.container_type === '20GP' ? 'selected' : ''}>20GP</option>
                <option value="40GP" ${data.container_type === '40GP' ? 'selected' : ''}>40GP</option>
                <option value="40HC" ${data.container_type === '40HC' ? 'selected' : ''}>40HC</option>
                <option value="20RF" ${data.container_type === '20RF' ? 'selected' : ''}>20RF</option>
                <option value="40RF" ${data.container_type === '40RF' ? 'selected' : ''}>40RF</option>
            </select>
        </td>
        <td>
            <select class="form-select form-select-sm" onchange="updateContainer(${containerCounter})">
                <option value="20" ${data.container_size === '20' ? 'selected' : ''}>20 قدم</option>
                <option value="40" ${data.container_size === '40' ? 'selected' : ''}>40 قدم</option>
            </select>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm"
                   value="${data.seal_number}" onchange="updateContainer(${containerCounter})"
                   placeholder="SL123456">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm container-total-weight"
                   value="${data.total_weight}" step="0.1" onchange="updateContainer(${containerCounter}); validateContainerWeights(${containerCounter})"
                   placeholder="الوزن الإجمالي (كجم)">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm container-net-weight"
                   value="${data.net_weight}" step="0.1" onchange="updateContainer(${containerCounter}); validateContainerWeights(${containerCounter})"
                   placeholder="الوزن الصافي (كجم)">
        </td>
        <td>
            <div class="form-check">
                <input type="checkbox" class="form-check-input"
                       ${data.temperature_controlled ? 'checked' : ''} onchange="updateContainer(${containerCounter})">
            </div>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm"
                   value="${data.notes}" onchange="updateContainer(${containerCounter})"
                   placeholder="ملاحظات">
        </td>
        <td>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeContainer(${containerCounter})">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    tbody.appendChild(row);

    // إضافة البيانات للمصفوفة
    containers.push({
        id: containerCounter,
        container_number: data.container_number,
        container_type: data.container_type,
        container_size: data.container_size,
        seal_number: data.seal_number,
        total_weight: data.total_weight,
        net_weight: data.net_weight,
        temperature_controlled: data.temperature_controlled,
        notes: data.notes
    });

    console.log(`📦 تم إضافة حاوية جديدة: ${data.container_number || containerCounter}`);

    updateTotalContainers();
    updateContainerSelects();
    updateContainerSealNumber();
    updateTotalWeightsFromContainers();
}

// تحديث حاوية
function updateContainer(containerId) {
    try {
        const row = document.getElementById(`container-${containerId}`);
        if (!row) {
            console.warn(`⚠️ لم يتم العثور على صف الحاوية: container-${containerId}`);
            return;
        }

        const inputs = row.querySelectorAll('input, select');
        if (inputs.length < 8) {
            console.warn(`⚠️ عدد غير كافٍ من المدخلات في صف الحاوية: ${inputs.length}`);
            return;
        }

        // تحديث البيانات في المصفوفة
        const containerIndex = containers.findIndex(container => container.id === containerId);
        if (containerIndex !== -1) {
            containers[containerIndex] = {
                ...containers[containerIndex],
                container_number: inputs[0]?.value || '',
                container_type: inputs[1]?.value || '',
                container_size: inputs[2]?.value || '',
                seal_number: inputs[3]?.value || '',
                total_weight: parseFloat(inputs[4]?.value) || 0,
                net_weight: parseFloat(inputs[5]?.value) || 0,
                temperature_controlled: inputs[6]?.checked || false,
                notes: inputs[7]?.value || ''
            };
        }

        updateTotalContainers();
        updateContainersJson();
        updateContainerSelects();
        updateContainerSealNumber();
        updateTotalWeightsFromContainers();

    } catch (error) {
        console.error('❌ خطأ في تحديث الحاوية:', error);
    }
}

// التحقق من صحة أوزان الحاوية
function validateContainerWeights(containerId) {
    const row = document.getElementById(`container-${containerId}`);
    if (!row) return;

    const totalWeightInput = row.querySelector('.container-total-weight');
    const netWeightInput = row.querySelector('.container-net-weight');

    if (!totalWeightInput || !netWeightInput) return;

    const totalWeight = parseFloat(totalWeightInput.value) || 0;
    const netWeight = parseFloat(netWeightInput.value) || 0;

    // إزالة الأخطاء السابقة
    totalWeightInput.classList.remove('is-invalid');
    netWeightInput.classList.remove('is-invalid');
    totalWeightInput.setCustomValidity('');
    netWeightInput.setCustomValidity('');

    // التحقق من أن الوزن الصافي أقل من أو يساوي الوزن الإجمالي
    if (netWeight > totalWeight && totalWeight > 0) {
        netWeightInput.setCustomValidity('الوزن الصافي يجب أن يكون أقل من أو يساوي الوزن الإجمالي');
        netWeightInput.classList.add('is-invalid');

        // إضافة tooltip للتوضيح
        netWeightInput.title = 'الوزن الصافي يجب أن يكون أقل من أو يساوي الوزن الإجمالي';
    } else {
        netWeightInput.title = '';
    }

    // تحديث إجمالي الأوزان في قسم تفاصيل البضاعة
    updateTotalWeightsFromContainers();
}

// حساب وتحديث إجمالي الأوزان من جدول الحاويات
function updateTotalWeightsFromContainers() {
    // إذا كان في الوضع اليدوي، لا تحدث الأوزان تلقائياً
    if (isManualWeightMode) {
        console.log('⚠️ الوضع اليدوي مفعل - لن يتم تحديث الأوزان تلقائياً');
        return;
    }

    let totalGrossWeight = 0;
    let totalNetWeight = 0;

    // جمع الأوزان من جميع الحاويات
    const containerRows = document.querySelectorAll('#containersBody tr');

    containerRows.forEach(row => {
        const totalWeightInput = row.querySelector('.container-total-weight');
        const netWeightInput = row.querySelector('.container-net-weight');

        if (totalWeightInput && netWeightInput) {
            const totalWeight = parseFloat(totalWeightInput.value) || 0;
            const netWeight = parseFloat(netWeightInput.value) || 0;

            totalGrossWeight += totalWeight;
            totalNetWeight += netWeight;
        }
    });

    // تحديث حقول الوزن في قسم تفاصيل البضاعة
    const cargoTotalWeightInput = document.querySelector('input[name="total_weight"]');
    const cargoNetWeightInput = document.querySelector('input[name="net_weight"]');

    if (cargoTotalWeightInput) {
        cargoTotalWeightInput.value = totalGrossWeight.toFixed(1);
        console.log(`📊 تحديث الوزن الإجمالي للبضاعة: ${totalGrossWeight.toFixed(1)} كجم`);
    }

    if (cargoNetWeightInput) {
        cargoNetWeightInput.value = totalNetWeight.toFixed(1);
        console.log(`📊 تحديث الوزن الصافي للبضاعة: ${totalNetWeight.toFixed(1)} كجم`);
    }

    // تحديث حساب التكاليف إذا كانت الدالة متاحة
    if (typeof calculateCosts === 'function') {
        calculateCosts();
    }
}

// متغير لتتبع وضع التحرير
let isManualWeightMode = false;

// تبديل وضع تحرير الأوزان
function toggleWeightEditMode() {
    const totalWeightInput = document.querySelector('input[name="total_weight"]');
    const netWeightInput = document.querySelector('input[name="net_weight"]');
    const toggleButton = document.getElementById('toggleWeightMode');

    if (!totalWeightInput || !netWeightInput || !toggleButton) return;

    isManualWeightMode = !isManualWeightMode;

    if (isManualWeightMode) {
        // تفعيل الوضع اليدوي
        totalWeightInput.readOnly = false;
        netWeightInput.readOnly = false;
        totalWeightInput.classList.remove('bg-light');
        netWeightInput.classList.remove('bg-light');

        toggleButton.innerHTML = '<i class="fas fa-calculator me-1"></i>العودة للوضع التلقائي';
        toggleButton.className = 'btn btn-outline-warning btn-sm';

        console.log('🔧 تم تفعيل الوضع اليدوي لتحرير الأوزان');
    } else {
        // تفعيل الوضع التلقائي
        totalWeightInput.readOnly = true;
        netWeightInput.readOnly = true;
        totalWeightInput.classList.add('bg-light');
        netWeightInput.classList.add('bg-light');

        toggleButton.innerHTML = '<i class="fas fa-edit me-1"></i>تحرير الأوزان يدوياً';
        toggleButton.className = 'btn btn-outline-primary btn-sm';

        // إعادة حساب الأوزان من الحاويات
        updateTotalWeightsFromContainers();

        console.log('🔄 تم تفعيل الوضع التلقائي لحساب الأوزان');
    }
}

// حذف حاوية
function removeContainer(containerId) {
    document.getElementById(`container-${containerId}`).remove();
    containers = containers.filter(container => container.id !== containerId);
    updateTotalContainers();
    updateContainersJson();
    updateContainerSelects();
    updateContainerSealNumber();
    updateTotalWeightsFromContainers();
}

// تحديث إجمالي الحاويات
function updateTotalContainers() {
    const totalContainersEl = document.getElementById('totalContainers');
    if (totalContainersEl) {
        totalContainersEl.textContent = containers.length;
    }
}

// تحديث رقم ختم الحاوية في قسم بيانات الشحن
function updateContainerSealNumber() {
    const sealNumberInput = document.querySelector('input[name="container_seal_number"]');
    if (sealNumberInput && containers.length > 0) {
        // جمع جميع أرقام الأختام من الحاويات
        const sealNumbers = containers
            .map(container => container.seal_number)
            .filter(seal => seal && seal.trim()) // إزالة الفارغة
            .join(', '); // دمجها بفاصلة

        if (sealNumbers) {
            sealNumberInput.value = sealNumbers;
            console.log(`🔗 تم تحديث رقم ختم الحاوية: ${sealNumbers}`);
        }
    }
}

// تحديث JSON الحاويات
function updateContainersJson() {
    const jsonValue = JSON.stringify(containers);
    document.getElementById('containersJson').value = jsonValue;
    console.log(`🔍 تحديث containers JSON: ${containers.length} حاوية`);
    console.log(`📋 البيانات: ${jsonValue}`);
}

// تحديث قوائم الحاويات في جدول الأصناف
function updateContainerSelects() {
    const selects = document.querySelectorAll('.container-select');
    console.log(`🔄 تحديث ${selects.length} قائمة حاويات مع ${containers.length} حاويات`);

    selects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">اختر الحاوية</option>';

        containers.forEach(container => {
            const option = document.createElement('option');
            option.value = container.id;
            option.textContent = container.container_number || `حاوية ${container.id}`;
            if (currentValue == container.id) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    });
}

// تحديث قوائم المستلمين في جدول الأصناف
function updateRecipientSelects() {
    const selects = document.querySelectorAll('.recipient-select');

    // جلب قائمة العملاء كمستلمين
    fetch('/shipments/api/customers', {
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                selects.forEach(select => {
                    const currentValue = select.value;
                    select.innerHTML = '<option value="">اختر المستلم</option>';

                    data.customers.forEach(customer => {
                        const option = document.createElement('option');
                        option.value = customer.id;
                        option.textContent = `${customer.company_name} (${customer.customer_code})`;
                        if (currentValue == customer.id) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                });
                console.log(`✅ تم تحميل ${data.customers.length} عميل كمستلمين`);
            } else {
                console.error('❌ فشل في تحميل العملاء:', data.message);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل العملاء:', error);
        });
}



// دوال الأزرار الجديدة
function saveDraft() {
    alert('سيتم تطوير ميزة حفظ المسودة قريباً');
}

function previewShipment() {
    alert('سيتم تطوير ميزة معاينة الشحنة قريباً');
}

// تحسينات لقسم بيانات الشحن
document.addEventListener('DOMContentLoaded', function() {
    // تحديث ميناء التفريغ تلقائياً عند تغيير ميناء الوجهة
    const destinationPortSelect = document.querySelector('select[name="destination_port_id"]');
    const portOfDischargeInput = document.querySelector('input[name="port_of_discharge"]');

    if (destinationPortSelect && portOfDischargeInput) {
        destinationPortSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.text && selectedOption.value) {
                portOfDischargeInput.value = selectedOption.text;
            }
        });
    }

    // تحسينات قسم إرسال المستندات
    const courierSelect = document.querySelector('select[name="courier_company"]');
    const trackingInput = document.querySelector('input[name="documents_tracking_number"]');
    const sentDateInput = document.querySelector('input[name="documents_sent_date"]');
    const statusSelect = document.querySelector('select[name="documents_delivery_status"]');

    // تحديث حالة التسليم تلقائياً عند إدخال رقم التتبع
    if (trackingInput && statusSelect) {
        trackingInput.addEventListener('input', function() {
            if (this.value.trim()) {
                statusSelect.value = 'تم الإرسال';
                // تحديث تاريخ الإرسال إلى اليوم إذا كان فارغاً
                if (sentDateInput && !sentDateInput.value) {
                    const today = new Date().toISOString().split('T')[0];
                    sentDateInput.value = today;
                }
            }
        });
    }

    // تحديث عنوان المستلم تلقائياً من بيانات المستلم
    const consigneeSelect = document.querySelector('select[name="consignee_id"]');
    const recipientNameInput = document.querySelector('input[name="documents_recipient_name"]');

    if (consigneeSelect && recipientNameInput) {
        consigneeSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.text && selectedOption.value) {
                recipientNameInput.value = selectedOption.text;
            }
        });
    }

    // تحديث ميناء التحميل تلقائياً عند تغيير ميناء المنشأ
    const originPortSelect = document.querySelector('select[name="origin_port_id"]');
    const portOfLoadingInput = document.querySelector('input[name="port_of_loading"]');

    if (originPortSelect && portOfLoadingInput) {
        originPortSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.text && selectedOption.value) {
                portOfLoadingInput.value = selectedOption.text;
            }
        });
    }

    // تنسيق رقم البوليصة تلقائياً
    const blNumberInput = document.querySelector('input[name="bill_of_lading_number"]');
    if (blNumberInput) {
        blNumberInput.addEventListener('input', function() {
            // تحويل إلى أحرف كبيرة
            this.value = this.value.toUpperCase();
        });
    }
});

// تحديث النموذج قبل الإرسال
document.getElementById('cargoShipmentForm').addEventListener('submit', function(e) {
    updateCargoItemsJson();
    updateContainersJson();

    // التحقق من رقم البوليصة
    const blNumber = document.querySelector('input[name="bill_of_lading_number"]').value;
    if (!blNumber.trim()) {
        alert('رقم البوليصة مطلوب!');
        e.preventDefault();
        return false;
    }
});

// 🔍 البحث التفاعلي المتقدم - بجانب Dropdown الأصلي
let supplierSearchTimeout;

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة البحث للشاحن
    const shipperSearch = document.getElementById('shipperSearch');
    if (shipperSearch) {
        shipperSearch.addEventListener('input', function() {
            const query = this.value.trim();
            clearTimeout(supplierSearchTimeout);
            if (query.length >= 2) {
                supplierSearchTimeout = setTimeout(() => {
                    searchSuppliers(query, 'shipper');
                }, 300);
            } else {
                hideResults('shipper');
            }
        });
    }

    // تهيئة البحث للمستلم
    const consigneeSearch = document.getElementById('consigneeSearch');
    if (consigneeSearch) {
        consigneeSearch.addEventListener('input', function() {
            const query = this.value.trim();
            clearTimeout(supplierSearchTimeout);
            if (query.length >= 2) {
                supplierSearchTimeout = setTimeout(() => {
                    searchSuppliers(query, 'consignee');
                }, 300);
            } else {
                hideResults('consignee');
            }
        });
    }
});

function searchSuppliers(query, fieldType) {
    const resultsDiv = document.getElementById(`${fieldType}Results`);
    if (!resultsDiv) return;

    resultsDiv.innerHTML = '<div style="padding: 8px; text-align: center; font-size: 12px; color: #666;"><i class="fas fa-spinner fa-spin"></i> جاري البحث...</div>';
    resultsDiv.style.display = 'block';

    fetch(`/shipments/api/suppliers/search?q=${encodeURIComponent(query)}&limit=10`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.suppliers && data.suppliers.length > 0) {
                displayResults(data.suppliers, fieldType);
            } else {
                resultsDiv.innerHTML = '<div style="padding: 8px; text-align: center; color: #666; font-size: 12px;">لا توجد نتائج</div>';
            }
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
            resultsDiv.innerHTML = '<div style="padding: 8px; text-align: center; color: #dc3545; font-size: 12px;">خطأ في البحث</div>';
        });
}

function displayResults(suppliers, fieldType) {
    const resultsDiv = document.getElementById(`${fieldType}Results`);

    let html = '';
    suppliers.forEach(supplier => {
        const displayName = supplier.display_name || supplier.name_ar || supplier.name_en || `مورد ${supplier.id}`;
        const cleanName = displayName.replace(/'/g, "\\'");

        html += `
            <div onclick="selectFromSearch(${supplier.id}, '${cleanName}', '${fieldType}')"
                 style="cursor: pointer; padding: 6px 8px; border-bottom: 1px solid #f0f0f0; font-size: 12px;"
                 onmouseover="this.style.backgroundColor='#f8f9fa'"
                 onmouseout="this.style.backgroundColor='white'">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <strong style="font-size: 13px;">${displayName}</strong>
                        ${supplier.country ? `<br><small style="color: #666; font-size: 11px;">${supplier.country}</small>` : ''}
                    </div>
                    ${supplier.supplier_code ? `<span style="background: #007bff; color: white; padding: 1px 4px; border-radius: 2px; font-size: 10px;">${supplier.supplier_code}</span>` : ''}
                </div>
            </div>
        `;
    });

    resultsDiv.innerHTML = html;
}

function selectFromSearch(id, name, fieldType) {
    const selectElement = document.getElementById(`${fieldType}Select`);
    const searchInput = document.getElementById(`${fieldType}Search`);
    const resultsDiv = document.getElementById(`${fieldType}Results`);

    // إضافة خيار جديد للـ dropdown إذا لم يكن موجود
    let optionExists = false;
    for (let option of selectElement.options) {
        if (option.value == id) {
            optionExists = true;
            break;
        }
    }

    if (!optionExists) {
        const newOption = new Option(name, id);
        selectElement.add(newOption);
    }

    // اختيار القيمة في dropdown
    selectElement.value = id;

    // مسح البحث وإخفاء النتائج
    searchInput.value = '';
    resultsDiv.style.display = 'none';

    // إضافة تأثير بصري
    selectElement.style.borderColor = '#28a745';
    setTimeout(() => {
        selectElement.style.borderColor = '';
    }, 2000);

    console.log(`تم اختيار ${fieldType}: ${name} (ID: ${id})`);
}

function clearSearch(fieldType) {
    const searchInput = document.getElementById(`${fieldType}Search`);
    const resultsDiv = document.getElementById(`${fieldType}Results`);

    searchInput.value = '';
    resultsDiv.style.display = 'none';
}

function hideResults(fieldType) {
    const resultsDiv = document.getElementById(`${fieldType}Results`);
    if (resultsDiv) {
        resultsDiv.style.display = 'none';
    }
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل صفحة حجز الشحنة');
    console.log('🔍 URL الحالي:', window.location.href);
    console.log('🔍 pathname:', window.location.pathname);
    console.log('🔍 search:', window.location.search);

    // فحص المتغيرات المرسلة من الخادم
    console.log('🔍 فحص المتغيرات:');
    console.log('  - shipment_data موجود:', {{ 'true' if shipment_data else 'false' }});
    console.log('  - is_edit_mode:', {{ 'true' if is_edit_mode else 'false' }});
    {% if shipment_data %}
        console.log('  - shipment_data.id:', {{ shipment_data.id }});
        console.log('  - shipment_data.tracking_number:', '{{ shipment_data.tracking_number }}');
    {% endif %}

    // فحص وضع التعديل
    {% if shipment_data %}
        console.log('✏️ وضع التعديل - البيانات المحملة:');
        console.log('  - رقم التتبع: {{ shipment_data.tracking_number }}');
        console.log('  - المرسل ID: {{ shipment_data.shipper_id }}');
        console.log('  - المستقبل ID: {{ shipment_data.consignee_id }}');
        console.log('  - خط الشحن ID: {{ shipment_data.shipping_line_id }}');
        console.log('  - ميناء الوصول ID: {{ shipment_data.destination_port_id }}');
        console.log('  - عدد الطرود: {{ shipment_data.total_packages }}');
        console.log('  - عدد الحاويات: {{ (shipment_data['containers']|length) if (shipment_data and shipment_data['containers']) else 0 }}');
        console.log('  - عدد الأصناف: {{ (shipment_data['items']|length) if (shipment_data and shipment_data['items']) else 0 }}');

        // طباعة البيانات الخام للتشخيص
        console.log('🔍 البيانات الخام:');
        console.log('  - shipment_data:', {{ shipment_data|tojson }});

        {% if shipment_data['containers'] %}
            console.log('📦 بيانات الحاويات الخام:');
            {% for container in shipment_data['containers'] %}
                console.log('    حاوية {{ loop.index }}:', {{ container|tojson }});
            {% endfor %}
        {% endif %}

        {% if shipment_data['items'] %}
            console.log('📋 بيانات الأصناف الخام:');
            {% for item in shipment_data['items'] %}
                console.log('    صنف {{ loop.index }}:', {{ item|tojson }});
            {% endfor %}
        {% endif %}

        // تحميل الحاويات
        {% if shipment_data and shipment_data['containers'] %}
            console.log('📦 تحميل الحاويات...');
            console.log('📊 عدد الحاويات المتاحة: {{ shipment_data['containers']|length }}');

            // تأخير التحميل للتأكد من تهيئة الصفحة
            setTimeout(() => {
                // التحقق من وجود الوظائف والعناصر
                if (typeof addContainer === 'function') {
                    console.log('✅ وظيفة addContainer متاحة');
                } else {
                    console.error('❌ وظيفة addContainer غير متاحة');
                    return;
                }

                const containersBody = document.getElementById('containersBody');
                if (containersBody) {
                    console.log('✅ جدول الحاويات متاح');
                } else {
                    console.error('❌ جدول الحاويات غير متاح');
                    return;
                }

                {% for container in shipment_data['containers'] %}
                    console.log('🔄 إضافة حاوية: {{ container.container_number }}');
                    try {
                        addContainer({
                            id: {{ container.id }},
                            container_number: '{{ container.container_number }}',
                            container_type: '{{ container.container_type }}',
                            container_size: '{{ container.container_size }}',
                            seal_number: '{{ container.seal_number or "" }}',
                            total_weight: {{ container.total_weight or container.weight_empty or 0 }},
                            net_weight: {{ container.net_weight or container.weight_loaded or 0 }},
                            temperature_controlled: {{ 'true' if container.temperature_controlled else 'false' }},
                            notes: '{{ container.notes or "" }}'
                        });
                        console.log('✅ تم إضافة حاوية: {{ container.container_number }}');
                    } catch (error) {
                        console.error('❌ خطأ في إضافة حاوية {{ container.container_number }}:', error);
                    }
                {% endfor %}
                console.log('✅ تم تحميل {{ shipment_data['containers']|length }} حاوية');

                // تحديث إجمالي الأوزان بعد تحميل جميع الحاويات
                setTimeout(() => {
                    updateTotalWeightsFromContainers();
                    console.log('📊 تم تحديث إجمالي الأوزان من الحاويات المحملة');
                }, 500);
            }, 1000);
        {% else %}
            console.log('⚠️ لا توجد حاويات للتحميل');
            // تحديث الأوزان حتى لو لم تكن هناك حاويات (ستكون صفر)
            setTimeout(() => {
                updateTotalWeightsFromContainers();
            }, 500);
        {% endif %}

        // تحميل الأصناف
        {% if shipment_data and shipment_data['items'] %}
            console.log('📋 تحميل الأصناف...');
            console.log('📊 عدد الأصناف المتاحة: {{ shipment_data['items']|length }}');

            // تأخير التحميل للتأكد من تهيئة الصفحة
            setTimeout(() => {
                // التحقق من وجود الوظائف والعناصر
                if (typeof addCargoItem === 'function') {
                    console.log('✅ وظيفة addCargoItem متاحة');
                } else {
                    console.error('❌ وظيفة addCargoItem غير متاحة');
                    return;
                }

                const itemsBody = document.getElementById('cargoItemsBody');
                if (itemsBody) {
                    console.log('✅ جدول الأصناف متاح');
                } else {
                    console.error('❌ جدول الأصناف غير متاح');
                    return;
                }

                {% for item in shipment_data['items'] %}
                    console.log('🔄 إضافة صنف: {{ item.item_name }}');
                    try {
                        addCargoItem({
                            id: {{ item.id }},
                            item_code: '{{ item.item_code or "" }}',
                            item_name: '{{ item.item_name }}',
                            quantity: {{ item.quantity or 0 }},
                            unit: '{{ item.unit or "" }}',
                            unit_price: {{ item.unit_price or 0 }},
                            total_price: {{ item.total_price or 0 }},
                            container_id: {{ item.container_id or 'null' }},
                            recipient_id: {{ item.recipient_id or 'null' }},
                            recipient_name: '{{ item.recipient_name or "" }}',
                            container_number: '{{ item.container_number or "" }}',
                            production_date: '{{ item.production_date or "" }}',
                            expiry_date: '{{ item.expiry_date or "" }}'
                        });
                        console.log('✅ تم إضافة صنف: {{ item.item_name }}');
                    } catch (error) {
                        console.error('❌ خطأ في إضافة صنف {{ item.item_name }}:', error);
                    }
                {% endfor %}
                console.log('✅ تم تحميل {{ shipment_data['items']|length }} صنف');
            }, 1200);
        {% else %}
            console.log('⚠️ لا توجد أصناف للتحميل');
        {% endif %}

        // التحقق من تحميل القيم في الحقول
        setTimeout(() => {
            const trackingInput = document.querySelector('input[name="tracking_number"]');
            const shipperSelect = document.querySelector('select[name="shipper_id"]');
            const consigneeSelect = document.querySelector('select[name="consignee_id"]');
            const cargoTextarea = document.querySelector('textarea[name="cargo_description"]');

            console.log('🔍 فحص الحقول المحملة:');
            console.log('  - رقم التتبع:', trackingInput?.value);
            console.log('  - المرسل:', shipperSelect?.value);
            console.log('  - المستقبل:', consigneeSelect?.value);
            console.log('  - وصف البضاعة:', cargoTextarea?.value?.substring(0, 50) + '...');

            // فحص الحاويات والأصناف
            if (typeof containers !== 'undefined') {
                console.log('  - عدد الحاويات المحملة:', containers.length);
                console.log('  - الحاويات:', containers);
            } else {
                console.log('  - متغير containers غير معرف');
            }

            if (typeof cargoItems !== 'undefined') {
                console.log('  - عدد الأصناف المحملة:', cargoItems.length);
                console.log('  - الأصناف:', cargoItems);
            } else {
                console.log('  - متغير cargoItems غير معرف');
            }

            // فحص جداول الحاويات والأصناف في DOM
            const containerTable = document.querySelector('#containersTable tbody');
            const itemsTable = document.querySelector('#cargoItemsTable tbody');

            if (containerTable) {
                console.log('  - صفوف جدول الحاويات:', containerTable.children.length);
            }

            if (itemsTable) {
                console.log('  - صفوف جدول الأصناف:', itemsTable.children.length);
            }
        }, 1500);
    {% else %}
        console.log('➕ وضع الإنشاء - نموذج فارغ');
    {% endif %}

    console.log('✅ تم تهيئة جميع مكونات الصفحة');

    // إعداد جلب رقم التتبع التلقائي
    setupAutoTrackingFetch();

    // تم إزالة جلب جدولة الشحن التلقائي
    // setupAutoScheduleFetch();
});

// ===== جلب رقم التتبع التلقائي =====
function setupAutoTrackingFetch() {
    const fetchBtn = document.getElementById('fetch-tracking-btn');
    const trackingInput = document.getElementById('tracking_number');
    const statusDiv = document.getElementById('tracking-fetch-status');

    if (!fetchBtn || !trackingInput) return;

    fetchBtn.addEventListener('click', function() {
        fetchTrackingNumber();
    });
}

function fetchTrackingNumber() {
    const fetchBtn = document.getElementById('fetch-tracking-btn');
    const trackingInput = document.getElementById('tracking_number');
    const statusDiv = document.getElementById('tracking-fetch-status');

    // جمع البيانات المطلوبة
    const shippingLineSelect = document.querySelector('select[name="shipping_line_id"]');
    const bookingInput = document.querySelector('input[name="booking_number"]');
    const bolInput = document.querySelector('input[name="bill_of_lading_number"]');
    const containerInputs = document.querySelectorAll('input[name="container_number"]');

    // تحديد شركة الشحن
    let shippingCompany = '';
    if (shippingLineSelect && shippingLineSelect.value) {
        const selectedOption = shippingLineSelect.options[shippingLineSelect.selectedIndex];
        shippingCompany = selectedOption.text.toUpperCase();

        // تحويل أسماء الشركات للأكواد التي يفهمها النظام القديم
        if (shippingCompany.includes('MSC')) shippingCompany = 'MSC';
        else if (shippingCompany.includes('MAERSK')) shippingCompany = 'MAERSK';
        else if (shippingCompany.includes('CMA')) shippingCompany = 'CMA CGM';
        else if (shippingCompany.includes('COSCO')) shippingCompany = 'COSCO';
        else if (shippingCompany.includes('HAPAG')) shippingCompany = 'HAPAG';
    }

    // تحديد الرقم المرجعي ونوعه
    let referenceNumber = '';
    let referenceType = '';

    // أولوية للحاوية
    if (containerInputs.length > 0 && containerInputs[0].value.trim()) {
        referenceNumber = containerInputs[0].value.trim();
        referenceType = 'container';
    }
    // ثم رقم البوليصة
    else if (bolInput && bolInput.value.trim()) {
        referenceNumber = bolInput.value.trim();
        referenceType = 'bol';
    }
    // أخيراً رقم الحجز
    else if (bookingInput && bookingInput.value.trim()) {
        referenceNumber = bookingInput.value.trim();
        referenceType = 'booking';
    }

    // التحقق من البيانات
    if (!shippingCompany) {
        showTrackingStatus('يرجى تحديد خط الشحن أولاً', 'warning');
        return;
    }

    if (!referenceNumber) {
        showTrackingStatus('يرجى إدخال رقم الحجز أو البوليصة أو الحاوية', 'warning');
        return;
    }

    console.log(`🔍 جلب رقم التتبع:`);
    console.log(`  - شركة الشحن: ${shippingCompany}`);
    console.log(`  - الرقم المرجعي: ${referenceNumber}`);
    console.log(`  - نوع الرقم: ${referenceType}`);

    // تعطيل الزر وإظهار التحميل
    fetchBtn.disabled = true;
    fetchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الجلب...';
    showTrackingStatus('جاري جلب رقم التتبع...', 'info');

    // إرسال الطلب
    fetch('/shipments/api/fetch-tracking-number', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            shipping_company: shippingCompany,
            reference_number: referenceNumber,
            reference_type: referenceType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            trackingInput.value = data.tracking_number;
            showTrackingStatus(`تم جلب رقم التتبع بنجاح من ${data.reference_info.company}`, 'success');
        } else {
            showTrackingStatus(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('خطأ في جلب رقم التتبع:', error);
        showTrackingStatus('حدث خطأ في جلب رقم التتبع', 'danger');
    })
    .finally(() => {
        // إعادة تفعيل الزر
        fetchBtn.disabled = false;
        fetchBtn.innerHTML = '<i class="fas fa-sync-alt"></i> جلب تلقائي';
    });
}

function showTrackingStatus(message, type) {
    const statusDiv = document.getElementById('tracking-fetch-status');
    if (!statusDiv) return;

    const alertClass = `alert alert-${type} alert-sm`;
    statusDiv.innerHTML = `<div class="${alertClass}" role="alert">${message}</div>`;

    // إخفاء الرسالة بعد 5 ثوان للرسائل الناجحة
    if (type === 'success') {
        setTimeout(() => {
            statusDiv.innerHTML = '';
        }, 5000);
    }
}

// ===== تم إزالة جلب جدولة الشحن التلقائي =====
// function setupAutoScheduleFetch() {
//     // تم إزالة هذه الوظيفة بناءً على طلب المستخدم
// }

// تم إزالة دالة fetchShippingSchedule بناءً على طلب المستخدم
// function fetchShippingSchedule() {
//     // تم إزالة هذه الوظيفة
// }







// تم إزالة دالة showScheduleStatus مع إزالة الجلب التلقائي
// function showScheduleStatus(message, type) {
//     // تم إزالة هذه الوظيفة
// }

</script>

{% endblock %}
