#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Routes للتحميل المشترك - Shared Download Routes
"""

from flask import send_file, current_app
from oracle_manager import get_oracle_manager
import logging
import os

logger = logging.getLogger(__name__)

def safe_convert_lob(value):
    """تحويل آمن لقيم LOB"""
    if value is None:
        return None
    try:
        if hasattr(value, 'read'):
            return value.read().decode('utf-8')
        return str(value)
    except:
        return str(value) if value else None

def download_shared_file(file_hash):
    """تحميل ملف مشترك باستخدام hash"""
    try:
        logger.info(f"🔗 طلب تحميل ملف مشترك: {file_hash}")
        
        # البحث عن الملف في قاعدة البيانات باستخدام الروابط المحفوظة
        oracle_manager = get_oracle_manager()
        
        # البحث في جدول وثائق العقود
        search_query = """
            SELECT id, file_path, file_name, title
            FROM contract_documents 
            WHERE onedrive_share_link LIKE :search_pattern
               OR nextcloud_share_link LIKE :search_pattern
        """
        
        search_pattern = f"%{file_hash}%"
        result = oracle_manager.execute_query(search_query, [search_pattern, search_pattern])
        
        if not result:
            logger.warning(f"⚠️ لم يتم العثور على ملف للـ hash: {file_hash}")
            return "الملف غير موجود أو انتهت صلاحية الرابط", 404
        
        doc = result[0]
        file_path = safe_convert_lob(doc[1])
        file_name = safe_convert_lob(doc[2])
        title = safe_convert_lob(doc[3])
        
        logger.info(f"📄 تم العثور على الملف: {file_name}")
        
        # التحقق من وجود الملف
        if not file_path or not os.path.exists(file_path):
            logger.error(f"❌ الملف غير موجود: {file_path}")
            return "الملف غير موجود على الخادم", 404
        
        # إرسال الملف للتحميل
        return send_file(
            file_path,
            as_attachment=True,
            download_name=file_name or f"document_{file_hash}.pdf",
            mimetype='application/octet-stream'
        )
        
    except Exception as e:
        logger.error(f"❌ خطأ في تحميل الملف المشترك: {e}")
        return "حدث خطأ في تحميل الملف", 500
