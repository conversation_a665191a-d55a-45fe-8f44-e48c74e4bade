#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لتحديث أمر الشراء
"""

import sys
sys.path.append('.')
from oracle_manager import OracleManager

def final_test():
    """اختبار نهائي"""
    print('🎯 اختبار نهائي لتحديث أمر الشراء...')

    try:
        oracle = OracleManager()
        
        # فحص الحالة الحالية
        current_query = """
            SELECT 
                po.STATUS as po_status,
                cs.shipment_status,
                ssc.status_name_ar as shipment_status_ar
            FROM PURCHASE_ORDERS po
            JOIN cargo_shipments cs ON po.ID = cs.purchase_order_id
            LEFT JOIN shipment_status_config ssc ON cs.shipment_status = ssc.status_code
            WHERE po.ID = 81
        """
        
        current_result = oracle.execute_query(current_query)
        
        if current_result:
            po_status, shipment_status_code, shipment_status_ar = current_result[0]
            print(f'📋 حالة أمر الشراء: {po_status}')
            print(f'🚢 حالة الشحنة (كود): {shipment_status_code}')
            print(f'🚢 حالة الشحنة (عربي): {shipment_status_ar}')
            
            # المقارنة الصحيحة: مقارنة النص العربي مع النص العربي
            if po_status == shipment_status_ar:
                print('✅ تطابق مثالي: حالة أمر الشراء تطابق حالة الشحنة!')
                success = True
            else:
                print('⚠️ عدم تطابق في النص، ولكن هذا طبيعي')
                print(f'   أمر الشراء: "{po_status}"')
                print(f'   الشحنة: "{shipment_status_ar}"')
                
                # التحقق من الربط في جدول mapping
                mapping_query = """
                    SELECT po_status
                    FROM po_shipment_status_map
                    WHERE shipment_status = :1
                """
                
                mapping_result = oracle.execute_query(mapping_query, [shipment_status_code])
                
                if mapping_result:
                    expected_po_status = mapping_result[0][0]
                    print(f'   المتوقع حسب الربط: "{expected_po_status}"')
                    
                    if po_status == expected_po_status:
                        print('✅ تطابق: حالة أمر الشراء صحيحة حسب جدول الربط!')
                        success = True
                    else:
                        print('❌ عدم تطابق: حالة أمر الشراء لا تطابق جدول الربط')
                        success = False
                else:
                    success = False
        
        oracle.close()
        return success
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        return False

def test_update_function_again():
    """اختبار دالة التحديث مرة أخرى"""
    print('\n🔄 اختبار دالة التحديث مرة أخرى...')

    try:
        oracle = OracleManager()
        
        # استدعاء دالة التحديث
        function_query = "SELECT update_po_from_shipments(81) FROM dual"
        function_result = oracle.execute_query(function_query)
        
        if function_result:
            result_status = function_result[0][0]
            print(f'📋 نتيجة الدالة: {result_status}')
        
        # فحص الحالة بعد التحديث
        after_query = """
            SELECT 
                po.STATUS,
                po.UPDATED_AT
            FROM PURCHASE_ORDERS po
            WHERE po.ID = 81
        """
        
        after_result = oracle.execute_query(after_query)
        
        if after_result:
            status, updated_at = after_result[0]
            print(f'📋 حالة أمر الشراء: {status}')
            print(f'⏰ آخر تحديث: {updated_at}')
        
        oracle.close()
        
    except Exception as e:
        print(f'❌ خطأ: {e}')

def check_po_page_display():
    """فحص عرض صفحة أوامر الشراء"""
    print('\n📄 فحص عرض صفحة أوامر الشراء...')
    
    print('💡 للتحقق من صفحة أوامر الشراء:')
    print('1. افتح صفحة أوامر الشراء في المتصفح')
    print('2. ابحث عن أمر الشراء PO-2025-0012')
    print('3. تحقق من أن الحالة تظهر "تم التسليم"')
    print('4. إذا كانت الحالة لا تزال "وصلت للميناء"، فهناك مشكلة في cache الصفحة')
    
    print('\n🔧 إذا لم تتحدث الصفحة:')
    print('1. اضغط F5 لإعادة تحميل الصفحة')
    print('2. أو امسح cache المتصفح')
    print('3. أو تحقق من أن الصفحة تجلب البيانات من قاعدة البيانات مباشرة')

if __name__ == '__main__':
    # اختبار نهائي
    success = final_test()
    
    # اختبار دالة التحديث
    test_update_function_again()
    
    # نصائح لفحص الصفحة
    check_po_page_display()
    
    print('\n' + '='*50)
    if success:
        print('🎉 تم إصلاح مشكلة تحديث أمر الشراء بنجاح!')
        print('✅ حالة أمر الشراء تطابق حالة الشحنة')
        print('💡 إذا لم تظهر التحديثات في الصفحة، فالمشكلة في cache المتصفح')
    else:
        print('⚠️ لا تزال هناك مشكلة في التحديث')
    
    print('\n🔧 الخطوات التالية:')
    print('1. تحديث صفحة أوامر الشراء (F5)')
    print('2. التحقق من أن الحالة تظهر "تم التسليم"')
    print('3. اختبار تغيير حالة شحنة أخرى')
