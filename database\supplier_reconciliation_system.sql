-- =====================================================
-- نظام مطابقة الأرصدة الشامل للموردين
-- Comprehensive Supplier Balance Reconciliation System
-- =====================================================

-- 1. جدول دورات المطابقة
CREATE TABLE RECONCILIATION_CYCLES (
    cycle_id NUMBER PRIMARY KEY,
    cycle_name VARCHAR2(100) NOT NULL,
    cycle_description VARCHAR2(500),
    reconciliation_date DATE NOT NULL,
    period_from DATE NOT NULL,
    period_to DATE NOT NULL,
    cycle_type VARCHAR2(30) DEFAULT 'MONTHLY', -- MONTHLY, QUARTERLY, YEARLY, ADHOC
    status VARCHAR2(20) DEFAULT 'OPEN', -- OPEN, IN_PROGRESS, COMPLETED, CANCELLED
    
    -- إحصائيات الدورة
    total_suppliers NUMBER DEFAULT 0,
    matched_suppliers NUMBER DEFAULT 0,
    unmatched_suppliers NUMBER DEFAULT 0,
    total_differences_amount NUMBER(15,2) DEFAULT 0,
    
    -- معلومات المعالجة
    started_by NUMBER,
    started_date TIMESTAMP,
    completed_by NUMBER,
    completed_date TIMESTAMP,
    
    -- ملاحظات ومرفقات
    notes CLOB,
    attachment_path VARCHAR2(500),
    
    -- بيانات النظام
    created_by NUMBER NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by NUMBER,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_rc_created_by FOREIGN KEY (created_by) REFERENCES USERS(id),
    CONSTRAINT fk_rc_updated_by FOREIGN KEY (updated_by) REFERENCES USERS(id),
    CONSTRAINT fk_rc_started_by FOREIGN KEY (started_by) REFERENCES USERS(id),
    CONSTRAINT fk_rc_completed_by FOREIGN KEY (completed_by) REFERENCES USERS(id)
);

-- 2. جدول مطابقة الأرصدة التفصيلي
CREATE TABLE SUPPLIER_RECONCILIATION (
    reconciliation_id NUMBER PRIMARY KEY,
    cycle_id NUMBER NOT NULL,
    supplier_id NUMBER NOT NULL,
    currency_code VARCHAR2(3) NOT NULL,
    
    -- أرصدة النظام
    system_opening_balance NUMBER(15,2) DEFAULT 0,
    system_debit_amount NUMBER(15,2) DEFAULT 0,
    system_credit_amount NUMBER(15,2) DEFAULT 0,
    system_closing_balance NUMBER(15,2) DEFAULT 0,
    
    -- أرصدة كشف المورد
    supplier_opening_balance NUMBER(15,2) DEFAULT 0,
    supplier_debit_amount NUMBER(15,2) DEFAULT 0,
    supplier_credit_amount NUMBER(15,2) DEFAULT 0,
    supplier_closing_balance NUMBER(15,2) DEFAULT 0,
    
    -- الفروقات
    opening_difference NUMBER(15,2) DEFAULT 0,
    debit_difference NUMBER(15,2) DEFAULT 0,
    credit_difference NUMBER(15,2) DEFAULT 0,
    closing_difference NUMBER(15,2) DEFAULT 0,
    total_difference NUMBER(15,2) DEFAULT 0,
    
    -- حالة المطابقة
    reconciliation_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, MATCHED, UNMATCHED, ADJUSTED, APPROVED
    match_tolerance NUMBER(15,2) DEFAULT 0.01, -- هامش التسامح في المطابقة
    auto_matched CHAR(1) DEFAULT 'N', -- هل تمت المطابقة تلقائياً
    
    -- معلومات كشف المورد
    supplier_statement_date DATE,
    supplier_statement_reference VARCHAR2(100),
    supplier_statement_file_path VARCHAR2(500),
    
    -- ملاحظات المطابقة
    reconciliation_notes CLOB,
    system_notes CLOB,
    supplier_notes CLOB,
    
    -- معلومات المعالجة
    reconciled_by NUMBER,
    reconciled_date TIMESTAMP,
    approved_by NUMBER,
    approved_date TIMESTAMP,
    
    -- بيانات النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- المفاتيح الخارجية والقيود
    CONSTRAINT fk_sr_cycle FOREIGN KEY (cycle_id) REFERENCES RECONCILIATION_CYCLES(cycle_id),
    CONSTRAINT fk_sr_supplier FOREIGN KEY (supplier_id) REFERENCES SUPPLIERS(id),
    CONSTRAINT fk_sr_currency FOREIGN KEY (currency_code) REFERENCES CURRENCIES(code),
    CONSTRAINT fk_sr_reconciled_by FOREIGN KEY (reconciled_by) REFERENCES USERS(id),
    CONSTRAINT fk_sr_approved_by FOREIGN KEY (approved_by) REFERENCES USERS(id),
    CONSTRAINT uk_sr_cycle_supplier_currency UNIQUE (cycle_id, supplier_id, currency_code)
);

-- 3. جدول تفاصيل الفروقات
CREATE TABLE RECONCILIATION_DIFFERENCES (
    difference_id NUMBER PRIMARY KEY,
    reconciliation_id NUMBER NOT NULL,
    difference_type VARCHAR2(50) NOT NULL, -- TIMING, AMOUNT, MISSING_TRANSACTION, DUPLICATE, CLASSIFICATION
    difference_category VARCHAR2(30), -- INVOICE, PAYMENT, CREDIT_NOTE, DEBIT_NOTE, ADJUSTMENT
    
    -- تفاصيل المعاملة في النظام
    system_transaction_id NUMBER,
    system_transaction_date DATE,
    system_reference_number VARCHAR2(100),
    system_amount NUMBER(15,2),
    system_description VARCHAR2(500),
    
    -- تفاصيل المعاملة في كشف المورد
    supplier_transaction_date DATE,
    supplier_reference_number VARCHAR2(100),
    supplier_amount NUMBER(15,2),
    supplier_description VARCHAR2(500),
    
    -- تحليل الفرق
    expected_amount NUMBER(15,2),
    actual_amount NUMBER(15,2),
    difference_amount NUMBER(15,2),
    difference_percentage NUMBER(5,2),
    
    -- سبب الفرق
    difference_reason VARCHAR2(500),
    root_cause VARCHAR2(200),
    impact_level VARCHAR2(20) DEFAULT 'MEDIUM', -- LOW, MEDIUM, HIGH, CRITICAL
    
    -- حالة المعالجة
    adjustment_required CHAR(1) DEFAULT 'N',
    adjustment_amount NUMBER(15,2),
    adjustment_description VARCHAR2(500),
    resolved CHAR(1) DEFAULT 'N',
    resolution_method VARCHAR2(100),
    resolution_notes CLOB,
    
    -- معلومات المعالجة
    identified_by NUMBER,
    identified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_by NUMBER,
    resolved_date TIMESTAMP,
    
    -- بيانات النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_rd_reconciliation FOREIGN KEY (reconciliation_id) REFERENCES SUPPLIER_RECONCILIATION(reconciliation_id),
    CONSTRAINT fk_rd_system_transaction FOREIGN KEY (system_transaction_id) REFERENCES SUPPLIER_TRANSACTIONS(transaction_id),
    CONSTRAINT fk_rd_identified_by FOREIGN KEY (identified_by) REFERENCES USERS(id),
    CONSTRAINT fk_rd_resolved_by FOREIGN KEY (resolved_by) REFERENCES USERS(id)
);

-- 4. جدول تعديلات المطابقة
CREATE TABLE RECONCILIATION_ADJUSTMENTS (
    adjustment_id NUMBER PRIMARY KEY,
    reconciliation_id NUMBER NOT NULL,
    difference_id NUMBER,
    adjustment_type VARCHAR2(30) NOT NULL, -- SYSTEM_ADJUSTMENT, SUPPLIER_CORRECTION, RECLASSIFICATION
    
    -- تفاصيل التعديل
    adjustment_amount NUMBER(15,2) NOT NULL,
    adjustment_currency VARCHAR2(3) NOT NULL,
    adjustment_description VARCHAR2(500) NOT NULL,
    adjustment_reason VARCHAR2(500),
    
    -- المعاملة المرتبطة
    related_transaction_id NUMBER,
    new_transaction_created CHAR(1) DEFAULT 'N',
    
    -- حالة التعديل
    adjustment_status VARCHAR2(20) DEFAULT 'PENDING', -- PENDING, APPROVED, APPLIED, REJECTED
    approval_required CHAR(1) DEFAULT 'Y',
    
    -- معلومات الموافقة
    requested_by NUMBER NOT NULL,
    requested_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approved_by NUMBER,
    approved_date TIMESTAMP,
    applied_by NUMBER,
    applied_date TIMESTAMP,
    
    -- ملاحظات
    request_notes CLOB,
    approval_notes CLOB,
    rejection_reason VARCHAR2(500),
    
    -- بيانات النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_ra_reconciliation FOREIGN KEY (reconciliation_id) REFERENCES SUPPLIER_RECONCILIATION(reconciliation_id),
    CONSTRAINT fk_ra_difference FOREIGN KEY (difference_id) REFERENCES RECONCILIATION_DIFFERENCES(difference_id),
    CONSTRAINT fk_ra_currency FOREIGN KEY (adjustment_currency) REFERENCES CURRENCIES(code),
    CONSTRAINT fk_ra_requested_by FOREIGN KEY (requested_by) REFERENCES USERS(id),
    CONSTRAINT fk_ra_approved_by FOREIGN KEY (approved_by) REFERENCES USERS(id),
    CONSTRAINT fk_ra_applied_by FOREIGN KEY (applied_by) REFERENCES USERS(id)
);

-- 5. جدول سجل أنشطة المطابقة
CREATE TABLE RECONCILIATION_ACTIVITY_LOG (
    log_id NUMBER PRIMARY KEY,
    cycle_id NUMBER,
    reconciliation_id NUMBER,
    activity_type VARCHAR2(50) NOT NULL, -- CYCLE_CREATED, RECONCILIATION_STARTED, DIFFERENCE_IDENTIFIED, ADJUSTMENT_APPLIED
    activity_description VARCHAR2(500) NOT NULL,
    
    -- تفاصيل النشاط
    old_value VARCHAR2(200),
    new_value VARCHAR2(200),
    affected_field VARCHAR2(100),
    
    -- معلومات المستخدم
    performed_by NUMBER NOT NULL,
    performed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR2(45),
    user_agent VARCHAR2(500),
    
    -- بيانات إضافية
    additional_data CLOB,
    
    -- المفاتيح الخارجية
    CONSTRAINT fk_ral_cycle FOREIGN KEY (cycle_id) REFERENCES RECONCILIATION_CYCLES(cycle_id),
    CONSTRAINT fk_ral_reconciliation FOREIGN KEY (reconciliation_id) REFERENCES SUPPLIER_RECONCILIATION(reconciliation_id),
    CONSTRAINT fk_ral_performed_by FOREIGN KEY (performed_by) REFERENCES USERS(id)
);

-- إنشاء Sequences للجداول
CREATE SEQUENCE RECONCILIATION_CYCLES_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SUPPLIER_RECONCILIATION_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE RECONCILIATION_DIFFERENCES_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE RECONCILIATION_ADJUSTMENTS_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE RECONCILIATION_ACTIVITY_LOG_SEQ START WITH 1 INCREMENT BY 1;

-- إنشاء Triggers للـ Auto Increment
CREATE OR REPLACE TRIGGER reconciliation_cycles_trigger
    BEFORE INSERT ON RECONCILIATION_CYCLES
    FOR EACH ROW
BEGIN
    IF :NEW.cycle_id IS NULL THEN
        :NEW.cycle_id := RECONCILIATION_CYCLES_SEQ.NEXTVAL;
    END IF;
    
    -- تحديث تاريخ التعديل
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;

CREATE OR REPLACE TRIGGER supplier_reconciliation_trigger
    BEFORE INSERT ON SUPPLIER_RECONCILIATION
    FOR EACH ROW
BEGIN
    IF :NEW.reconciliation_id IS NULL THEN
        :NEW.reconciliation_id := SUPPLIER_RECONCILIATION_SEQ.NEXTVAL;
    END IF;
    
    -- حساب الفروقات تلقائياً
    :NEW.opening_difference := NVL(:NEW.system_opening_balance, 0) - NVL(:NEW.supplier_opening_balance, 0);
    :NEW.debit_difference := NVL(:NEW.system_debit_amount, 0) - NVL(:NEW.supplier_debit_amount, 0);
    :NEW.credit_difference := NVL(:NEW.system_credit_amount, 0) - NVL(:NEW.supplier_credit_amount, 0);
    :NEW.closing_difference := NVL(:NEW.system_closing_balance, 0) - NVL(:NEW.supplier_closing_balance, 0);
    :NEW.total_difference := :NEW.opening_difference + :NEW.debit_difference + :NEW.credit_difference + :NEW.closing_difference;
    
    -- تحديد حالة المطابقة تلقائياً
    IF ABS(:NEW.total_difference) <= NVL(:NEW.match_tolerance, 0.01) THEN
        :NEW.reconciliation_status := 'MATCHED';
        :NEW.auto_matched := 'Y';
    ELSE
        :NEW.reconciliation_status := 'UNMATCHED';
        :NEW.auto_matched := 'N';
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;

CREATE OR REPLACE TRIGGER reconciliation_differences_trigger
    BEFORE INSERT ON RECONCILIATION_DIFFERENCES
    FOR EACH ROW
BEGIN
    IF :NEW.difference_id IS NULL THEN
        :NEW.difference_id := RECONCILIATION_DIFFERENCES_SEQ.NEXTVAL;
    END IF;
    
    -- حساب الفرق والنسبة المئوية
    :NEW.difference_amount := NVL(:NEW.expected_amount, 0) - NVL(:NEW.actual_amount, 0);
    
    IF NVL(:NEW.expected_amount, 0) != 0 THEN
        :NEW.difference_percentage := (:NEW.difference_amount / :NEW.expected_amount) * 100;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;

CREATE OR REPLACE TRIGGER reconciliation_adjustments_trigger
    BEFORE INSERT ON RECONCILIATION_ADJUSTMENTS
    FOR EACH ROW
BEGIN
    IF :NEW.adjustment_id IS NULL THEN
        :NEW.adjustment_id := RECONCILIATION_ADJUSTMENTS_SEQ.NEXTVAL;
    END IF;
    
    :NEW.updated_date := CURRENT_TIMESTAMP;
END;

CREATE OR REPLACE TRIGGER reconciliation_activity_log_trigger
    BEFORE INSERT ON RECONCILIATION_ACTIVITY_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.log_id IS NULL THEN
        :NEW.log_id := RECONCILIATION_ACTIVITY_LOG_SEQ.NEXTVAL;
    END IF;
END;

-- 6. إنشاء فهارس للأداء
CREATE INDEX idx_rc_status ON RECONCILIATION_CYCLES(status);
CREATE INDEX idx_rc_date ON RECONCILIATION_CYCLES(reconciliation_date);
CREATE INDEX idx_rc_period ON RECONCILIATION_CYCLES(period_from, period_to);

CREATE INDEX idx_sr_cycle ON SUPPLIER_RECONCILIATION(cycle_id);
CREATE INDEX idx_sr_supplier ON SUPPLIER_RECONCILIATION(supplier_id);
CREATE INDEX idx_sr_status ON SUPPLIER_RECONCILIATION(reconciliation_status);
CREATE INDEX idx_sr_currency ON SUPPLIER_RECONCILIATION(currency_code);
CREATE INDEX idx_sr_difference ON SUPPLIER_RECONCILIATION(total_difference);

CREATE INDEX idx_rd_reconciliation ON RECONCILIATION_DIFFERENCES(reconciliation_id);
CREATE INDEX idx_rd_type ON RECONCILIATION_DIFFERENCES(difference_type);
CREATE INDEX idx_rd_resolved ON RECONCILIATION_DIFFERENCES(resolved);
CREATE INDEX idx_rd_amount ON RECONCILIATION_DIFFERENCES(difference_amount);

CREATE INDEX idx_ra_reconciliation ON RECONCILIATION_ADJUSTMENTS(reconciliation_id);
CREATE INDEX idx_ra_status ON RECONCILIATION_ADJUSTMENTS(adjustment_status);
CREATE INDEX idx_ra_type ON RECONCILIATION_ADJUSTMENTS(adjustment_type);

CREATE INDEX idx_ral_cycle ON RECONCILIATION_ACTIVITY_LOG(cycle_id);
CREATE INDEX idx_ral_date ON RECONCILIATION_ACTIVITY_LOG(performed_date);
CREATE INDEX idx_ral_type ON RECONCILIATION_ACTIVITY_LOG(activity_type);

-- 7. إنشاء Views للتقارير
CREATE OR REPLACE VIEW V_RECONCILIATION_SUMMARY AS
SELECT
    rc.cycle_id,
    rc.cycle_name,
    rc.reconciliation_date,
    rc.period_from,
    rc.period_to,
    rc.status as cycle_status,

    -- إحصائيات الموردين
    COUNT(sr.reconciliation_id) as total_reconciliations,
    SUM(CASE WHEN sr.reconciliation_status = 'MATCHED' THEN 1 ELSE 0 END) as matched_count,
    SUM(CASE WHEN sr.reconciliation_status = 'UNMATCHED' THEN 1 ELSE 0 END) as unmatched_count,
    SUM(CASE WHEN sr.reconciliation_status = 'ADJUSTED' THEN 1 ELSE 0 END) as adjusted_count,

    -- إحصائيات الفروقات
    SUM(ABS(sr.total_difference)) as total_differences_amount,
    AVG(ABS(sr.total_difference)) as avg_difference_amount,
    MAX(ABS(sr.total_difference)) as max_difference_amount,

    -- معدلات المطابقة
    ROUND((SUM(CASE WHEN sr.reconciliation_status = 'MATCHED' THEN 1 ELSE 0 END) /
           NULLIF(COUNT(sr.reconciliation_id), 0)) * 100, 2) as match_rate_percentage,

    -- تواريخ مهمة
    rc.started_date,
    rc.completed_date,

    -- معلومات المستخدمين
    u1.full_name as created_by_name,
    u2.full_name as completed_by_name

FROM RECONCILIATION_CYCLES rc
LEFT JOIN SUPPLIER_RECONCILIATION sr ON rc.cycle_id = sr.cycle_id
LEFT JOIN USERS u1 ON rc.created_by = u1.id
LEFT JOIN USERS u2 ON rc.completed_by = u2.id
GROUP BY rc.cycle_id, rc.cycle_name, rc.reconciliation_date, rc.period_from, rc.period_to,
         rc.status, rc.started_date, rc.completed_date, u1.full_name, u2.full_name;

CREATE OR REPLACE VIEW V_SUPPLIER_RECONCILIATION_DETAILS AS
SELECT
    sr.reconciliation_id,
    sr.cycle_id,
    rc.cycle_name,
    rc.reconciliation_date,

    -- معلومات المورد
    sr.supplier_id,
    s.supplier_code,
    s.name_ar as supplier_name,
    sr.currency_code,
    c.symbol as currency_symbol,

    -- أرصدة النظام
    sr.system_opening_balance,
    sr.system_debit_amount,
    sr.system_credit_amount,
    sr.system_closing_balance,

    -- أرصدة المورد
    sr.supplier_opening_balance,
    sr.supplier_debit_amount,
    sr.supplier_credit_amount,
    sr.supplier_closing_balance,

    -- الفروقات
    sr.opening_difference,
    sr.debit_difference,
    sr.credit_difference,
    sr.closing_difference,
    sr.total_difference,

    -- حالة المطابقة
    sr.reconciliation_status,
    sr.auto_matched,
    sr.match_tolerance,

    -- عدد الفروقات
    COUNT(rd.difference_id) as differences_count,
    SUM(CASE WHEN rd.resolved = 'Y' THEN 1 ELSE 0 END) as resolved_differences,

    -- معلومات كشف المورد
    sr.supplier_statement_date,
    sr.supplier_statement_reference,

    -- معلومات المعالجة
    sr.reconciled_by,
    u1.full_name as reconciled_by_name,
    sr.reconciled_date,
    sr.approved_by,
    u2.full_name as approved_by_name,
    sr.approved_date

FROM SUPPLIER_RECONCILIATION sr
JOIN RECONCILIATION_CYCLES rc ON sr.cycle_id = rc.cycle_id
JOIN SUPPLIERS s ON sr.supplier_id = s.id
JOIN CURRENCIES c ON sr.currency_code = c.code
LEFT JOIN RECONCILIATION_DIFFERENCES rd ON sr.reconciliation_id = rd.reconciliation_id
LEFT JOIN USERS u1 ON sr.reconciled_by = u1.id
LEFT JOIN USERS u2 ON sr.approved_by = u2.id
GROUP BY sr.reconciliation_id, sr.cycle_id, rc.cycle_name, rc.reconciliation_date,
         sr.supplier_id, s.supplier_code, s.name_ar, sr.currency_code, c.symbol,
         sr.system_opening_balance, sr.system_debit_amount, sr.system_credit_amount, sr.system_closing_balance,
         sr.supplier_opening_balance, sr.supplier_debit_amount, sr.supplier_credit_amount, sr.supplier_closing_balance,
         sr.opening_difference, sr.debit_difference, sr.credit_difference, sr.closing_difference, sr.total_difference,
         sr.reconciliation_status, sr.auto_matched, sr.match_tolerance,
         sr.supplier_statement_date, sr.supplier_statement_reference,
         sr.reconciled_by, u1.full_name, sr.reconciled_date, sr.approved_by, u2.full_name, sr.approved_date;

-- 8. إنشاء تعليقات للجداول
COMMENT ON TABLE RECONCILIATION_CYCLES IS 'دورات مطابقة أرصدة الموردين';
COMMENT ON TABLE SUPPLIER_RECONCILIATION IS 'تفاصيل مطابقة أرصدة الموردين';
COMMENT ON TABLE RECONCILIATION_DIFFERENCES IS 'تفاصيل الفروقات في المطابقة';
COMMENT ON TABLE RECONCILIATION_ADJUSTMENTS IS 'تعديلات المطابقة المطلوبة';
COMMENT ON TABLE RECONCILIATION_ACTIVITY_LOG IS 'سجل أنشطة المطابقة';

-- تم إنشاء نظام مطابقة الأرصدة بنجاح
-- Reconciliation system created successfully
