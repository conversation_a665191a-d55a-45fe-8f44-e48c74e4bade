#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Manager - Unified Oracle and SQLite Support
مدير قاعدة البيانات - دعم موحد لـ Oracle و SQLite
"""

import os
import logging
from typing import Optional, Dict, Any, List
from flask import current_app

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """مدير قاعدة البيانات الموحد"""
    
    def __init__(self):
        self.oracle_jdbc = None
        self.use_oracle = False
        self.connection = None
        # تهيئة تلقائية
        self.initialize()
        
    def initialize(self, app=None):
        """تهيئة مدير قاعدة البيانات"""
        try:
            if app:
                self.app = app
            else:
                try:
                    self.app = current_app
                except:
                    self.app = None

            # استخدام Oracle فقط
            config_name = os.environ.get('FLASK_CONFIG', 'development')
            self.use_oracle = True  # استخدام Oracle فقط

            logger.info("🔧 تهيئة Oracle JDBC...")
            self._initialize_oracle()
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة DatabaseManager: {e}")
            
    def _initialize_oracle(self):
        """تهيئة Oracle Database"""
        try:
            # استخدام OracleManager الجديد
            from oracle_manager import get_oracle_manager
            self.oracle_manager = get_oracle_manager()

            if self.oracle_manager.connect():
                logger.info("✅ Oracle Database initialized successfully")
                return True
            else:
                logger.error("❌ Failed to connect Oracle Database")
                return False

        except Exception as e:
            logger.error(f"❌ Oracle Database initialization error: {e}")
            return False
    
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        if hasattr(self, 'oracle_manager') and self.oracle_manager:
            return self.oracle_manager.connection
        return None

    def execute_query(self, query: str, params: Optional[List] = None) -> List:
        """تنفيذ استعلام"""
        if hasattr(self, 'oracle_manager') and self.oracle_manager:
            return self.oracle_manager.execute_query(query, params)
        return []

    def execute_update(self, query: str, params: Optional[List] = None) -> int:
        """تنفيذ استعلام تحديث (INSERT/UPDATE/DELETE)"""
        if hasattr(self, 'oracle_manager') and self.oracle_manager:
            return self.oracle_manager.execute_update(query, params)
        return 0

    def execute_update(self, query: str, params: Optional[List] = None) -> int:
        """تنفيذ تحديث مع commit تلقائي"""
        if hasattr(self, 'oracle_manager') and self.oracle_manager:
            return self.oracle_manager.execute_update(query, params)
        return 0

    def execute_update_no_commit(self, query: str, params: Optional[List] = None) -> int:
        """تنفيذ تحديث بدون commit تلقائي"""
        if hasattr(self, 'oracle_manager') and self.oracle_manager:
            return self.oracle_manager.execute_update_no_commit(query, params)
        return 0

    def get_cursor(self):
        """الحصول على cursor لقاعدة البيانات"""
        if hasattr(self, 'oracle_manager') and self.oracle_manager:
            return self.oracle_manager.get_cursor()
        return None
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """الحصول على معلومات الجدول"""
        return self._get_oracle_table_info(table_name)
    
    def _get_oracle_table_info(self, table_name: str) -> Dict[str, Any]:
        """معلومات جدول Oracle"""
        try:
            # استعلام عدد الصفوف
            count_query = f"SELECT COUNT(*) FROM {table_name}"
            count_result = self.execute_query(count_query)
            row_count = count_result[0][0] if count_result else 0

            # استعلام معلومات الأعمدة
            columns_query = f"""
            SELECT column_name, data_type, data_length, nullable
            FROM user_tab_columns
            WHERE table_name = UPPER('{table_name}')
            ORDER BY column_id
            """
            columns_result = self.execute_query(columns_query)

            # تحويل نتائج الأعمدة إلى تنسيق مناسب
            columns = []
            if columns_result:
                for col in columns_result:
                    columns.append({
                        'column_name': col[0],
                        'data_type': col[1],
                        'data_length': col[2],
                        'nullable': col[3]
                    })

            return {
                'name': table_name,
                'row_count': row_count,
                'columns': columns,
                'status': 'active'
            }
            
        except Exception as e:
            logger.error(f"Error getting Oracle table info for {table_name}: {e}")
            return {
                'name': table_name,
                'row_count': 0,
                'columns': [],
                'status': 'error',
                'error': str(e)
            }

    
    def get_all_tables_info(self) -> List[Dict[str, Any]]:
        """الحصول على معلومات جميع الجداول"""
        tables_info = []

        try:
            if self.oracle_jdbc:
                # الحصول على قائمة الجداول الموجودة فعلاً في Oracle
                existing_tables_query = """
                SELECT table_name
                FROM user_tables
                ORDER BY table_name
                """
                existing_tables_result = self.execute_query(existing_tables_query)

                if existing_tables_result:
                    for table_row in existing_tables_result:
                        table_name = table_row[0].lower()  # تحويل إلى أحرف صغيرة
                        table_info = self.get_table_info(table_name)
                        if table_info['status'] == 'active':
                            tables_info.append(table_info)
                else:
                    # إذا فشل الاستعلام، استخدم الجداول المعروفة
                    known_tables = ['users', 'branches']
                    for table_name in known_tables:
                        table_info = self.get_table_info(table_name)
                        if table_info['status'] == 'active':
                            tables_info.append(table_info)

        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الجداول: {e}")
            # في حالة الخطأ، استخدم الجداول المعروفة
            known_tables = ['users', 'branches']
            for table_name in known_tables:
                table_info = self.get_table_info(table_name)
                tables_info.append(table_info)

        return tables_info
    
    def test_connection(self) -> Dict[str, Any]:
        """اختبار الاتصال"""
        try:
            if self.oracle_jdbc:
                # اختبار Oracle
                result = self.execute_query("SELECT 1 FROM DUAL")
                return {
                    'status': 'success',
                    'database_type': 'Oracle',
                    'message': 'Oracle connection successful',
                    'result': result
                }
            else:
                return {
                    'status': 'error',
                    'database_type': 'Oracle',
                    'message': 'Oracle JDBC not initialized',
                    'error': 'Oracle JDBC manager not available'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'database_type': 'Oracle' if self.use_oracle else 'SQLite',
                'message': f'Connection failed: {str(e)}',
                'error': str(e)
            }
    
    def commit(self):
        """تأكيد المعاملة"""
        if self.oracle_manager:
            self.oracle_manager.commit()

    def rollback(self):
        """إلغاء المعاملة"""
        if self.oracle_manager:
            self.oracle_manager.rollback()

    def close(self):
        """إغلاق الاتصالات"""
        if self.oracle_jdbc:
            self.oracle_jdbc.close()
            logger.info("✅ Oracle JDBC connections closed")

# إنشاء مثيل عام
db_manager = DatabaseManager()
