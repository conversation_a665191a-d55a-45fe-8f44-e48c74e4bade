{% extends "base.html" %}

{% block content %}
<style>
.carrier-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.carrier-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.carrier-logo {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.performance-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}
</style>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-truck text-primary me-2"></i>
                        إدارة شركات الشحن
                    </h1>
                    <p class="text-muted mb-0">إدارة شركات الشحن والناقلين</p>
                </div>
                <div>
                    <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addCarrierModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة شركة شحن
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card stats-card border-0">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">إجمالي الشركات</h6>
                            <h3 class="mb-0">{{ stats.total_carriers or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-circle p-3">
                                <i class="fas fa-check text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">نشطة</h6>
                            <h3 class="mb-0">{{ stats.active_carriers or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-circle p-3">
                                <i class="fas fa-pause text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">معطلة</h6>
                            <h3 class="mb-0">{{ stats.inactive_carriers or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-circle p-3">
                                <i class="fas fa-star text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">متوسط التقييم</h6>
                            <h3 class="mb-0">{{ "%.1f"|format(stats.avg_rating or 0) }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Carriers List -->
    <div class="row">
        {% if carriers %}
            {% for carrier in carriers %}
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card carrier-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-start mb-3">
                            <div class="carrier-logo me-3">
                                {{ carrier.name[0] if carrier.name else 'C' }}
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">{{ carrier.name }}</h5>
                                <p class="text-muted mb-2">{{ carrier.code }}</p>
                                <span class="badge performance-badge bg-{{ 'success' if carrier.is_active else 'secondary' }}">
                                    {{ 'نشط' if carrier.is_active else 'معطل' }}
                                </span>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editCarrier({{ carrier.id }})">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="viewPerformance({{ carrier.id }})">
                                        <i class="fas fa-chart-line me-2"></i>الأداء
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteCarrier({{ carrier.id }})">
                                        <i class="fas fa-trash me-2"></i>حذف
                                    </a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="mb-0">{{ carrier.total_shipments or 0 }}</h6>
                                    <small class="text-muted">شحنة</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="mb-0">{{ "%.1f"|format(carrier.delivery_rate or 0) }}%</h6>
                                    <small class="text-muted">نجاح</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h6 class="mb-0">{{ "%.1f"|format(carrier.avg_time or 0) }}ساعة</h6>
                                <small class="text-muted">متوسط</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted d-block">
                                <i class="fas fa-user me-1"></i>
                                {{ carrier.contact_person or 'غير محدد' }}
                            </small>
                            <small class="text-muted d-block">
                                <i class="fas fa-phone me-1"></i>
                                {{ carrier.phone or 'غير محدد' }}
                            </small>
                            <small class="text-muted d-block">
                                <i class="fas fa-envelope me-1"></i>
                                {{ carrier.email or 'غير محدد' }}
                            </small>
                        </div>

                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm flex-fill" onclick="viewDetails({{ carrier.id }})">
                                <i class="fas fa-eye me-1"></i>
                                التفاصيل
                            </button>
                            <button class="btn btn-outline-success btn-sm flex-fill" onclick="assignShipment({{ carrier.id }})">
                                <i class="fas fa-plus me-1"></i>
                                تعيين شحنة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-truck text-muted mb-3" style="font-size: 4rem;"></i>
                    <h3 class="text-muted">لا توجد شركات شحن</h3>
                    <p class="text-muted">لم يتم إضافة أي شركات شحن بعد</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCarrierModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة شركة شحن جديدة
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Add Carrier Modal -->
<div class="modal fade" id="addCarrierModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة شركة شحن جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCarrierForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الشركة *</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رمز الشركة *</label>
                            <input type="text" class="form-control" name="code" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الشخص المسؤول</label>
                            <input type="text" class="form-control" name="contact_person">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الموقع الإلكتروني</label>
                            <input type="url" class="form-control" name="website">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">API Endpoint</label>
                            <input type="url" class="form-control" name="api_endpoint">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">API Key</label>
                            <input type="text" class="form-control" name="api_key">
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    شركة نشطة
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// إضافة شركة شحن جديدة
document.getElementById('addCarrierForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{{ url_for("shipments.add_carrier") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إضافة شركة الشحن بنجاح');
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في إضافة شركة الشحن');
    });
});

// عرض تفاصيل الشركة
function viewDetails(carrierId) {
    window.location.href = `/shipments/carriers/${carrierId}`;
}

// تعديل شركة الشحن
function editCarrier(carrierId) {
    alert(`تعديل شركة الشحن ${carrierId} - قريباً`);
}

// عرض أداء الشركة
function viewPerformance(carrierId) {
    alert(`عرض أداء شركة الشحن ${carrierId} - قريباً`);
}

// تعيين شحنة
function assignShipment(carrierId) {
    alert(`تعيين شحنة لشركة ${carrierId} - قريباً`);
}

// حذف شركة الشحن
function deleteCarrier(carrierId) {
    if (confirm('هل أنت متأكد من حذف هذه الشركة؟')) {
        alert(`حذف شركة الشحن ${carrierId} - قريباً`);
    }
}
</script>

{% endblock %}
