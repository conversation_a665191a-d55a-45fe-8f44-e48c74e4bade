-- إضافة أعمدة روابط المشاركة لجدول وثائق طلبات الحوالات
-- Add share link columns to transfer request documents table

-- التحقق من وجود الأعمدة أولاً
DECLARE
    column_exists NUMBER;
BEGIN
    -- فحص عمود share_link
    SELECT COUNT(*) INTO column_exists
    FROM user_tab_columns
    WHERE table_name = 'TRANSFER_REQUEST_DOCUMENTS' AND column_name = 'SHARE_LINK';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE transfer_request_documents ADD share_link VARCHAR2(2000)';
        DBMS_OUTPUT.PUT_LINE('تم إضافة عمود share_link');
    ELSE
        DBMS_OUTPUT.PUT_LINE('عمود share_link موجود مسبقاً');
    END IF;
    
    -- فحص عمود share_service
    SELECT COUNT(*) INTO column_exists
    FROM user_tab_columns
    WHERE table_name = 'TRANSFER_REQUEST_DOCUMENTS' AND column_name = 'SHARE_SERVICE';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE transfer_request_documents ADD share_service VARCHAR2(100)';
        DBMS_OUTPUT.PUT_LINE('تم إضافة عمود share_service');
    ELSE
        DBMS_OUTPUT.PUT_LINE('عمود share_service موجود مسبقاً');
    END IF;
    
    -- فحص عمود share_created_at
    SELECT COUNT(*) INTO column_exists
    FROM user_tab_columns
    WHERE table_name = 'TRANSFER_REQUEST_DOCUMENTS' AND column_name = 'SHARE_CREATED_AT';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE transfer_request_documents ADD share_created_at TIMESTAMP';
        DBMS_OUTPUT.PUT_LINE('تم إضافة عمود share_created_at');
    ELSE
        DBMS_OUTPUT.PUT_LINE('عمود share_created_at موجود مسبقاً');
    END IF;
    
    COMMIT;
    DBMS_OUTPUT.PUT_LINE('✅ تم تطبيق جميع التغييرات بنجاح');
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        DBMS_OUTPUT.PUT_LINE('خطأ: ' || SQLERRM);
        RAISE;
END;
/

-- إضافة تعليقات على الأعمدة الجديدة
COMMENT ON COLUMN transfer_request_documents.share_link IS 'رابط المشاركة السحابي للوثيقة';
COMMENT ON COLUMN transfer_request_documents.share_service IS 'اسم الخدمة السحابية (OneDrive, Nextcloud, etc.)';
COMMENT ON COLUMN transfer_request_documents.share_created_at IS 'تاريخ إنشاء رابط المشاركة';

-- إنشاء فهرس على عمود share_service للبحث السريع
BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_transfer_docs_share_service ON transfer_request_documents(share_service)';
    DBMS_OUTPUT.PUT_LINE('تم إنشاء فهرس share_service');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -955 THEN
            DBMS_OUTPUT.PUT_LINE('فهرس share_service موجود مسبقاً');
        ELSE
            RAISE;
        END IF;
END;
/

COMMIT;
