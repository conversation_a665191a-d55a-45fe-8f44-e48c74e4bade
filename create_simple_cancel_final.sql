-- إن<PERSON><PERSON><PERSON> إجراء إلغاء الحوالات الصحيح
-- Create Correct Transfer Cancellation Procedure

CREATE OR REPLACE PROCEDURE CANCEL_TRANSFER_ACCOUNTING(
    p_transfer_id IN NUMBER,
    p_user_id IN NUMBER DEFAULT 1,
    p_ip_address IN VARCHAR2 DEFAULT NULL,
    p_session_id IN VARCHAR2 DEFAULT NULL,
    p_cancellation_reason IN VARCHAR2 DEFAULT NULL
) AS
    v_request_status VARCHAR2(50);
    v_amount NUMBER(15,2);
    v_money_changer_id NUMBER;
    v_currency VARCHAR2(10);
    v_transfer_exists NUMBER := 0;

BEGIN
    -- نقطة الحفظ
    SAVEPOINT start_transfer_cancellation;

    -- 1. التحقق من وجود الحوالة في TRANSFER_REQUESTS
    BEGIN
        SELECT status, total_amount, money_changer_bank_id, currency
        INTO v_request_status, v_amount, v_money_changer_id, v_currency
        FROM TRANSFER_REQUESTS 
        WHERE id = p_transfer_id;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RAISE_APPLICATION_ERROR(-20001, 'Transfer request not found');
    END;

    -- 2. التحقق من وجود التنفيذ في TRANSFERS
    SELECT COUNT(*) INTO v_transfer_exists
    FROM TRANSFERS 
    WHERE request_id = p_transfer_id;

    -- 3. التحقق من إمكانية الإلغاء
    IF v_request_status NOT IN ('executed', 'cancelled') THEN
        RAISE_APPLICATION_ERROR(-20002, 'Cannot cancel non-executed transfer');
    END IF;

    -- تحديد العملة إذا كانت فارغة
    IF v_currency IS NULL THEN
        v_currency := 'USD';
    END IF;

    -- 4. عكس التأثير المحاسبي في CURRENT_BALANCES
    -- عكس رصيد الصراف (إرجاع المبلغ - مدين)
    UPDATE CURRENT_BALANCES SET
        debit_amount = debit_amount + v_amount,
        current_balance = current_balance + v_amount,
        total_transactions_count = total_transactions_count + 1,
        last_transaction_date = CURRENT_TIMESTAMP,
        last_document_type = 'TRANSFER_CANCEL',
        last_document_number = 'CANCEL-TRF-' || p_transfer_id,
        updated_at = CURRENT_TIMESTAMP,
        updated_by = p_user_id
    WHERE entity_type_code = 'MONEY_CHANGER'
    AND entity_id = v_money_changer_id
    AND currency_code = v_currency;

    -- 5. تحديث حالة الحوالة في TRANSFER_REQUESTS
    UPDATE TRANSFER_REQUESTS SET
        status = 'cancelled',
        updated_at = CURRENT_TIMESTAMP,
        updated_by = p_user_id
    WHERE id = p_transfer_id;

    -- 6. تحديث حالة التنفيذ في TRANSFERS (إذا كان موجود)
    IF v_transfer_exists > 0 THEN
        UPDATE TRANSFERS SET
            status = 'cancelled',
            updated_at = CURRENT_TIMESTAMP
        WHERE request_id = p_transfer_id;
    END IF;

    -- 7. تسجيل النشاط (تجنب مشاكل foreign key)
    BEGIN
        INSERT INTO TRANSFER_ACTIVITY_LOG (
            transfer_id, activity_type, description, created_at, created_by
        ) VALUES (
            p_transfer_id, 'CANCELLATION',
            'Transfer cancelled with accounting reversal',
            CURRENT_TIMESTAMP, p_user_id
        );
    EXCEPTION
        WHEN OTHERS THEN
            -- تجاهل أخطاء foreign key في سجل الأنشطة
            DBMS_OUTPUT.PUT_LINE('Warning: Could not log activity due to FK constraint');
    END;

    -- تأكيد المعاملة
    COMMIT;
    
    DBMS_OUTPUT.PUT_LINE('SUCCESS: Transfer ' || p_transfer_id || ' cancelled successfully');

EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK TO start_transfer_cancellation;
        RAISE_APPLICATION_ERROR(-20999, 'Cancellation failed: ' || SQLERRM);
END;
