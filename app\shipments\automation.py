#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الأتمتة التلقائية لأوامر التسليم
Automated Delivery Orders Management System

هذا النظام يوفر:
- إنشاء أوامر التسليم تلقائياً
- اختيار المخلص الأمثل تلقائياً
- جدولة المهام والمتابعة التلقائية
- تحديث الحالات تلقائياً
- إرسال الإشعارات تلقائياً
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from flask import current_app
from sqlalchemy import text
from database_manager import DatabaseManager
from app.shipments.notifications import NotificationManager
import json

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DeliveryOrderAutomation:
    """نظام الأتمتة التلقائية لأوامر التسليم"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.notification_manager = NotificationManager()
        
        # إعدادات الأتمتة
        self.automation_config = {
            'auto_create_orders': True,
            'auto_assign_agents': True,
            'auto_send_notifications': True,
            'auto_update_status': True,
            'priority_rules': {
                'urgent': ['medical', 'food', 'emergency'],
                'high': ['electronics', 'automotive'],
                'normal': ['general', 'textiles'],
                'low': ['raw_materials']
            },
            'agent_selection_criteria': {
                'specialization_weight': 0.4,
                'rating_weight': 0.3,
                'workload_weight': 0.2,
                'location_weight': 0.1
            }
        }
    
    def process_shipment_status_change(self, shipment_id: int, old_status: str, new_status: str) -> Dict:
        """معالجة تغيير حالة الشحنة وتنفيذ الإجراءات التلقائية"""
        try:
            logger.info(f"Processing status change for shipment {shipment_id}: {old_status} -> {new_status}")
            
            result = {
                'success': True,
                'actions_taken': [],
                'errors': []
            }
            
            # التحقق من الحاجة لإنشاء أمر تسليم تلقائياً
            if self._should_create_delivery_order(new_status):
                order_result = self.auto_create_delivery_order(shipment_id)
                if order_result['success']:
                    result['actions_taken'].append(f"تم إنشاء أمر تسليم تلقائياً: {order_result['order_number']}")
                else:
                    result['errors'].append(f"فشل في إنشاء أمر التسليم: {order_result['message']}")
            
            # تحديث أولوية الأوامر الموجودة
            if new_status in ['customs_clearance', 'ready_for_delivery']:
                priority_result = self._update_order_priority(shipment_id, new_status)
                if priority_result:
                    result['actions_taken'].append("تم تحديث أولوية أمر التسليم")
            
            # إرسال الإشعارات التلقائية
            if self.automation_config['auto_send_notifications']:
                notification_result = self._send_status_notifications(shipment_id, new_status)
                if notification_result:
                    result['actions_taken'].append("تم إرسال الإشعارات التلقائية")
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing shipment status change: {e}")
            return {
                'success': False,
                'message': f'خطأ في معالجة تغيير الحالة: {str(e)}',
                'actions_taken': [],
                'errors': [str(e)]
            }
    
    def auto_create_delivery_order(self, shipment_id: int) -> Dict:
        """إنشاء أمر تسليم تلقائياً للشحنة"""
        try:
            # التحقق من عدم وجود أمر تسليم مسبق
            existing_order_query = """
                SELECT id FROM delivery_orders 
                WHERE shipment_id = :shipment_id 
                AND order_status NOT IN ('cancelled', 'completed')
            """
            existing_order = self.db_manager.execute_query(
                existing_order_query, 
                {'shipment_id': shipment_id}
            )
            
            if existing_order:
                return {
                    'success': False,
                    'message': 'يوجد أمر تسليم مسبق لهذه الشحنة'
                }
            
            # جلب تفاصيل الشحنة
            shipment_details = self._get_shipment_details(shipment_id)
            if not shipment_details:
                return {
                    'success': False,
                    'message': 'لم يتم العثور على تفاصيل الشحنة'
                }
            
            # اختيار المخلص الأمثل تلقائياً
            best_agent = self.auto_select_best_agent(shipment_details)
            if not best_agent:
                return {
                    'success': False,
                    'message': 'لم يتم العثور على مخلص مناسب'
                }
            
            # تحديد الأولوية تلقائياً
            priority = self._determine_priority(shipment_details)
            
            # إنشاء رقم أمر التسليم
            order_number = self._generate_delivery_order_number()
            
            # إنشاء أمر التسليم
            create_order_query = """
                INSERT INTO delivery_orders (
                    id, order_number, shipment_id, customs_agent_id,
                    order_status, priority, created_date, expected_completion_date,
                    delivery_location, special_instructions, created_by,
                    contact_person, contact_phone, contact_email
                ) VALUES (
                    delivery_orders_seq.NEXTVAL, :order_number, :shipment_id, :agent_id,
                    'draft', :priority, SYSDATE, :expected_date,
                    :delivery_location, :instructions, 1,
                    :contact_person, :contact_phone, :contact_email
                )
            """
            
            # تحديد التاريخ المتوقع للإنجاز (3 أيام عمل من الآن)
            expected_date = self._calculate_expected_completion_date(priority)
            
            order_params = {
                'order_number': order_number,
                'shipment_id': shipment_id,
                'agent_id': best_agent['id'],
                'priority': priority,
                'expected_date': expected_date,
                'delivery_location': shipment_details.get('port_of_discharge', ''),
                'instructions': f"أمر تسليم تلقائي للشحنة {shipment_details.get('tracking_number', '')}",
                'contact_person': 'النظام التلقائي',
                'contact_phone': '',
                'contact_email': ''
            }
            
            self.db_manager.execute_update(create_order_query, order_params)
            
            # إضافة الوثائق المطلوبة تلقائياً
            self._add_required_documents(order_number, shipment_details)
            
            # تسجيل العملية في السجل
            self._log_automation_action(
                'AUTO_CREATE_ORDER',
                f"تم إنشاء أمر تسليم تلقائي {order_number} للشحنة {shipment_id}"
            )
            
            return {
                'success': True,
                'order_number': order_number,
                'agent_name': best_agent['name'],
                'priority': priority,
                'expected_date': expected_date.strftime('%Y-%m-%d')
            }
            
        except Exception as e:
            logger.error(f"Error auto-creating delivery order: {e}")
            return {
                'success': False,
                'message': f'خطأ في إنشاء أمر التسليم التلقائي: {str(e)}'
            }
    
    def auto_select_best_agent(self, shipment_details: Dict) -> Optional[Dict]:
        """اختيار أفضل مخلص تلقائياً بناءً على معايير متعددة"""
        try:
            # جلب المخلصين النشطين
            agents_query = """
                SELECT 
                    ca.id, ca.agent_name, ca.specialization, ca.rating,
                    NVL(ca.total_orders, 0) as total_orders,
                    NVL(ca.completed_orders, 0) as completed_orders,
                    NVL(ca.average_completion_days, 0) as avg_days,
                    (SELECT COUNT(*) FROM delivery_orders do 
                     WHERE do.customs_agent_id = ca.id 
                     AND do.order_status IN ('draft', 'sent', 'in_progress')) as current_workload
                FROM customs_agents ca
                WHERE ca.is_active = 1
                ORDER BY ca.rating DESC, current_workload ASC
            """
            
            agents = self.db_manager.execute_query(agents_query)
            if not agents:
                return None
            
            # حساب النقاط لكل مخلص
            best_agent = None
            best_score = -1
            
            for agent in agents:
                score = self._calculate_agent_score(agent, shipment_details)
                if score > best_score:
                    best_score = score
                    best_agent = {
                        'id': agent[0],
                        'name': agent[1],
                        'specialization': agent[2],
                        'rating': agent[3],
                        'score': score
                    }
            
            return best_agent
            
        except Exception as e:
            logger.error(f"Error selecting best agent: {e}")
            return None
    
    def _calculate_agent_score(self, agent: Tuple, shipment_details: Dict) -> float:
        """حساب نقاط المخلص بناءً على معايير متعددة"""
        try:
            criteria = self.automation_config['agent_selection_criteria']
            
            # نقاط التخصص
            specialization_score = 0
            agent_specialization = agent[2] or 'general'
            shipment_category = shipment_details.get('category', 'general')
            
            if agent_specialization.lower() == shipment_category.lower():
                specialization_score = 1.0
            elif agent_specialization.lower() == 'general':
                specialization_score = 0.7
            else:
                specialization_score = 0.3
            
            # نقاط التقييم
            rating = agent[3] or 0
            rating_score = rating / 5.0  # تحويل إلى نسبة من 0 إلى 1
            
            # نقاط العبء الحالي (كلما قل العبء كانت النقاط أعلى)
            current_workload = agent[7] or 0
            max_workload = 10  # الحد الأقصى المفترض للعبء
            workload_score = max(0, (max_workload - current_workload) / max_workload)
            
            # نقاط الموقع (افتراضي 1.0 حالياً)
            location_score = 1.0
            
            # حساب النقاط الإجمالية
            total_score = (
                specialization_score * criteria['specialization_weight'] +
                rating_score * criteria['rating_weight'] +
                workload_score * criteria['workload_weight'] +
                location_score * criteria['location_weight']
            )
            
            return total_score
            
        except Exception as e:
            logger.error(f"Error calculating agent score: {e}")
            return 0.0

    def _should_create_delivery_order(self, status: str) -> bool:
        """تحديد ما إذا كان يجب إنشاء أمر تسليم تلقائياً"""
        auto_create_statuses = ['arrived_port', 'customs_clearance', 'ready_for_delivery']
        return status in auto_create_statuses

    def _get_shipment_details(self, shipment_id: int) -> Optional[Dict]:
        """جلب تفاصيل الشحنة"""
        try:
            query = """
                SELECT
                    cs.id, cs.tracking_number, cs.booking_number,
                    cs.port_of_loading, cs.port_of_discharge,
                    cs.shipment_status, cs.cargo_description,
                    cs.estimated_value, cs.weight, cs.volume,
                    po.supplier_name, po.po_number
                FROM cargo_shipments cs
                LEFT JOIN purchase_orders po ON cs.purchase_order_id = po.id
                WHERE cs.id = :shipment_id
            """

            result = self.db_manager.execute_query(query, {'shipment_id': shipment_id})
            if result:
                row = result[0]
                return {
                    'id': row[0],
                    'tracking_number': row[1],
                    'booking_number': row[2],
                    'port_of_loading': row[3],
                    'port_of_discharge': row[4],
                    'shipment_status': row[5],
                    'cargo_description': row[6],
                    'estimated_value': row[7],
                    'weight': row[8],
                    'volume': row[9],
                    'supplier_name': row[10],
                    'po_number': row[11],
                    'category': self._determine_cargo_category(row[6])
                }
            return None

        except Exception as e:
            logger.error(f"Error getting shipment details: {e}")
            return None

    def _determine_cargo_category(self, description: str) -> str:
        """تحديد فئة البضاعة من الوصف"""
        if not description:
            return 'general'

        description_lower = description.lower()

        # قواعد تصنيف البضائع
        if any(word in description_lower for word in ['دواء', 'طبي', 'medical', 'medicine']):
            return 'medical'
        elif any(word in description_lower for word in ['طعام', 'غذاء', 'food', 'beverage']):
            return 'food'
        elif any(word in description_lower for word in ['إلكترونيات', 'كمبيوتر', 'electronics', 'computer']):
            return 'electronics'
        elif any(word in description_lower for word in ['سيارة', 'قطع غيار', 'automotive', 'spare parts']):
            return 'automotive'
        elif any(word in description_lower for word in ['نسيج', 'ملابس', 'textile', 'clothing']):
            return 'textiles'
        else:
            return 'general'

    def _determine_priority(self, shipment_details: Dict) -> str:
        """تحديد أولوية أمر التسليم تلقائياً"""
        category = shipment_details.get('category', 'general')

        for priority, categories in self.automation_config['priority_rules'].items():
            if category in categories:
                return priority

        # تحديد الأولوية بناءً على القيمة
        estimated_value = shipment_details.get('estimated_value', 0) or 0
        if estimated_value > 100000:
            return 'high'
        elif estimated_value > 50000:
            return 'normal'
        else:
            return 'low'

    def _generate_delivery_order_number(self) -> str:
        """إنشاء رقم أمر تسليم فريد"""
        try:
            # الحصول على التسلسل التالي
            query = "SELECT delivery_orders_seq.NEXTVAL FROM DUAL"
            result = self.db_manager.execute_query(query)

            if result:
                seq_num = result[0][0]
                return f"DO-{datetime.now().year}-{seq_num:06d}"
            else:
                # في حالة فشل الحصول على التسلسل
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                return f"DO-{timestamp}"

        except Exception as e:
            logger.error(f"Error generating delivery order number: {e}")
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            return f"DO-{timestamp}"

    def _calculate_expected_completion_date(self, priority: str) -> datetime:
        """حساب التاريخ المتوقع للإنجاز بناءً على الأولوية"""
        base_date = datetime.now()

        # تحديد عدد أيام العمل بناءً على الأولوية
        if priority == 'urgent':
            work_days = 1
        elif priority == 'high':
            work_days = 2
        elif priority == 'normal':
            work_days = 3
        else:  # low
            work_days = 5

        # إضافة أيام العمل (تجاهل عطلات نهاية الأسبوع)
        current_date = base_date
        days_added = 0

        while days_added < work_days:
            current_date += timedelta(days=1)
            # تجاهل الجمعة (4) والسبت (5)
            if current_date.weekday() not in [4, 5]:
                days_added += 1

        return current_date

    def _add_required_documents(self, order_number: str, shipment_details: Dict):
        """إضافة الوثائق المطلوبة تلقائياً"""
        try:
            # الحصول على معرف أمر التسليم
            order_id_query = "SELECT id FROM delivery_orders WHERE order_number = :order_number"
            order_result = self.db_manager.execute_query(order_id_query, {'order_number': order_number})

            if not order_result:
                return

            order_id = order_result[0][0]

            # قائمة الوثائق المطلوبة الأساسية
            required_docs = [
                ('bill_of_lading', 'بوليصة الشحن', 1),
                ('commercial_invoice', 'الفاتورة التجارية', 1),
                ('packing_list', 'قائمة التعبئة', 1),
                ('certificate_of_origin', 'شهادة المنشأ', 1),
                ('insurance_certificate', 'شهادة التأمين', 0)
            ]

            # إضافة وثائق خاصة بناءً على فئة البضاعة
            category = shipment_details.get('category', 'general')
            if category == 'medical':
                required_docs.extend([
                    ('health_certificate', 'شهادة صحية', 1),
                    ('drug_license', 'ترخيص الأدوية', 1)
                ])
            elif category == 'food':
                required_docs.extend([
                    ('health_certificate', 'شهادة صحية', 1),
                    ('halal_certificate', 'شهادة حلال', 0)
                ])

            # إدراج الوثائق في قاعدة البيانات
            for doc_type, doc_name, is_required in required_docs:
                insert_doc_query = """
                    INSERT INTO delivery_order_documents (
                        id, delivery_order_id, document_type, document_name,
                        is_required, is_available
                    ) VALUES (
                        delivery_order_docs_seq.NEXTVAL, :order_id, :doc_type, :doc_name,
                        :is_required, 0
                    )
                """

                self.db_manager.execute_update(insert_doc_query, {
                    'order_id': order_id,
                    'doc_type': doc_type,
                    'doc_name': doc_name,
                    'is_required': is_required
                })

        except Exception as e:
            logger.error(f"Error adding required documents: {e}")

    def _update_order_priority(self, shipment_id: int, new_status: str) -> bool:
        """تحديث أولوية أمر التسليم بناءً على حالة الشحنة"""
        try:
            # قواعد تحديث الأولوية
            priority_updates = {
                'customs_clearance': 'high',
                'ready_for_delivery': 'urgent'
            }

            new_priority = priority_updates.get(new_status)
            if not new_priority:
                return False

            update_query = """
                UPDATE delivery_orders
                SET priority = :new_priority, updated_at = SYSDATE
                WHERE shipment_id = :shipment_id
                AND order_status NOT IN ('completed', 'cancelled')
            """

            self.db_manager.execute_update(update_query, {
                'new_priority': new_priority,
                'shipment_id': shipment_id
            })

            return True

        except Exception as e:
            logger.error(f"Error updating order priority: {e}")
            return False

    def _send_status_notifications(self, shipment_id: int, new_status: str) -> bool:
        """إرسال الإشعارات التلقائية عند تغيير الحالة"""
        try:
            # تحديد نوع الإشعار بناءً على الحالة
            notification_templates = {
                'arrived_port': 'shipment_arrived',
                'customs_clearance': 'customs_clearance_started',
                'ready_for_delivery': 'ready_for_delivery',
                'out_for_delivery': 'out_for_delivery',
                'delivered': 'delivery_completed'
            }

            template_key = notification_templates.get(new_status)
            if not template_key:
                return False

            # إرسال الإشعار
            self.notification_manager.send_shipment_notification(
                shipment_id, template_key, ['SMS', 'EMAIL']
            )

            return True

        except Exception as e:
            logger.error(f"Error sending status notifications: {e}")
            return False

    def _log_automation_action(self, action: str, description: str):
        """تسجيل إجراءات الأتمتة في السجل"""
        try:
            log_query = """
                INSERT INTO automation_log (
                    id, action, description, action_date, system_user
                ) VALUES (
                    automation_log_seq.NEXTVAL, :action, :description, SYSDATE, 'SYSTEM'
                )
            """

            self.db_manager.execute_update(log_query, {
                'action': action,
                'description': description
            })

        except Exception as e:
            logger.error(f"Error logging automation action: {e}")

    def schedule_daily_automation_tasks(self):
        """جدولة المهام التلقائية اليومية"""
        try:
            logger.info("Starting daily automation tasks...")

            # مراجعة الأوامر المتأخرة
            self._check_overdue_orders()

            # تحديث تقييمات المخلصين
            self._update_agent_ratings()

            # إرسال تذكيرات المواعيد النهائية
            self._send_deadline_reminders()

            # تنظيف البيانات القديمة
            self._cleanup_old_data()

            logger.info("Daily automation tasks completed successfully")

        except Exception as e:
            logger.error(f"Error in daily automation tasks: {e}")

    def _check_overdue_orders(self):
        """فحص الأوامر المتأخرة وتحديث حالتها"""
        try:
            overdue_query = """
                SELECT id, order_number, customs_agent_id, expected_completion_date
                FROM delivery_orders
                WHERE expected_completion_date < SYSDATE
                AND order_status IN ('draft', 'sent', 'in_progress')
            """

            overdue_orders = self.db_manager.execute_query(overdue_query)

            for order in overdue_orders:
                order_id, order_number, agent_id, expected_date = order

                # تحديث الأولوية إلى عاجل
                update_query = """
                    UPDATE delivery_orders
                    SET priority = 'urgent', updated_at = SYSDATE
                    WHERE id = :order_id
                """
                self.db_manager.execute_update(update_query, {'order_id': order_id})

                # إرسال تنبيه للمخلص
                self._send_overdue_notification(order_id, agent_id)

                # تسجيل في السجل
                self._log_automation_action(
                    'OVERDUE_DETECTED',
                    f"تم اكتشاف تأخير في أمر التسليم {order_number}"
                )

        except Exception as e:
            logger.error(f"Error checking overdue orders: {e}")

    def _update_agent_ratings(self):
        """تحديث تقييمات المخلصين بناءً على الأداء"""
        try:
            # حساب التقييمات الجديدة بناءً على الأداء
            rating_query = """
                SELECT
                    ca.id,
                    COUNT(do.id) as total_orders,
                    COUNT(CASE WHEN do.order_status = 'completed' THEN 1 END) as completed_orders,
                    AVG(CASE
                        WHEN do.actual_completion_date IS NOT NULL AND do.expected_completion_date IS NOT NULL
                        THEN do.actual_completion_date - do.expected_completion_date
                        ELSE NULL
                    END) as avg_delay_days,
                    AVG(CASE
                        WHEN do.order_status = 'completed'
                        THEN do.actual_completion_date - do.created_date
                        ELSE NULL
                    END) as avg_completion_days
                FROM customs_agents ca
                LEFT JOIN delivery_orders do ON ca.id = do.customs_agent_id
                WHERE ca.is_active = 1
                GROUP BY ca.id
            """

            agents_performance = self.db_manager.execute_query(rating_query)

            for performance in agents_performance:
                agent_id, total_orders, completed_orders, avg_delay, avg_completion = performance

                # حساب التقييم الجديد
                new_rating = self._calculate_agent_rating(
                    total_orders, completed_orders, avg_delay, avg_completion
                )

                # تحديث التقييم في قاعدة البيانات
                update_rating_query = """
                    UPDATE customs_agents
                    SET rating = :new_rating,
                        total_orders = :total_orders,
                        completed_orders = :completed_orders,
                        average_completion_days = :avg_completion,
                        updated_at = SYSDATE
                    WHERE id = :agent_id
                """

                self.db_manager.execute_update(update_rating_query, {
                    'new_rating': new_rating,
                    'total_orders': total_orders or 0,
                    'completed_orders': completed_orders or 0,
                    'avg_completion': avg_completion or 0,
                    'agent_id': agent_id
                })

        except Exception as e:
            logger.error(f"Error updating agent ratings: {e}")

    def _calculate_agent_rating(self, total_orders: int, completed_orders: int,
                               avg_delay: float, avg_completion: float) -> float:
        """حساب تقييم المخلص بناءً على الأداء"""
        if not total_orders or total_orders == 0:
            return 3.0  # تقييم افتراضي للمخلصين الجدد

        # معدل الإنجاز (0-2 نقاط)
        completion_rate = (completed_orders / total_orders) if total_orders > 0 else 0
        completion_score = completion_rate * 2

        # نقاط التأخير (0-2 نقاط)
        if avg_delay is None:
            delay_score = 1.5  # نقاط متوسطة إذا لم تكن هناك بيانات
        elif avg_delay <= 0:
            delay_score = 2.0  # لا توجد تأخيرات
        elif avg_delay <= 1:
            delay_score = 1.5  # تأخير يوم واحد
        elif avg_delay <= 3:
            delay_score = 1.0  # تأخير 2-3 أيام
        else:
            delay_score = 0.5  # تأخير أكثر من 3 أيام

        # نقاط سرعة الإنجاز (0-1 نقطة)
        if avg_completion is None:
            speed_score = 0.5
        elif avg_completion <= 2:
            speed_score = 1.0  # سريع جداً
        elif avg_completion <= 4:
            speed_score = 0.8  # سريع
        elif avg_completion <= 7:
            speed_score = 0.6  # متوسط
        else:
            speed_score = 0.3  # بطيء

        # التقييم الإجمالي (من 5)
        total_rating = completion_score + delay_score + speed_score
        return min(5.0, max(1.0, total_rating))  # ضمان أن التقييم بين 1 و 5

    def _send_deadline_reminders(self):
        """إرسال تذكيرات المواعيد النهائية"""
        try:
            # البحث عن الأوامر التي تقترب مواعيدها النهائية
            reminder_query = """
                SELECT
                    do.id, do.order_number, do.customs_agent_id,
                    do.expected_completion_date, ca.agent_name, ca.email, ca.mobile
                FROM delivery_orders do
                JOIN customs_agents ca ON do.customs_agent_id = ca.id
                WHERE do.expected_completion_date BETWEEN SYSDATE AND SYSDATE + 1
                AND do.order_status IN ('draft', 'sent', 'in_progress')
                AND ca.is_active = 1
            """

            upcoming_deadlines = self.db_manager.execute_query(reminder_query)

            for deadline in upcoming_deadlines:
                order_id, order_number, agent_id, expected_date, agent_name, email, mobile = deadline

                # إرسال تذكير للمخلص
                self._send_deadline_reminder(order_id, agent_id, order_number, expected_date)

                # تسجيل في السجل
                self._log_automation_action(
                    'DEADLINE_REMINDER',
                    f"تم إرسال تذكير موعد نهائي للأمر {order_number} للمخلص {agent_name}"
                )

        except Exception as e:
            logger.error(f"Error sending deadline reminders: {e}")

    def _send_overdue_notification(self, order_id: int, agent_id: int):
        """إرسال تنبيه تأخير للمخلص"""
        try:
            # يمكن تطوير هذه الوظيفة لاحقاً مع نظام الإشعارات المتقدم
            pass
        except Exception as e:
            logger.error(f"Error sending overdue notification: {e}")

    def _send_deadline_reminder(self, order_id: int, agent_id: int, order_number: str, expected_date):
        """إرسال تذكير موعد نهائي للمخلص"""
        try:
            # يمكن تطوير هذه الوظيفة لاحقاً مع نظام الإشعارات المتقدم
            pass
        except Exception as e:
            logger.error(f"Error sending deadline reminder: {e}")

    def _cleanup_old_data(self):
        """تنظيف البيانات القديمة"""
        try:
            # حذف سجلات السجل القديمة (أكثر من 6 أشهر)
            cleanup_log_query = """
                DELETE FROM automation_log
                WHERE action_date < SYSDATE - 180
            """
            self.db_manager.execute_update(cleanup_log_query)

            # حذف الإشعارات القديمة المقروءة (أكثر من شهر)
            cleanup_notifications_query = """
                DELETE FROM notifications
                WHERE created_at < SYSDATE - 30
                AND is_read = 1
            """
            self.db_manager.execute_update(cleanup_notifications_query)

            logger.info("Old data cleanup completed")

        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")

    def get_automation_statistics(self) -> Dict:
        """الحصول على إحصائيات الأتمتة"""
        try:
            stats = {}

            # إحصائيات الأوامر التلقائية
            auto_orders_query = """
                SELECT
                    COUNT(*) as total_auto_orders,
                    COUNT(CASE WHEN order_status = 'completed' THEN 1 END) as completed_auto_orders,
                    AVG(CASE
                        WHEN actual_completion_date IS NOT NULL AND created_date IS NOT NULL
                        THEN actual_completion_date - created_date
                        ELSE NULL
                    END) as avg_completion_time
                FROM delivery_orders
                WHERE created_by = 1  -- النظام التلقائي
                AND created_date >= SYSDATE - 30  -- آخر 30 يوم
            """

            auto_orders_stats = self.db_manager.execute_query(auto_orders_query)
            if auto_orders_stats:
                row = auto_orders_stats[0]
                stats['auto_orders'] = {
                    'total': row[0] or 0,
                    'completed': row[1] or 0,
                    'avg_completion_days': float(row[2] or 0)
                }

            # إحصائيات المخلصين
            agents_stats_query = """
                SELECT
                    COUNT(*) as total_agents,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_agents,
                    AVG(rating) as avg_rating,
                    MAX(rating) as max_rating,
                    MIN(rating) as min_rating
                FROM customs_agents
            """

            agents_stats = self.db_manager.execute_query(agents_stats_query)
            if agents_stats:
                row = agents_stats[0]
                stats['agents'] = {
                    'total': row[0] or 0,
                    'active': row[1] or 0,
                    'avg_rating': float(row[2] or 0),
                    'max_rating': float(row[3] or 0),
                    'min_rating': float(row[4] or 0)
                }

            # إحصائيات الأداء
            performance_query = """
                SELECT
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN expected_completion_date < SYSDATE AND order_status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue_orders,
                    COUNT(CASE WHEN order_status = 'completed' AND actual_completion_date <= expected_completion_date THEN 1 END) as on_time_completions
                FROM delivery_orders
                WHERE created_date >= SYSDATE - 30
            """

            performance_stats = self.db_manager.execute_query(performance_query)
            if performance_stats:
                row = performance_stats[0]
                total = row[0] or 0
                stats['performance'] = {
                    'total_orders': total,
                    'overdue_orders': row[1] or 0,
                    'on_time_completions': row[2] or 0,
                    'on_time_rate': (row[2] / total * 100) if total > 0 else 0
                }

            return stats

        except Exception as e:
            logger.error(f"Error getting automation statistics: {e}")
            return {}

# مثيل عام للاستخدام
automation_manager = DeliveryOrderAutomation()
