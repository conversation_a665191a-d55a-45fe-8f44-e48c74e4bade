/**
 * مكون ربط جهات الاتصال مع قواعد الأتمتة
 * Automation Contacts Integration Component
 */

class AutomationContactsManager {
    constructor() {
        this.selectedContacts = [];
        this.contactGroups = [];
        this.notificationChannels = ['SMS', 'EMAIL', 'WHATSAPP'];
        this.init();
    }

    init() {
        this.loadContactGroups();
        this.setupEventListeners();
    }

    // جلب مجموعات جهات الاتصال
    async loadContactGroups() {
        try {
            const response = await fetch('/notifications/contacts/api/automation/contact-groups');
            const data = await response.json();
            
            if (data.success) {
                this.contactGroups = data.groups;
                this.renderContactGroupOptions();
            }
        } catch (error) {
            console.error('خطأ في جلب مجموعات جهات الاتصال:', error);
        }
    }

    // عرض خيارات مجموعات جهات الاتصال في قواعد الأتمتة
    renderContactGroupOptions() {
        const containers = document.querySelectorAll('.contact-groups-container');
        
        containers.forEach(container => {
            container.innerHTML = '';
            
            this.contactGroups.forEach(group => {
                const option = document.createElement('div');
                option.className = 'form-check mb-2';
                option.innerHTML = `
                    <input class="form-check-input contact-group-checkbox" 
                           type="checkbox" 
                           value="${group.id}" 
                           id="group_${group.id}">
                    <label class="form-check-label" for="group_${group.id}">
                        <i class="${group.icon} me-2"></i>
                        ${group.name}
                        <small class="text-muted d-block">${group.description}</small>
                    </label>
                `;
                container.appendChild(option);
            });
        });
    }

    // إعداد Event Listeners
    setupEventListeners() {
        // عند تغيير اختيار مجموعات جهات الاتصال
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('contact-group-checkbox')) {
                this.updateSelectedGroups();
            }
        });

        // عند النقر على زر اختيار جهات اتصال مخصصة
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('select-custom-contacts-btn')) {
                this.openCustomContactSelector();
            }
        });
    }

    // تحديث المجموعات المختارة
    updateSelectedGroups() {
        const selectedGroups = [];
        const checkboxes = document.querySelectorAll('.contact-group-checkbox:checked');
        
        checkboxes.forEach(checkbox => {
            const group = this.contactGroups.find(g => g.id === checkbox.value);
            if (group) {
                selectedGroups.push(group);
            }
        });

        this.displaySelectedGroups(selectedGroups);
    }

    // عرض المجموعات المختارة
    displaySelectedGroups(groups) {
        const container = document.getElementById('selectedGroupsDisplay');
        if (!container) return;

        if (groups.length === 0) {
            container.innerHTML = '<p class="text-muted">لم يتم اختيار أي مجموعة</p>';
            return;
        }

        container.innerHTML = groups.map(group => `
            <span class="badge bg-primary me-2 mb-2">
                <i class="${group.icon} me-1"></i>
                ${group.name}
            </span>
        `).join('');
    }

    // فتح واجهة اختيار جهات الاتصال المخصصة
    openCustomContactSelector() {
        // استخدام الواجهة التي أنشأناها سابقاً
        if (typeof openContactSelector === 'function') {
            openContactSelector((selectedContacts) => {
                this.selectedContacts = selectedContacts;
                this.displaySelectedContacts();
            });
        }
    }

    // عرض جهات الاتصال المختارة
    displaySelectedContacts() {
        const container = document.getElementById('selectedContactsDisplay');
        if (!container) return;

        if (this.selectedContacts.length === 0) {
            container.innerHTML = '<p class="text-muted">لم يتم اختيار أي جهة اتصال</p>';
            return;
        }

        container.innerHTML = this.selectedContacts.map(contact => `
            <div class="selected-contact-item d-flex align-items-center justify-content-between mb-2 p-2 border rounded">
                <div>
                    <strong>${contact.name}</strong>
                    ${contact.is_vip ? '<i class="fas fa-star text-warning ms-1"></i>' : ''}
                    <br>
                    <small class="text-muted">
                        ${contact.type} - الأولوية: ${contact.priority}
                        ${contact.phone ? ` - ${contact.phone}` : ''}
                    </small>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="automationContacts.removeContact(${contact.id})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }

    // إزالة جهة اتصال
    removeContact(contactId) {
        this.selectedContacts = this.selectedContacts.filter(c => c.id !== contactId);
        this.displaySelectedContacts();
    }

    // جلب جهات الاتصال حسب المجموعات المختارة
    async getContactsByGroups(groupIds) {
        const contacts = [];
        
        for (const groupId of groupIds) {
            try {
                let url = '/notifications/contacts/api/automation/contacts';
                
                // إضافة فلاتر حسب نوع المجموعة
                const group = this.contactGroups.find(g => g.id === groupId);
                if (group) {
                    const params = new URLSearchParams();
                    
                    switch(groupId) {
                        case 'all_managers':
                            params.append('type', 'MANAGER');
                            break;
                        case 'all_customers':
                            params.append('type', 'CUSTOMER');
                            break;
                        case 'all_drivers':
                            params.append('type', 'DRIVER');
                            break;
                        case 'all_agents':
                            params.append('type', 'AGENT');
                            break;
                        case 'vip_contacts':
                            params.append('vip', 'true');
                            break;
                        case 'high_priority':
                            params.append('min_priority', '8');
                            break;
                    }
                    
                    if (params.toString()) {
                        url += '?' + params.toString();
                    }
                }
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success) {
                    contacts.push(...data.contacts);
                }
            } catch (error) {
                console.error(`خطأ في جلب جهات الاتصال للمجموعة ${groupId}:`, error);
            }
        }
        
        // إزالة التكرارات
        const uniqueContacts = contacts.filter((contact, index, self) => 
            index === self.findIndex(c => c.id === contact.id)
        );
        
        return uniqueContacts;
    }

    // إنشاء بيانات الإشعار للأتمتة
    createNotificationData(ruleId, triggerData = {}) {
        const selectedGroupIds = Array.from(document.querySelectorAll('.contact-group-checkbox:checked'))
            .map(cb => cb.value);
        
        const selectedChannels = Array.from(document.querySelectorAll('.notification-channel-checkbox:checked'))
            .map(cb => cb.value);

        return {
            rule_id: ruleId,
            trigger_data: triggerData,
            recipients: {
                contact_groups: selectedGroupIds,
                custom_contacts: this.selectedContacts.map(c => c.id),
                channels: selectedChannels
            },
            message_template: this.getMessageTemplate(),
            priority: this.getNotificationPriority(),
            created_at: new Date().toISOString()
        };
    }

    // الحصول على قالب الرسالة
    getMessageTemplate() {
        const templateInput = document.getElementById('messageTemplate');
        return templateInput ? templateInput.value : '';
    }

    // الحصول على أولوية الإشعار
    getNotificationPriority() {
        const prioritySelect = document.getElementById('notificationPriority');
        return prioritySelect ? parseInt(prioritySelect.value) : 5;
    }

    // إرسال الإشعار
    async sendNotification(notificationData) {
        try {
            // جلب جهات الاتصال من المجموعات المختارة
            const groupContacts = await this.getContactsByGroups(notificationData.recipients.contact_groups);
            
            // دمج جهات الاتصال من المجموعات والمخصصة
            const allContacts = [...groupContacts, ...this.selectedContacts];
            
            // إزالة التكرارات
            const uniqueContacts = allContacts.filter((contact, index, self) => 
                index === self.findIndex(c => c.id === contact.id)
            );

            // إرسال الإشعار لكل جهة اتصال
            const results = [];
            for (const contact of uniqueContacts) {
                for (const channel of notificationData.recipients.channels) {
                    if (this.contactSupportsChannel(contact, channel)) {
                        const result = await this.sendSingleNotification(contact, channel, notificationData);
                        results.push(result);
                    }
                }
            }

            return {
                success: true,
                sent_count: results.filter(r => r.success).length,
                failed_count: results.filter(r => !r.success).length,
                results: results
            };

        } catch (error) {
            console.error('خطأ في إرسال الإشعارات:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // التحقق من دعم جهة الاتصال للقناة
    contactSupportsChannel(contact, channel) {
        switch(channel) {
            case 'SMS':
                return contact.phone && contact.phone.trim() !== '';
            case 'EMAIL':
                return contact.email && contact.email.trim() !== '';
            case 'WHATSAPP':
                return contact.whatsapp && contact.whatsapp.trim() !== '';
            default:
                return false;
        }
    }

    // إرسال إشعار واحد
    async sendSingleNotification(contact, channel, notificationData) {
        try {
            const response = await fetch('/api/notifications/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contact_id: contact.id,
                    channel: channel,
                    message: this.processMessageTemplate(notificationData.message_template, notificationData.trigger_data),
                    priority: notificationData.priority,
                    rule_id: notificationData.rule_id
                })
            });

            const result = await response.json();
            return {
                success: result.success,
                contact: contact,
                channel: channel,
                message: result.message || result.error
            };

        } catch (error) {
            return {
                success: false,
                contact: contact,
                channel: channel,
                error: error.message
            };
        }
    }

    // معالجة قالب الرسالة
    processMessageTemplate(template, data) {
        let processedMessage = template;
        
        // استبدال المتغيرات في القالب
        Object.keys(data).forEach(key => {
            const placeholder = `{${key}}`;
            processedMessage = processedMessage.replace(new RegExp(placeholder, 'g'), data[key]);
        });

        return processedMessage;
    }

    // الحصول على إحصائيات الإشعارات
    async getNotificationStats(ruleId, dateFrom, dateTo) {
        try {
            const params = new URLSearchParams({
                rule_id: ruleId,
                date_from: dateFrom,
                date_to: dateTo
            });

            const response = await fetch(`/api/notifications/stats?${params}`);
            const data = await response.json();

            return data;
        } catch (error) {
            console.error('خطأ في جلب إحصائيات الإشعارات:', error);
            return { success: false, error: error.message };
        }
    }

    // تصدير إعدادات جهات الاتصال للأتمتة
    exportSettings() {
        const selectedGroupIds = Array.from(document.querySelectorAll('.contact-group-checkbox:checked'))
            .map(cb => cb.value);
        
        const selectedChannels = Array.from(document.querySelectorAll('.notification-channel-checkbox:checked'))
            .map(cb => cb.value);

        return {
            contact_groups: selectedGroupIds,
            custom_contacts: this.selectedContacts,
            channels: selectedChannels,
            message_template: this.getMessageTemplate(),
            priority: this.getNotificationPriority()
        };
    }

    // استيراد إعدادات جهات الاتصال للأتمتة
    importSettings(settings) {
        // تحديد المجموعات
        settings.contact_groups.forEach(groupId => {
            const checkbox = document.getElementById(`group_${groupId}`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });

        // تحديد جهات الاتصال المخصصة
        this.selectedContacts = settings.custom_contacts || [];
        this.displaySelectedContacts();

        // تحديد القنوات
        settings.channels.forEach(channel => {
            const checkbox = document.getElementById(`channel_${channel}`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });

        // تحديد قالب الرسالة
        const templateInput = document.getElementById('messageTemplate');
        if (templateInput && settings.message_template) {
            templateInput.value = settings.message_template;
        }

        // تحديد الأولوية
        const prioritySelect = document.getElementById('notificationPriority');
        if (prioritySelect && settings.priority) {
            prioritySelect.value = settings.priority;
        }

        // تحديث العرض
        this.updateSelectedGroups();
    }
}

// إنشاء instance عام
const automationContacts = new AutomationContactsManager();

// تصدير للاستخدام في ملفات أخرى
window.AutomationContactsManager = AutomationContactsManager;
window.automationContacts = automationContacts;
