# -*- coding: utf-8 -*-
"""
خدمة PDF متقدمة باستخدام Puppeteer
Advanced PDF Service using Puppeteer
"""

import os
import sys
import subprocess
import json
import time
from datetime import datetime

class PuppeteerPDFService:
    """خدمة PDF متقدمة باستخدام Puppeteer"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
        self.base_url = 'https://127.0.0.1:5000'
        self.project_root = os.path.join(os.path.dirname(__file__), '..', '..')
    
    def generate_perfect_pdf(self, delivery_order_id):
        """إنشاء PDF مثالي باستخدام Puppeteer"""
        try:
            print(f"🚀 إنشاء PDF مثالي لأمر التسليم {delivery_order_id} باستخدام Puppeteer...")
            
            # إنشاء اسم الملف
            filename = f"delivery_order_perfect_{delivery_order_id}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # URL صفحة المعاينة
            viewer_url = f"{self.base_url}/shipments/delivery-order-viewer/{delivery_order_id}"
            
            print(f"🌐 صفحة المعاينة: {viewer_url}")
            
            # إنشاء ملف Puppeteer
            if self._create_puppeteer_script(viewer_url, filepath, delivery_order_id):
                # تنفيذ Puppeteer
                if self._run_puppeteer_script(delivery_order_id):
                    if os.path.exists(filepath) and os.path.getsize(filepath) > 1000:
                        file_size = os.path.getsize(filepath)
                        print(f"✅ تم إنشاء PDF مثالي! حجم الملف: {file_size} بايت ({file_size/1024:.1f} KB)")
                        
                        # فحص إضافي للتأكد أنه PDF حقيقي
                        with open(filepath, 'rb') as f:
                            header = f.read(4)
                            if header == b'%PDF':
                                print("✅ الملف هو PDF حقيقي مثالي!")
                                return filepath, "تم إنشاء PDF مثالي باستخدام Puppeteer"
                            else:
                                print("❌ الملف ليس PDF صحيح!")
                                return None, "الملف المُنشأ ليس PDF صحيح"
                    else:
                        print("❌ لم يتم إنشاء PDF أو الملف فارغ")
                        return None, "فشل في إنشاء PDF"
                else:
                    return None, "فشل في تنفيذ Puppeteer"
            else:
                return None, "فشل في إنشاء ملف Puppeteer"
                
        except Exception as e:
            return None, f"خطأ في إنشاء PDF: {str(e)}"
    
    def _create_puppeteer_script(self, viewer_url, output_path, delivery_order_id):
        """إنشاء ملف Puppeteer JavaScript"""
        try:
            # تحويل المسارات إلى مسارات مطلقة
            abs_output_path = os.path.abspath(output_path).replace('\\', '/')
            
            puppeteer_script = f"""
const puppeteer = require('puppeteer');
const fs = require('fs');

(async () => {{
    let browser;
    try {{
        console.log('Starting Puppeteer...');

        browser = await puppeteer.launch({{
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--ignore-certificate-errors',
                '--ignore-ssl-errors',
                '--ignore-certificate-errors-spki-list',
                '--ignore-ssl-errors-list',
                '--allow-running-insecure-content',
                '--disable-features=VizDisplayCompositor',
                '--disable-extensions',
                '--no-first-run',
                '--disable-default-apps'
            ]
        }});

        console.log('Browser launched');

        const page = await browser.newPage();

        await page.setViewport({{
            width: 1920,
            height: 1080
        }});

        console.log('Checking if login is required...');

        await page.goto('{viewer_url}', {{
            waitUntil: 'networkidle2',
            timeout: 30000
        }});

        // فحص إذا كانت الصفحة تحتاج تسجيل دخول
        const needsLogin = await page.evaluate(() => {{
            return document.title.includes('تسجيل الدخول') ||
                   document.querySelector('input[name=\"username\"]') !== null;
        }});

        if (needsLogin) {{
            console.log('Login required, attempting to login...');

            // تسجيل الدخول (استخدام بيانات افتراضية)
            await page.type('input[name=\"username\"]', 'admin');
            await page.type('input[name=\"password\"]', 'admin');
            await page.click('button[type=\"submit\"]');

            // انتظار إعادة التوجيه
            await page.waitForNavigation({{ waitUntil: 'networkidle2', timeout: 60000 }});

            // الذهاب إلى صفحة المعاينة مرة أخرى
            await page.goto('{viewer_url}', {{
                waitUntil: 'networkidle2',
                timeout: 30000
            }});
        }}

        console.log('Page loaded');

        console.log('Waiting for content...');

        // انتظار المحتوى فقط
        await page.waitForSelector('#delivery-order-content', {{ timeout: 60000 }});

        console.log('Content loaded');

        // انتظار إضافي لتحميل الخطوط والأنماط
        await new Promise(resolve => setTimeout(resolve, 5000));

        // تحسين عرض الخطوط والنصوص
        await page.addStyleTag({{
            content: `
                * {{
                    -webkit-font-smoothing: antialiased !important;
                    -moz-osx-font-smoothing: grayscale !important;
                    text-rendering: optimizeLegibility !important;
                }}
                #delivery-order-content {{
                    transform: scale(1);
                    transform-origin: top left;
                }}
            `
        }});

        console.log('Checking libraries...');

        // فحص المكتبات مع محاولة تحميلها إذا لم تكن متاحة
        const librariesReady = await page.evaluate(() => {{
            // إذا لم تكن المكتبات محملة، نحاول تحميلها
            if (typeof html2canvas === 'undefined' || typeof window.jspdf === 'undefined') {{
                console.log('Libraries not loaded, trying to load them...');

                // محاولة تحميل html2canvas
                if (typeof html2canvas === 'undefined') {{
                    const script1 = document.createElement('script');
                    script1.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
                    document.head.appendChild(script1);
                }}

                // محاولة تحميل jsPDF
                if (typeof window.jspdf === 'undefined') {{
                    const script2 = document.createElement('script');
                    script2.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
                    document.head.appendChild(script2);
                }}

                return false;
            }}

            return true;
        }});

        if (!librariesReady) {{
            console.log('Loading libraries...');
            await new Promise(resolve => setTimeout(resolve, 5000));

            // فحص مرة أخرى
            await page.waitForFunction(() => {{
                return typeof html2canvas !== 'undefined' && typeof window.jspdf !== 'undefined';
            }}, {{ timeout: 30000 }});
        }}

        console.log('All libraries ready');

        console.log('Generating PDF...');
        
        const pdfBuffer = await page.evaluate(() => {{
            return new Promise((resolve, reject) => {{
                try {{
                    const controls = document.querySelector('.controls');
                    if (controls) controls.style.display = 'none';

                    const element = document.getElementById('delivery-order-content');
                    if (!element) {{
                        reject(new Error('Content element not found'));
                        return;
                    }}

                    html2canvas(element, {{
                        scale: 2.0,  // زيادة الدقة من 1.2 إلى 2.0
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#ffffff',
                        logging: false,
                        removeContainer: true,
                        letterRendering: true,
                        allowTaint: false,
                        foreignObjectRendering: false,
                        dpi: 300,  // دقة عالية
                        width: element.scrollWidth,
                        height: element.scrollHeight
                    }}).then(function(canvas) {{
                        try {{
                            const {{ jsPDF }} = window.jspdf;
                            const pdf = new jsPDF('p', 'mm', 'a4');

                            // تحسين جودة الصورة - استخدام PNG بدلاً من JPEG للجودة العالية
                            const imgData = canvas.toDataURL('image/png');
                            const imgWidth = 210;
                            const imgHeight = (canvas.height * imgWidth) / canvas.width;

                            // إضافة الصورة بجودة عالية
                            pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight, '', 'FAST');

                            // تقليل الضغط للحصول على جودة أفضل
                            pdf.compress = false;

                            const pdfArrayBuffer = pdf.output('arraybuffer');
                            const pdfUint8Array = new Uint8Array(pdfArrayBuffer);

                            if (controls) controls.style.display = 'block';

                            console.log('PDF created successfully');
                            resolve(Array.from(pdfUint8Array));

                        }} catch (error) {{
                            console.error('PDF creation error:', error);
                            reject(error);
                        }}
                    }}).catch(function(error) {{
                        console.error('html2canvas error:', error);
                        reject(error);
                    }});

                }} catch (error) {{
                    console.error('General error:', error);
                    reject(error);
                }}
            }});
        }});

        console.log('Saving PDF...');

        const buffer = Buffer.from(pdfBuffer);
        fs.writeFileSync('{abs_output_path}', buffer);

        console.log('PDF saved successfully');

        await browser.close();
        console.log('PDF generation completed!');

    }} catch (error) {{
        console.error('Puppeteer error:', error);
        if (browser) {{
            await browser.close();
        }}
        process.exit(1);
    }}
}})();
            """
            
            # حفظ ملف Puppeteer
            script_path = os.path.join(self.project_root, f'puppeteer_pdf_{delivery_order_id}.js')
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(puppeteer_script)
            
            print(f"✅ تم إنشاء ملف Puppeteer: {script_path}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف Puppeteer: {e}")
            return False
    
    def _run_puppeteer_script(self, delivery_order_id):
        """تنفيذ ملف Puppeteer"""
        try:
            script_path = os.path.join(self.project_root, f'puppeteer_pdf_{delivery_order_id}.js')
            
            print("🚀 تنفيذ Puppeteer...")
            
            # تنفيذ الملف
            result = subprocess.run(
                ['node', script_path],
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=120,  # مهلة زمنية 2 دقيقة
                cwd=self.project_root
            )
            
            # طباعة المخرجات
            if result.stdout:
                print("📋 مخرجات Puppeteer:")
                print(result.stdout)
            
            if result.stderr:
                print("⚠️ تحذيرات Puppeteer:")
                print(result.stderr)
            
            # تنظيف الملف المؤقت
            if os.path.exists(script_path):
                os.remove(script_path)
                print(f"🧹 تم حذف الملف المؤقت: {script_path}")
            
            if result.returncode == 0:
                print("✅ تم تنفيذ Puppeteer بنجاح!")
                return True
            else:
                print(f"❌ فشل Puppeteer مع كود الخروج: {result.returncode}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ انتهت مهلة Puppeteer")
            return False
        except Exception as e:
            print(f"❌ خطأ في تنفيذ Puppeteer: {e}")
            return False
    
    def check_puppeteer_availability(self):
        """فحص توفر Puppeteer"""
        try:
            # فحص Node.js
            node_result = subprocess.run(['node', '--version'], capture_output=True, timeout=5)
            if node_result.returncode != 0:
                return False, "Node.js غير متاح"
            
            # فحص Puppeteer
            puppeteer_check = subprocess.run(
                ['node', '-e', 'require("puppeteer"); console.log("Puppeteer متاح");'],
                capture_output=True,
                text=True,
                timeout=10,
                cwd=self.project_root
            )
            
            if puppeteer_check.returncode == 0:
                return True, "Puppeteer متاح وجاهز"
            else:
                return False, f"Puppeteer غير متاح: {puppeteer_check.stderr}"
                
        except Exception as e:
            return False, f"خطأ في فحص Puppeteer: {str(e)}"


# إنشاء instance عام للخدمة
puppeteer_pdf_service = PuppeteerPDFService()


def generate_perfect_pdf(delivery_order_id):
    """دالة مساعدة لإنشاء PDF مثالي"""
    return puppeteer_pdf_service.generate_perfect_pdf(delivery_order_id)


def check_puppeteer_tools():
    """فحص أدوات Puppeteer"""
    return puppeteer_pdf_service.check_puppeteer_availability()
