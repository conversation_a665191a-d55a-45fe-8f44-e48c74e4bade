-- نظام الأتمتة التلقائية لأوامر التسليم
-- Automated Delivery Orders System Database Schema

-- 1. إنشاء جدول سجل الأتمتة
CREATE TABLE automation_log (
    id NUMBER PRIMARY KEY,
    action VARCHAR2(100) NOT NULL,
    description CLOB,
    action_date DATE DEFAULT SYSDATE,
    system_user VARCHAR2(50) DEFAULT 'SYSTEM',
    shipment_id NUMBER,
    order_id NUMBER,
    agent_id NUMBER,
    execution_time NUMBER(10,3), -- وقت التنفيذ بالثواني
    status VARCHAR2(20) DEFAULT 'SUCCESS', -- SUCCESS, FAILED, WARNING
    error_message CLOB,
    
    -- فهرسة للبحث السريع
    created_at DATE DEFAULT SYSDATE
);

-- 2. إنشاء sequence للسجل
CREATE SEQUENCE automation_log_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 3. <PERSON><PERSON><PERSON><PERSON><PERSON> جدول إعدادات الأتمتة
CREATE TABLE automation_settings (
    id NUMBER PRIMARY KEY,
    setting_key VARCHAR2(100) UNIQUE NOT NULL,
    setting_value CLOB,
    setting_type VARCHAR2(50) DEFAULT 'STRING', -- STRING, NUMBER, BOOLEAN, JSON
    description VARCHAR2(500),
    is_active NUMBER(1) DEFAULT 1,
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE,
    updated_by NUMBER
);

-- 4. إنشاء sequence للإعدادات
CREATE SEQUENCE automation_settings_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 5. إنشاء جدول قواعد الأتمتة
CREATE TABLE automation_rules (
    id NUMBER PRIMARY KEY,
    rule_name VARCHAR2(200) NOT NULL,
    rule_type VARCHAR2(50) NOT NULL, -- STATUS_CHANGE, TIME_BASED, CONDITION_BASED
    trigger_condition CLOB, -- شروط تفعيل القاعدة (JSON)
    action_type VARCHAR2(50) NOT NULL, -- CREATE_ORDER, ASSIGN_AGENT, SEND_NOTIFICATION, UPDATE_PRIORITY
    action_parameters CLOB, -- معاملات الإجراء (JSON)
    priority NUMBER DEFAULT 1, -- أولوية تنفيذ القاعدة
    is_active NUMBER(1) DEFAULT 1,
    execution_count NUMBER DEFAULT 0,
    last_execution DATE,
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER,
    updated_at DATE,
    updated_by NUMBER
);

-- 6. إنشاء sequence لقواعد الأتمتة
CREATE SEQUENCE automation_rules_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 7. إنشاء جدول جدولة المهام التلقائية
CREATE TABLE automation_scheduled_tasks (
    id NUMBER PRIMARY KEY,
    task_name VARCHAR2(200) NOT NULL,
    task_type VARCHAR2(50) NOT NULL, -- DAILY, WEEKLY, MONTHLY, CUSTOM
    schedule_expression VARCHAR2(100), -- Cron expression
    next_execution DATE,
    last_execution DATE,
    execution_count NUMBER DEFAULT 0,
    is_active NUMBER(1) DEFAULT 1,
    task_parameters CLOB, -- معاملات المهمة (JSON)
    max_execution_time NUMBER DEFAULT 300, -- الحد الأقصى لوقت التنفيذ بالثواني
    created_at DATE DEFAULT SYSDATE,
    created_by NUMBER
);

-- 8. إنشاء sequence للمهام المجدولة
CREATE SEQUENCE automation_tasks_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 9. إنشاء جدول إحصائيات الأتمتة
CREATE TABLE automation_statistics (
    id NUMBER PRIMARY KEY,
    stat_date DATE DEFAULT SYSDATE,
    stat_type VARCHAR2(50) NOT NULL, -- DAILY, WEEKLY, MONTHLY
    
    -- إحصائيات الأوامر
    total_orders_created NUMBER DEFAULT 0,
    auto_orders_created NUMBER DEFAULT 0,
    orders_completed NUMBER DEFAULT 0,
    orders_overdue NUMBER DEFAULT 0,
    
    -- إحصائيات المخلصين
    total_agents NUMBER DEFAULT 0,
    active_agents NUMBER DEFAULT 0,
    avg_agent_rating NUMBER(3,2) DEFAULT 0,
    
    -- إحصائيات الأداء
    avg_completion_time NUMBER(5,2) DEFAULT 0,
    on_time_completion_rate NUMBER(5,2) DEFAULT 0,
    automation_success_rate NUMBER(5,2) DEFAULT 0,
    
    -- إحصائيات النظام
    total_automations_executed NUMBER DEFAULT 0,
    successful_automations NUMBER DEFAULT 0,
    failed_automations NUMBER DEFAULT 0,
    
    created_at DATE DEFAULT SYSDATE
);

-- 10. إنشاء sequence للإحصائيات
CREATE SEQUENCE automation_stats_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- 11. إنشاء فهارس للأداء
CREATE INDEX idx_automation_log_date ON automation_log(action_date);
CREATE INDEX idx_automation_log_action ON automation_log(action);
CREATE INDEX idx_automation_log_status ON automation_log(status);
CREATE INDEX idx_automation_rules_type ON automation_rules(rule_type);
CREATE INDEX idx_automation_rules_active ON automation_rules(is_active);
CREATE INDEX idx_automation_tasks_next ON automation_scheduled_tasks(next_execution);
CREATE INDEX idx_automation_stats_date ON automation_statistics(stat_date);

-- 12. إدراج الإعدادات الافتراضية
INSERT INTO automation_settings (id, setting_key, setting_value, setting_type, description) VALUES
(automation_settings_seq.NEXTVAL, 'AUTO_CREATE_ORDERS', 'true', 'BOOLEAN', 'تفعيل إنشاء أوامر التسليم تلقائياً');

INSERT INTO automation_settings (id, setting_key, setting_value, setting_type, description) VALUES
(automation_settings_seq.NEXTVAL, 'AUTO_ASSIGN_AGENTS', 'true', 'BOOLEAN', 'تفعيل تعيين المخلصين تلقائياً');

INSERT INTO automation_settings (id, setting_key, setting_value, setting_type, description) VALUES
(automation_settings_seq.NEXTVAL, 'AUTO_SEND_NOTIFICATIONS', 'true', 'BOOLEAN', 'تفعيل إرسال الإشعارات تلقائياً');

INSERT INTO automation_settings (id, setting_key, setting_value, setting_type, description) VALUES
(automation_settings_seq.NEXTVAL, 'AGENT_SELECTION_CRITERIA', '{"specialization_weight": 0.4, "rating_weight": 0.3, "workload_weight": 0.2, "location_weight": 0.1}', 'JSON', 'معايير اختيار المخلص الأمثل');

INSERT INTO automation_settings (id, setting_key, setting_value, setting_type, description) VALUES
(automation_settings_seq.NEXTVAL, 'PRIORITY_RULES', '{"urgent": ["medical", "food", "emergency"], "high": ["electronics", "automotive"], "normal": ["general", "textiles"], "low": ["raw_materials"]}', 'JSON', 'قواعد تحديد الأولوية');

-- 13. إدراج قواعد الأتمتة الافتراضية
INSERT INTO automation_rules (id, rule_name, rule_type, trigger_condition, action_type, action_parameters) VALUES
(automation_rules_seq.NEXTVAL, 'إنشاء أمر تسليم عند وصول الشحنة للميناء', 'STATUS_CHANGE', '{"old_status": "*", "new_status": "arrived_port"}', 'CREATE_ORDER', '{"auto_assign": true, "priority": "normal"}');

INSERT INTO automation_rules (id, rule_name, rule_type, trigger_condition, action_type, action_parameters) VALUES
(automation_rules_seq.NEXTVAL, 'رفع الأولوية عند بدء التخليص الجمركي', 'STATUS_CHANGE', '{"old_status": "*", "new_status": "customs_clearance"}', 'UPDATE_PRIORITY', '{"new_priority": "high"}');

INSERT INTO automation_rules (id, rule_name, rule_type, trigger_condition, action_type, action_parameters) VALUES
(automation_rules_seq.NEXTVAL, 'إشعار عاجل عند جاهزية التسليم', 'STATUS_CHANGE', '{"old_status": "*", "new_status": "ready_for_delivery"}', 'SEND_NOTIFICATION', '{"channels": ["SMS", "EMAIL"], "template": "ready_for_delivery"}');

-- 14. إدراج المهام المجدولة الافتراضية
INSERT INTO automation_scheduled_tasks (id, task_name, task_type, schedule_expression, next_execution) VALUES
(automation_tasks_seq.NEXTVAL, 'فحص الأوامر المتأخرة', 'DAILY', '0 8 * * *', SYSDATE + 1);

INSERT INTO automation_scheduled_tasks (id, task_name, task_type, schedule_expression, next_execution) VALUES
(automation_tasks_seq.NEXTVAL, 'تحديث تقييمات المخلصين', 'DAILY', '0 2 * * *', SYSDATE + 1);

INSERT INTO automation_scheduled_tasks (id, task_name, task_type, schedule_expression, next_execution) VALUES
(automation_tasks_seq.NEXTVAL, 'إرسال تذكيرات المواعيد النهائية', 'DAILY', '0 9 * * *', SYSDATE + 1);

INSERT INTO automation_scheduled_tasks (id, task_name, task_type, schedule_expression, next_execution) VALUES
(automation_tasks_seq.NEXTVAL, 'تنظيف البيانات القديمة', 'WEEKLY', '0 1 * * 0', SYSDATE + 7);

INSERT INTO automation_scheduled_tasks (id, task_name, task_type, schedule_expression, next_execution) VALUES
(automation_tasks_seq.NEXTVAL, 'إنشاء تقرير الأداء الشهري', 'MONTHLY', '0 6 1 * *', ADD_MONTHS(SYSDATE, 1));

-- 15. إنشاء view شامل لإحصائيات الأتمتة
CREATE OR REPLACE VIEW v_automation_dashboard AS
SELECT 
    -- إحصائيات اليوم
    (SELECT COUNT(*) FROM automation_log WHERE TRUNC(action_date) = TRUNC(SYSDATE)) as today_automations,
    (SELECT COUNT(*) FROM automation_log WHERE TRUNC(action_date) = TRUNC(SYSDATE) AND status = 'SUCCESS') as today_successful,
    (SELECT COUNT(*) FROM automation_log WHERE TRUNC(action_date) = TRUNC(SYSDATE) AND status = 'FAILED') as today_failed,
    
    -- إحصائيات الأوامر
    (SELECT COUNT(*) FROM delivery_orders WHERE TRUNC(created_date) = TRUNC(SYSDATE) AND created_by = 1) as today_auto_orders,
    (SELECT COUNT(*) FROM delivery_orders WHERE order_status IN ('draft', 'sent', 'in_progress')) as active_orders,
    (SELECT COUNT(*) FROM delivery_orders WHERE expected_completion_date < SYSDATE AND order_status NOT IN ('completed', 'cancelled')) as overdue_orders,
    
    -- إحصائيات المخلصين
    (SELECT COUNT(*) FROM customs_agents WHERE is_active = 1) as active_agents,
    (SELECT ROUND(AVG(rating), 2) FROM customs_agents WHERE is_active = 1) as avg_agent_rating,
    
    -- إحصائيات الأداء
    (SELECT COUNT(*) FROM delivery_orders WHERE order_status = 'completed' AND TRUNC(actual_completion_date) = TRUNC(SYSDATE)) as today_completions,
    (SELECT ROUND(AVG(actual_completion_date - created_date), 2) FROM delivery_orders WHERE order_status = 'completed' AND actual_completion_date >= SYSDATE - 30) as avg_completion_days
FROM DUAL;

-- 16. إنشاء triggers للتسجيل التلقائي
CREATE OR REPLACE TRIGGER trg_delivery_order_automation
    AFTER INSERT OR UPDATE ON delivery_orders
    FOR EACH ROW
BEGIN
    -- تسجيل إنشاء أمر تسليم تلقائي
    IF INSERTING AND :NEW.created_by = 1 THEN
        INSERT INTO automation_log (id, action, description, shipment_id, order_id)
        VALUES (automation_log_seq.NEXTVAL, 'AUTO_ORDER_CREATED', 
                'تم إنشاء أمر تسليم تلقائي رقم ' || :NEW.order_number,
                :NEW.shipment_id, :NEW.id);
    END IF;
    
    -- تسجيل تحديث الحالة
    IF UPDATING AND NVL(:OLD.order_status, 'NULL') != NVL(:NEW.order_status, 'NULL') THEN
        INSERT INTO automation_log (id, action, description, order_id)
        VALUES (automation_log_seq.NEXTVAL, 'ORDER_STATUS_UPDATED',
                'تم تحديث حالة الأمر ' || :NEW.order_number || ' من ' || 
                NVL(:OLD.order_status, 'غير محدد') || ' إلى ' || NVL(:NEW.order_status, 'غير محدد'),
                :NEW.id);
    END IF;
END;
/

COMMIT;
