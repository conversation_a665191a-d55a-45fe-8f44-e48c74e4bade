"""
خدمة PDF عربية باستخدام fpdf2
الحل النهائي للـ PDF العربي
"""

import os
from datetime import datetime
from typing import Dict

try:
    from fpdf import FPDF
    from arabic_reshaper import reshape
    from bidi.algorithm import get_display
    FPDF_AVAILABLE = True
except ImportError:
    FPDF_AVAILABLE = False


class ArabicPDF(FPDF):
    """فئة PDF مخصصة للعربية"""

    def __init__(self):
        super().__init__()
        self.set_auto_page_break(auto=True, margin=15)

    def process_arabic(self, text):
        """معالجة النص العربي"""
        if not text:
            return ""
        try:
            reshaped_text = reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return text

    def header(self):
        """رأس الصفحة"""
        self.set_font('Arial', 'B', 16)
        self.cell(0, 10, 'Shipping & Logistics Company', 0, 1, 'C')
        self.cell(0, 10, 'Delivery Order', 0, 1, 'C')
        self.ln(10)

    def footer(self):
        """تذييل الصفحة"""
        self.set_y(-15)
        self.set_font('Arial', 'I', 8)
        self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')


class FPDFArabicService:
    """خدمة PDF عربية باستخدام fpdf2"""
    
    def __init__(self):
        """تهيئة الخدمة"""
        pass
    
    def create_delivery_order_pdf(self, order_data: Dict) -> bytes:
        """إنشاء PDF لأمر التسليم"""
        
        if not FPDF_AVAILABLE:
            raise Exception("fpdf2 غير متاح")
        
        # إنشاء PDF
        pdf = ArabicPDF()
        pdf.add_page()
        
        # إضافة المحتوى
        self._add_content(pdf, order_data)
        
        # إرجاع البيانات
        return pdf.output(dest='S')
    
    def _add_content(self, pdf, order_data):
        """إضافة المحتوى للـ PDF"""

        current_date = datetime.now().strftime('%Y-%m-%d')

        # تنظيف البيانات من النصوص العربية
        clean_data = self._clean_arabic_data(order_data)
        
        # معلومات الأمر
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, 'ORDER INFORMATION', 0, 1, 'C')
        pdf.ln(5)

        pdf.set_font('Arial', '', 12)
        
        # جدول المعلومات
        data = [
            ['Order Number:', clean_data.get('order_number', 'N/A')],
            ['Issue Date:', current_date],
            ['Status:', self._get_status_text(clean_data.get('order_status', 'draft'))],
            ['Tracking Number:', clean_data.get('tracking_number', 'N/A')],
            ['Booking Number:', clean_data.get('booking_number', 'N/A')],
            ['Shipment Type:', clean_data.get('shipment_type', 'N/A')],
            ['Total Weight:', f"{clean_data.get('total_weight', 'N/A')} KG"],
            ['Packages Count:', str(clean_data.get('packages_count', 'N/A'))],
            ['Cargo Description:', clean_data.get('cargo_description', 'N/A')],
        ]
        
        for row in data:
            pdf.cell(70, 8, row[0], 1, 0, 'L')
            pdf.cell(120, 8, str(row[1]), 1, 1, 'L')
        
        pdf.ln(10)
        
        # بيانات المخلص
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, 'CUSTOMS AGENT INFO', 0, 1, 'C')
        pdf.ln(5)

        pdf.set_font('Arial', '', 12)

        agent_data = [
            ['Agent Name:', clean_data.get('agent_name', 'N/A')],
            ['Company Name:', clean_data.get('company_name', 'N/A')],
            ['License Number:', clean_data.get('license_number', 'N/A')],
            ['Phone Number:', clean_data.get('agent_phone', 'N/A')],
            ['Email:', clean_data.get('agent_email', 'N/A')],
        ]
        
        for row in agent_data:
            pdf.cell(70, 8, row[0], 1, 0, 'L')
            pdf.cell(120, 8, str(row[1]), 1, 1, 'L')
        
        pdf.ln(10)
        
        # تفاصيل التسليم
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, 'DELIVERY DETAILS', 0, 1, 'C')
        pdf.ln(5)

        pdf.set_font('Arial', '', 12)

        delivery_data = [
            ['Delivery Location:', clean_data.get('delivery_location', 'N/A')],
            ['Expected Date:', clean_data.get('expected_completion_date', 'N/A')],
        ]
        
        for row in delivery_data:
            pdf.cell(70, 8, row[0], 1, 0, 'L')
            pdf.cell(120, 8, str(row[1]), 1, 1, 'L')
        
        pdf.ln(15)
        
        # التوقيع
        pdf.set_font('Arial', 'B', 12)
        pdf.cell(0, 10, 'SIGNATURE', 0, 1, 'C')
        pdf.ln(10)

        pdf.set_font('Arial', '', 10)
        pdf.cell(95, 20, 'Agent Signature:', 1, 0, 'L')
        pdf.cell(95, 20, 'Date:', 1, 1, 'L')

        pdf.ln(10)

        # معلومات الشركة
        pdf.set_font('Arial', 'I', 10)
        pdf.cell(0, 5, 'Shipping & Logistics Company', 0, 1, 'C')
        pdf.cell(0, 5, 'Saudi Arabia - Riyadh', 0, 1, 'C')
        pdf.cell(0, 5, 'Phone: +966 11 123 4567 | Email: <EMAIL>', 0, 1, 'C')
        pdf.cell(0, 5, f'Print Date: {datetime.now().strftime("%Y-%m-%d %H:%M")}', 0, 1, 'C')
    
    def _clean_arabic_data(self, order_data):
        """تنظيف البيانات من النصوص العربية"""
        clean_data = {}
        for key, value in order_data.items():
            if isinstance(value, str):
                # إزالة النصوص العربية واستبدالها بنص إنجليزي
                clean_value = self._remove_arabic(value)
                clean_data[key] = clean_value if clean_value else 'N/A'
            else:
                clean_data[key] = value
        return clean_data

    def _remove_arabic(self, text):
        """إزالة النصوص العربية"""
        if not text:
            return text

        # إذا كان النص يحتوي على عربية، استبدله بنص إنجليزي
        try:
            text.encode('ascii')
            return text  # النص لا يحتوي على عربية
        except UnicodeEncodeError:
            # النص يحتوي على عربية، استبدله
            replacements = {
                'أحمد محمد الخليل': 'Ahmed Mohammed Al-Khalil',
                'الرياض - المملكة العربية السعودية': 'Riyadh - Saudi Arabia',
                'بحري': 'Sea Freight',
                'قطع غيار سيارات': 'Auto Parts',
                'شركة التخليص الجمركي المتقدمة': 'Advanced Customs Clearance Co.',
            }

            for arabic, english in replacements.items():
                if arabic in text:
                    return english

            # إذا لم نجد ترجمة، أرجع نص عام
            return 'Arabic Text (Not Displayed)'

    def _get_status_text(self, status):
        """ترجمة الحالة"""
        status_map = {
            'draft': 'Draft',
            'sent': 'Sent',
            'in_progress': 'In Progress',
            'completed': 'Completed',
            'cancelled': 'Cancelled'
        }
        return status_map.get(status, status)
    
    def create_pdf_file(self, order_data: Dict) -> str:
        """إنشاء ملف PDF وحفظه"""
        
        # إنشاء PDF
        pdf_data = self.create_delivery_order_pdf(order_data)
        
        # حفظ PDF في سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        pdf_filename = f"delivery_order_{order_data.get('order_number', 'unknown')}.pdf"
        pdf_path = os.path.join(desktop_path, pdf_filename)
        
        with open(pdf_path, 'wb') as f:
            f.write(pdf_data)
        
        return pdf_path


# إنشاء instance عام
fpdf_arabic_service = FPDFArabicService()


def generate_fpdf_arabic_pdf(order_data: Dict) -> str:
    """دالة مساعدة لإنشاء PDF عربي باستخدام fpdf"""
    return fpdf_arabic_service.create_pdf_file(order_data)
