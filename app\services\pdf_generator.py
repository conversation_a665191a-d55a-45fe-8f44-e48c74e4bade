# -*- coding: utf-8 -*-
"""
مولد PDF لأوامر التسليم
PDF Generator for Delivery Orders with Arabic Support
"""

import os
import sys
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display

# إضافة مسار قاعدة البيانات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database_manager import DatabaseManager

class DeliveryOrderPDFGenerator:
    """مولد PDF لأوامر التسليم مع دعم العربية"""
    
    def __init__(self):
        """تهيئة مولد PDF"""
        self.setup_arabic_fonts()
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'pdf')
        os.makedirs(self.output_dir, exist_ok=True)
    
    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # تسجيل خط عربي
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # مسار الخط المُحمل
            font_path = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'fonts', 'NotoSansArabic-Regular.ttf')

            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    print(f"✅ تم تسجيل الخط العربي: {font_path}")
                    self.arabic_font_available = True
                    return
                except Exception as e:
                    print(f"❌ فشل تسجيل الخط المُحمل: {e}")

            # محاولة استخدام خطوط النظام
            system_fonts = [
                'C:/Windows/Fonts/arial.ttf',
                'C:/Windows/Fonts/tahoma.ttf',
                'C:/Windows/Fonts/calibri.ttf',
                'C:/Windows/Fonts/segoeui.ttf'
            ]

            for font_path in system_fonts:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('Arabic', font_path))
                        print(f"✅ تم تسجيل خط النظام: {font_path}")
                        self.arabic_font_available = True
                        return
                    except Exception as e:
                        continue

            print("⚠️ لم يتم العثور على خط عربي مناسب")
            self.arabic_font_available = False

        except Exception as e:
            print(f"خطأ في إعداد الخطوط: {e}")
            self.arabic_font_available = False
    
    def reshape_arabic_text(self, text):
        """إعادة تشكيل النص العربي للعرض الصحيح"""
        try:
            if text and any('\u0600' <= char <= '\u06FF' for char in text):
                reshaped_text = arabic_reshaper.reshape(str(text))
                return get_display(reshaped_text)
            return str(text) if text else ""
        except Exception as e:
            print(f"خطأ في معالجة النص العربي: {e}")
            return str(text) if text else ""
    
    def generate_delivery_order_pdf(self, delivery_order_id):
        """إنشاء PDF لأمر التسليم"""
        try:
            # جلب بيانات أمر التسليم
            order_data = self._get_delivery_order_data(delivery_order_id)
            if not order_data:
                return None, "أمر التسليم غير موجود"
            
            # إنشاء اسم الملف
            filename = f"delivery_order_{order_data['order_number']}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # إنشاء المستند
            doc = SimpleDocTemplate(
                filepath,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            
            # إنشاء المحتوى
            story = []
            
            # إضافة الهيدر
            self._add_header(story, order_data)
            
            # إضافة معلومات أمر التسليم
            self._add_order_info(story, order_data)
            
            # إضافة معلومات الشحنة
            self._add_shipment_info(story, order_data)
            
            # إضافة معلومات المخلص
            self._add_agent_info(story, order_data)
            
            # إضافة الفوتر
            self._add_footer(story, order_data)
            
            # بناء المستند
            doc.build(story)
            
            return filepath, "تم إنشاء PDF بنجاح"
            
        except Exception as e:
            return None, f"خطأ في إنشاء PDF: {str(e)}"
    
    def _get_delivery_order_data(self, delivery_order_id):
        """جلب بيانات أمر التسليم من قاعدة البيانات"""
        try:
            db_manager = DatabaseManager()
            
            query = """
                SELECT
                    do.id,
                    do.order_number,
                    do.shipment_id,
                    do.customs_agent_id,
                    ca.agent_name,
                    ca.phone,
                    ca.mobile,
                    ca.email,
                    do.branch_id,
                    b.brn_lname as branch_name,
                    b.brn_ladd as branch_address,
                    do.created_date,
                    do.order_status,
                    cs.shipment_number,
                    cs.port_of_loading,
                    cs.port_of_discharge,
                    cs.shipment_status,
                    cc.container_number,
                    cc.container_type,
                    cc.seal_number,
                    cc.total_weight,
                    cc.net_weight
                FROM delivery_orders do
                LEFT JOIN customs_agents ca ON do.customs_agent_id = ca.id
                LEFT JOIN branches b ON do.branch_id = b.brn_no
                LEFT JOIN cargo_shipments cs ON do.shipment_id = cs.id
                LEFT JOIN cargo_containers cc ON cs.id = cc.cargo_shipment_id
                WHERE do.id = :delivery_order_id
            """
            
            result = db_manager.execute_query(query, {'delivery_order_id': delivery_order_id})
            
            if result:
                row = result[0]
                return {
                    'id': row[0],
                    'order_number': row[1],
                    'shipment_id': row[2],
                    'customs_agent_id': row[3],
                    'agent_name': row[4],
                    'agent_phone': row[5],
                    'agent_mobile': row[6],
                    'agent_email': row[7],
                    'branch_id': row[8],
                    'branch_name': row[9],
                    'branch_address': row[10],
                    'created_date': row[11],
                    'order_status': row[12],
                    'shipment_reference': row[13],
                    'origin_port': row[14],
                    'destination_port': row[15],
                    'shipment_status': row[16],
                    'container_number': row[17],
                    'container_type': row[18],
                    'seal_number': row[19],
                    'total_weight': row[20],
                    'net_weight': row[21]
                }
            
            db_manager.close()
            return None
            
        except Exception as e:
            print(f"خطأ في جلب بيانات أمر التسليم: {e}")
            return None
    
    def _add_header(self, story, order_data):
        """إضافة هيدر المستند"""
        # تحديد الخط المناسب
        font_name = 'Arabic' if hasattr(self, 'arabic_font_available') and self.arabic_font_available else 'Helvetica'

        # عنوان الشركة
        company_style = ParagraphStyle(
            'CompanyTitle',
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.darkblue,
            fontName=font_name
        )

        company_name = self.reshape_arabic_text("شركة النقل والشحن المتطورة")
        story.append(Paragraph(company_name, company_style))

        # عنوان أمر التسليم
        title_style = ParagraphStyle(
            'Title',
            fontSize=16,
            alignment=TA_CENTER,
            spaceAfter=30,
            textColor=colors.red,
            fontName=font_name
        )

        title = self.reshape_arabic_text("أمر تسليم - Delivery Order")
        story.append(Paragraph(title, title_style))

        story.append(Spacer(1, 20))
    
    def _add_order_info(self, story, order_data):
        """إضافة معلومات أمر التسليم"""
        order_info_data = [
            [self.reshape_arabic_text("رقم الأمر"), order_data['order_number']],
            [self.reshape_arabic_text("تاريخ الإنشاء"), str(order_data['created_date'])],
            [self.reshape_arabic_text("حالة الأمر"), self.reshape_arabic_text(order_data['order_status'])],
            [self.reshape_arabic_text("رقم الشحنة"), str(order_data['shipment_id'])],
        ]
        
        if order_data['shipment_reference']:
            order_info_data.append([
                self.reshape_arabic_text("مرجع الشحنة"), 
                order_data['shipment_reference']
            ])
        
        order_table = Table(order_info_data, colWidths=[4*cm, 6*cm])
        # تحديد الخط المناسب
        font_name = 'Arabic' if hasattr(self, 'arabic_font_available') and self.arabic_font_available else 'Helvetica'

        order_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(order_table)
        story.append(Spacer(1, 20))
    
    def _add_shipment_info(self, story, order_data):
        """إضافة معلومات الشحنة"""
        # تحديد الخط المناسب
        font_name = 'Arabic' if hasattr(self, 'arabic_font_available') and self.arabic_font_available else 'Helvetica'

        # عنوان قسم الشحنة
        section_style = ParagraphStyle(
            'SectionTitle',
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=10,
            textColor=colors.darkblue,
            fontName=font_name
        )

        section_title = self.reshape_arabic_text("معلومات الشحنة")
        story.append(Paragraph(section_title, section_style))
        
        shipment_data = [
            [self.reshape_arabic_text("ميناء المنشأ"), order_data['origin_port'] or "غير محدد"],
            [self.reshape_arabic_text("ميناء الوصول"), order_data['destination_port'] or "غير محدد"],
            [self.reshape_arabic_text("حالة الشحنة"), self.reshape_arabic_text(order_data['shipment_status'] or "غير محدد")],
            [self.reshape_arabic_text("رقم الحاوية"), order_data['container_number'] or "غير محدد"],
            [self.reshape_arabic_text("نوع الحاوية"), order_data['container_type'] or "غير محدد"],
            [self.reshape_arabic_text("رقم الختم"), order_data['seal_number'] or "غير محدد"],
            [self.reshape_arabic_text("الوزن الإجمالي"), f"{order_data['total_weight'] or 0} كيلو"],
            [self.reshape_arabic_text("الوزن الصافي"), f"{order_data['net_weight'] or 0} كيلو"],
        ]
        
        shipment_table = Table(shipment_data, colWidths=[4*cm, 8*cm])
        shipment_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('BACKGROUND', (1, 0), (1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(shipment_table)
        story.append(Spacer(1, 20))
    
    def _add_agent_info(self, story, order_data):
        """إضافة معلومات المخلص"""
        # تحديد الخط المناسب
        font_name = 'Arabic' if hasattr(self, 'arabic_font_available') and self.arabic_font_available else 'Helvetica'

        section_style = ParagraphStyle(
            'SectionTitle',
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=10,
            textColor=colors.darkgreen,
            fontName=font_name
        )

        section_title = self.reshape_arabic_text("معلومات المخلص الجمركي")
        story.append(Paragraph(section_title, section_style))
        
        agent_data = [
            [self.reshape_arabic_text("اسم المخلص"), self.reshape_arabic_text(order_data['agent_name'] or "غير محدد")],
            [self.reshape_arabic_text("الهاتف"), order_data['agent_phone'] or "غير محدد"],
            [self.reshape_arabic_text("الجوال"), order_data['agent_mobile'] or "غير محدد"],
            [self.reshape_arabic_text("البريد الإلكتروني"), order_data['agent_email'] or "غير محدد"],
            [self.reshape_arabic_text("الفرع"), self.reshape_arabic_text(order_data['branch_name'] or "غير محدد")],
            [self.reshape_arabic_text("عنوان الفرع"), self.reshape_arabic_text(order_data['branch_address'] or "غير محدد")],
        ]
        
        agent_table = Table(agent_data, colWidths=[4*cm, 8*cm])
        agent_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgreen),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('BACKGROUND', (1, 0), (1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(agent_table)
        story.append(Spacer(1, 30))
    
    def _add_footer(self, story, order_data):
        """إضافة فوتر المستند"""
        # تحديد الخط المناسب
        font_name = 'Arabic' if hasattr(self, 'arabic_font_available') and self.arabic_font_available else 'Helvetica'

        # ملاحظات
        notes_style = ParagraphStyle(
            'Notes',
            fontSize=10,
            alignment=TA_RIGHT,
            spaceAfter=20,
            fontName=font_name
        )

        notes_text = self.reshape_arabic_text(
            "ملاحظات: يرجى مراجعة جميع البيانات والتأكد من صحتها قبل البدء في إجراءات التخليص الجمركي."
        )
        story.append(Paragraph(notes_text, notes_style))
        
        # توقيع
        signature_data = [
            [self.reshape_arabic_text("توقيع المخلص"), ""],
            [self.reshape_arabic_text("التاريخ"), ""],
            [self.reshape_arabic_text("الختم"), ""],
        ]
        
        signature_table = Table(signature_data, colWidths=[3*cm, 6*cm])
        signature_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(signature_table)
        
        # معلومات النظام
        system_style = ParagraphStyle(
            'SystemInfo',
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.grey,
            fontName=font_name
        )
        
        system_text = f"تم إنشاء هذا المستند تلقائياً بواسطة نظام إدارة الشحنات - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        story.append(Spacer(1, 20))
        story.append(Paragraph(system_text, system_style))


# إنشاء instance عام للمولد
pdf_generator = DeliveryOrderPDFGenerator()


def generate_delivery_order_pdf(delivery_order_id):
    """دالة مساعدة لإنشاء PDF أمر التسليم"""
    return pdf_generator.generate_delivery_order_pdf(delivery_order_id)
