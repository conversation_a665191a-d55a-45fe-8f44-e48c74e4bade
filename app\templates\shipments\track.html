{% extends "base.html" %}

{% block content %}
<style>
.tracking-timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    position: relative;
    padding: 20px 0 20px 60px;
    border-left: 3px solid #e9ecef;
}

.timeline-item:last-child {
    border-left: 3px solid transparent;
}

.timeline-item.active {
    border-left-color: #28a745;
}

.timeline-item.completed {
    border-left-color: #007bff;
}

.timeline-icon {
    position: absolute;
    left: -12px;
    top: 25px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
}

.timeline-icon.active {
    background: #28a745;
    animation: pulse 2s infinite;
}

.timeline-icon.completed {
    background: #007bff;
}

.shipment-map {
    height: 400px;
    border-radius: 10px;
    background: #f8f9fa;
}

.qr-code-container {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

.tracking-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.tracking-number {
    font-family: 'Courier New', monospace;
    font-size: 2rem;
    font-weight: bold;
}

.status-badge {
    font-size: 1.1rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
}
</style>

<div class="container-fluid">
    {% if shipment %}
    <!-- Header -->
    <div class="tracking-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-search me-2"></i>
                    تتبع الشحنة
                </h1>
                <div class="tracking-number">{{ shipment.tracking_number }}</div>
                <p class="mb-0 mt-2">من {{ shipment.sender_name }} إلى {{ shipment.recipient_name }}</p>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge status-badge bg-{{ 'success' if shipment.status == 'تم التسليم' else 'warning' if shipment.status == 'في الطريق' else 'info' if shipment.status == 'معلق' else 'primary' }}">
                    {{ shipment.status }}
                </span>
                <div class="mt-2">
                    <small>آخر تحديث: {{ shipment.last_update or 'غير محدد' }}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Timeline -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-route me-2"></i>
                        مسار الشحنة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="tracking-timeline">
                        {% for event in tracking_events %}
                        <div class="timeline-item {{ 'completed' if event.completed else 'active' if event.current else '' }}">
                            <div class="timeline-icon {{ 'completed' if event.completed else 'active' if event.current else '' }}">
                                <i class="fas {{ event.icon }}"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">{{ event.title }}</h6>
                                <p class="text-muted mb-1">{{ event.description }}</p>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ event.timestamp }}
                                    {% if event.location %}
                                    <i class="fas fa-map-marker-alt me-1 ms-3"></i>
                                    {{ event.location }}
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Shipment Details -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل الشحنة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">معلومات المرسل</h6>
                            <p class="mb-1"><strong>الاسم:</strong> {{ shipment.sender_name }}</p>
                            <p class="mb-1"><strong>الهاتف:</strong> {{ shipment.sender_phone }}</p>
                            <p class="mb-1"><strong>المدينة:</strong> {{ shipment.sender_city }}</p>
                            <p class="mb-3"><strong>العنوان:</strong> {{ shipment.sender_address }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">معلومات المستقبل</h6>
                            <p class="mb-1"><strong>الاسم:</strong> {{ shipment.recipient_name }}</p>
                            <p class="mb-1"><strong>الهاتف:</strong> {{ shipment.recipient_phone }}</p>
                            <p class="mb-1"><strong>المدينة:</strong> {{ shipment.recipient_city }}</p>
                            <p class="mb-3"><strong>العنوان:</strong> {{ shipment.recipient_address }}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <p class="mb-1"><strong>الوزن:</strong> {{ shipment.weight }} كجم</p>
                        </div>
                        <div class="col-md-3">
                            <p class="mb-1"><strong>نوع الشحنة:</strong> {{ shipment.shipment_type }}</p>
                        </div>
                        <div class="col-md-3">
                            <p class="mb-1"><strong>الأولوية:</strong> {{ shipment.priority }}</p>
                        </div>
                        <div class="col-md-3">
                            <p class="mb-1"><strong>التكلفة:</strong> {{ shipment.total_cost }} ريال</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Live Map -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-map text-primary me-2"></i>
                        الموقع الحالي
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div id="shipmentMap" class="shipment-map"></div>
                </div>
            </div>

            <!-- QR Code -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-qrcode text-info me-2"></i>
                        رمز QR للشحنة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="qr-code-container">
                        {% if shipment.qr_code %}
                        <img src="{{ shipment.qr_code }}" alt="QR Code" class="img-fluid mb-3" style="max-width: 200px;">
                        {% else %}
                        <div class="text-muted">
                            <i class="fas fa-qrcode mb-2" style="font-size: 3rem;"></i>
                            <p>رمز QR غير متاح</p>
                        </div>
                        {% endif %}
                        <button class="btn btn-outline-primary btn-sm" onclick="generateQR()">
                            <i class="fas fa-download me-1"></i>
                            تحميل QR Code
                        </button>
                    </div>
                </div>
            </div>

            <!-- Estimated Delivery -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-clock text-warning me-2"></i>
                        معلومات التسليم
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">التسليم المتوقع</small>
                        <div class="h6">{{ shipment.expected_delivery_date or 'غير محدد' }}</div>
                    </div>
                    {% if shipment.actual_delivery_date %}
                    <div class="mb-3">
                        <small class="text-muted">التسليم الفعلي</small>
                        <div class="h6 text-success">{{ shipment.actual_delivery_date }}</div>
                    </div>
                    {% endif %}
                    <div class="mb-3">
                        <small class="text-muted">الموقع الحالي</small>
                        <div class="h6">{{ shipment.current_location or 'غير محدد' }}</div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-cogs text-secondary me-2"></i>
                        إجراءات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="shareTracking()">
                            <i class="fas fa-share me-2"></i>
                            مشاركة رابط التتبع
                        </button>
                        <button class="btn btn-outline-info" onclick="printLabel()">
                            <i class="fas fa-print me-2"></i>
                            طباعة ملصق الشحنة
                        </button>
                        <button class="btn btn-outline-success" onclick="sendNotification()">
                            <i class="fas fa-bell me-2"></i>
                            إرسال إشعار للمستقبل
                        </button>
                        {% if shipment.status != 'تم التسليم' %}
                        <button class="btn btn-outline-warning" onclick="updateStatus()">
                            <i class="fas fa-edit me-2"></i>
                            تحديث الحالة
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% else %}
    <!-- No Shipment Found -->
    <div class="text-center py-5">
        <i class="fas fa-search text-muted mb-3" style="font-size: 4rem;"></i>
        <h3 class="text-muted">الشحنة غير موجودة</h3>
        <p class="text-muted">رقم التتبع المدخل غير صحيح أو الشحنة غير موجودة في النظام</p>
        <a href="{{ url_for('shipments.index') }}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة الرئيسية
        </a>
    </div>
    {% endif %}
</div>

<script>
// تهيئة الخريطة
function initMap() {
    {% if shipment and shipment.current_latitude and shipment.current_longitude %}
    const map = new google.maps.Map(document.getElementById('shipmentMap'), {
        zoom: 13,
        center: { lat: {{ shipment.current_latitude }}, lng: {{ shipment.current_longitude }} }
    });

    new google.maps.Marker({
        position: { lat: {{ shipment.current_latitude }}, lng: {{ shipment.current_longitude }} },
        map: map,
        title: 'الموقع الحالي للشحنة'
    });
    {% else %}
    document.getElementById('shipmentMap').innerHTML = `
        <div class="d-flex align-items-center justify-content-center h-100 text-muted">
            <div class="text-center">
                <i class="fas fa-map-marked-alt mb-2" style="font-size: 2rem;"></i>
                <p class="mb-0">الموقع غير متاح</p>
            </div>
        </div>
    `;
    {% endif %}
}

// مشاركة رابط التتبع
function shareTracking() {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
        alert('تم نسخ رابط التتبع إلى الحافظة');
    });
}

// طباعة ملصق الشحنة
function printLabel() {
    window.print();
}

// إرسال إشعار
function sendNotification() {
    alert('سيتم إرسال إشعار للمستقبل');
}

// تحديث الحالة
function updateStatus() {
    alert('نافذة تحديث الحالة - قريباً');
}

// إنشاء QR Code
function generateQR() {
    alert('تحميل QR Code - قريباً');
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initMap();
    
    // تحديث تلقائي كل 30 ثانية
    setInterval(() => {
        location.reload();
    }, 30000);
});
</script>

{% endblock %}
