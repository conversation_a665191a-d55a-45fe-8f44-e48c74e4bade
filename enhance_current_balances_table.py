#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحسين جدول CURRENT_BALANCES بإضافة أعمدة مهمة
Enhance CURRENT_BALANCES Table with Important Columns
"""

import sys
sys.path.append('.')
from oracle_manager import oracle_manager

def analyze_current_table_structure():
    """تحليل بنية الجدول الحالية"""
    
    print("🔍 تحليل بنية جدول CURRENT_BALANCES الحالية")
    print("=" * 60)
    
    try:
        # فحص الأعمدة الموجودة
        current_columns = oracle_manager.execute_query("""
            SELECT COLUMN_NAME, DATA_TYPE, NULLABLE, DATA_LENGTH
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = 'CURRENT_BALANCES'
            ORDER BY COLUMN_ID
        """)
        
        if current_columns:
            print("📋 الأعمدة الموجودة حالياً:")
            for col in current_columns:
                nullable = "NULL" if col[2] == "Y" else "NOT NULL"
                print(f"   📌 {col[0]} | {col[1]} | {nullable}")
        
        # فحص البيانات الموجودة
        data_count = oracle_manager.execute_query("""
            SELECT COUNT(*) FROM CURRENT_BALANCES
        """)
        
        if data_count:
            print(f"\n📊 عدد السجلات الموجودة: {data_count[0][0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحليل: {e}")
        return False

def add_new_columns():
    """إضافة الأعمدة الجديدة"""
    
    print("\n🔧 إضافة الأعمدة الجديدة")
    print("=" * 60)
    
    try:
        # 1. إضافة عمود تاريخ الوثيقة
        print("📅 إضافة عمود DOCUMENT_DATE...")
        try:
            oracle_manager.execute_update("""
                ALTER TABLE CURRENT_BALANCES 
                ADD DOCUMENT_DATE DATE
            """)
            print("   ✅ تم إضافة عمود DOCUMENT_DATE")
        except Exception as e:
            if "ORA-01430" in str(e):
                print("   ℹ️ عمود DOCUMENT_DATE موجود بالفعل")
            else:
                print(f"   ❌ خطأ في إضافة DOCUMENT_DATE: {e}")
        
        # 2. إضافة عمود الشهر
        print("📅 إضافة عمود DOCUMENT_MONTH...")
        try:
            oracle_manager.execute_update("""
                ALTER TABLE CURRENT_BALANCES 
                ADD DOCUMENT_MONTH VARCHAR2(7)
            """)
            print("   ✅ تم إضافة عمود DOCUMENT_MONTH")
        except Exception as e:
            if "ORA-01430" in str(e):
                print("   ℹ️ عمود DOCUMENT_MONTH موجود بالفعل")
            else:
                print(f"   ❌ خطأ في إضافة DOCUMENT_MONTH: {e}")
        
        # 3. إضافة عمود الوصف/البيان
        print("📝 إضافة عمود DESCRIPTION...")
        try:
            oracle_manager.execute_update("""
                ALTER TABLE CURRENT_BALANCES 
                ADD DESCRIPTION VARCHAR2(500)
            """)
            print("   ✅ تم إضافة عمود DESCRIPTION")
        except Exception as e:
            if "ORA-01430" in str(e):
                print("   ℹ️ عمود DESCRIPTION موجود بالفعل")
            else:
                print(f"   ❌ خطأ في إضافة DESCRIPTION: {e}")
        
        # 4. إضافة فهارس للبحث السريع
        print("🔍 إضافة فهارس للبحث السريع...")
        
        indexes_to_create = [
            ("IDX_CB_DOCUMENT_DATE", "DOCUMENT_DATE"),
            ("IDX_CB_DOCUMENT_MONTH", "DOCUMENT_MONTH"),
            ("IDX_CB_DESCRIPTION", "DESCRIPTION")
        ]
        
        for index_name, column_name in indexes_to_create:
            try:
                oracle_manager.execute_update(f"""
                    CREATE INDEX {index_name} ON CURRENT_BALANCES ({column_name})
                """)
                print(f"   ✅ تم إنشاء فهرس {index_name}")
            except Exception as e:
                if "ORA-00955" in str(e):
                    print(f"   ℹ️ فهرس {index_name} موجود بالفعل")
                else:
                    print(f"   ❌ خطأ في إنشاء فهرس {index_name}: {e}")
        
        oracle_manager.commit()
        print("\n✅ تم إضافة جميع الأعمدة والفهارس بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الأعمدة: {e}")
        oracle_manager.rollback()
        return False

def update_existing_data():
    """تحديث البيانات الموجودة"""
    
    print("\n🔄 تحديث البيانات الموجودة")
    print("=" * 60)
    
    try:
        # تحديث البيانات الموجودة بناءً على last_transaction_date
        print("📅 تحديث تواريخ الوثائق...")
        
        oracle_manager.execute_update("""
            UPDATE CURRENT_BALANCES 
            SET DOCUMENT_DATE = NVL(last_transaction_date, created_at),
                DOCUMENT_MONTH = TO_CHAR(NVL(last_transaction_date, created_at), 'YYYY-MM')
            WHERE DOCUMENT_DATE IS NULL
        """)
        
        # تحديث الوصف بناءً على نوع المستند
        print("📝 تحديث الأوصاف...")
        
        oracle_manager.execute_update("""
            UPDATE CURRENT_BALANCES 
            SET DESCRIPTION = CASE 
                WHEN last_document_type = 'PURCHASE_ORDER' THEN 'أمر شراء رقم ' || last_document_number
                WHEN last_document_type = 'OPENING_BALANCE' THEN 'رصيد افتتاحي'
                WHEN last_document_type IS NULL THEN 'رصيد افتتاحي'
                ELSE last_document_type || ' رقم ' || NVL(last_document_number, 'غير محدد')
            END
            WHERE DESCRIPTION IS NULL
        """)
        
        oracle_manager.commit()
        
        # فحص النتائج
        updated_count = oracle_manager.execute_query("""
            SELECT COUNT(*) FROM CURRENT_BALANCES 
            WHERE DOCUMENT_DATE IS NOT NULL AND DOCUMENT_MONTH IS NOT NULL
        """)
        
        if updated_count:
            print(f"✅ تم تحديث {updated_count[0][0]} سجل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث البيانات: {e}")
        oracle_manager.rollback()
        return False

def create_enhanced_posting_function():
    """إنشاء دالة ترحيل محسنة"""
    
    print("\n🔧 إنشاء دالة ترحيل محسنة")
    print("=" * 60)
    
    try:
        # دالة ترحيل أمر الشراء المحسنة
        enhanced_po_posting_sql = """
        CREATE OR REPLACE FUNCTION ENHANCED_POST_PURCHASE_ORDER(p_po_id NUMBER) RETURN VARCHAR2 IS
            v_already_posted NUMBER;
            v_supplier_code NUMBER;
            v_total_amount NUMBER;
            v_currency VARCHAR2(3);
            v_po_number VARCHAR2(50);
            v_po_title VARCHAR2(500);
            v_po_date DATE;
            v_balance_id NUMBER;
            v_current_balance NUMBER := 0;
            v_credit_amount NUMBER := 0;
            v_result VARCHAR2(500);
        BEGIN
            -- التحقق من الترحيل المسبق
            v_already_posted := IS_PO_ALREADY_POSTED(p_po_id);
            
            IF v_already_posted > 0 THEN
                RETURN 'ERROR: أمر الشراء مُرحل مسبقاً (' || v_already_posted || ' مرة)';
            END IF;
            
            -- الحصول على بيانات أمر الشراء
            SELECT SUPPLIER_CODE, TOTAL_AMOUNT, NVL(CURRENCY, 'USD'), PO_NUMBER, 
                   NVL(TITLE, 'أمر شراء'), NVL(CREATED_AT, SYSDATE)
            INTO v_supplier_code, v_total_amount, v_currency, v_po_number, v_po_title, v_po_date
            FROM PURCHASE_ORDERS
            WHERE ID = p_po_id;
            
            IF v_supplier_code IS NULL THEN
                RETURN 'ERROR: لا يوجد كود مورد';
            END IF;
            
            -- البحث عن الرصيد الحالي
            BEGIN
                SELECT ID, CURRENT_BALANCE, CREDIT_AMOUNT
                INTO v_balance_id, v_current_balance, v_credit_amount
                FROM CURRENT_BALANCES
                WHERE entity_type_code = 'SUPPLIER' 
                AND entity_id = v_supplier_code 
                AND currency_code = v_currency;
                
            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    -- إنشاء رصيد جديد
                    SELECT CURRENT_BALANCES_SEQ.NEXTVAL INTO v_balance_id FROM DUAL;
                    
                    INSERT INTO CURRENT_BALANCES (
                        ID, ENTITY_TYPE_CODE, ENTITY_ID, CURRENCY_CODE,
                        OPENING_BALANCE, DEBIT_AMOUNT, CREDIT_AMOUNT, CURRENT_BALANCE,
                        TOTAL_TRANSACTIONS_COUNT, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY,
                        DOCUMENT_DATE, DOCUMENT_MONTH, DESCRIPTION
                    ) VALUES (
                        v_balance_id, 'SUPPLIER', v_supplier_code, v_currency,
                        0, 0, 0, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1,
                        v_po_date, TO_CHAR(v_po_date, 'YYYY-MM'), 'رصيد جديد'
                    );
                    
                    v_current_balance := 0;
                    v_credit_amount := 0;
            END;
            
            -- ترحيل المبلغ (مرة واحدة فقط)
            v_credit_amount := v_credit_amount + v_total_amount;
            v_current_balance := v_current_balance + v_total_amount;
            
            -- تحديث الرصيد مع الأعمدة الجديدة
            UPDATE CURRENT_BALANCES SET
                CREDIT_AMOUNT = v_credit_amount,
                CURRENT_BALANCE = v_current_balance,
                TOTAL_TRANSACTIONS_COUNT = NVL(TOTAL_TRANSACTIONS_COUNT, 0) + 1,
                LAST_TRANSACTION_DATE = v_po_date,
                LAST_DOCUMENT_TYPE = 'PURCHASE_ORDER',
                LAST_DOCUMENT_NUMBER = v_po_number,
                DOCUMENT_DATE = v_po_date,
                DOCUMENT_MONTH = TO_CHAR(v_po_date, 'YYYY-MM'),
                DESCRIPTION = 'أمر شراء: ' || v_po_title,
                UPDATED_AT = CURRENT_TIMESTAMP,
                UPDATED_BY = 1
            WHERE ID = v_balance_id;
            
            -- تسجيل العملية
            INSERT INTO SYSTEM_LOGS (
                id, log_type, table_name, operation, record_id, log_message, created_at
            ) VALUES (
                SYSTEM_LOGS_SEQ.NEXTVAL, 'ENHANCED_PO_POSTING', 'CURRENT_BALANCES', 'INSERT', p_po_id,
                'ترحيل محسن لأمر الشراء ' || v_po_number || ' للمورد ' || v_supplier_code || ' بتاريخ ' || TO_CHAR(v_po_date, 'YYYY-MM-DD'),
                SYSDATE
            );
            
            RETURN 'SUCCESS: تم الترحيل المحسن بنجاح';
            
        EXCEPTION
            WHEN OTHERS THEN
                RETURN 'ERROR: ' || SQLERRM;
        END;
        """
        
        oracle_manager.execute_update(enhanced_po_posting_sql)
        print("✅ تم إنشاء دالة الترحيل المحسنة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الدالة: {e}")
        return False

def create_opening_balance_posting_function():
    """إنشاء دالة ترحيل الأرصدة الافتتاحية المحسنة"""
    
    print("\n🔧 إنشاء دالة ترحيل الأرصدة الافتتاحية المحسنة")
    print("=" * 60)
    
    try:
        enhanced_opening_posting_sql = """
        CREATE OR REPLACE FUNCTION ENHANCED_POST_OPENING_BALANCE(
            p_supplier_code NUMBER,
            p_amount NUMBER,
            p_currency VARCHAR2,
            p_fiscal_year NUMBER,
            p_description VARCHAR2 DEFAULT NULL
        ) RETURN VARCHAR2 IS
            v_balance_id NUMBER;
            v_opening_date DATE;
            v_description VARCHAR2(500);
        BEGIN
            -- تحديد تاريخ بداية السنة المالية
            v_opening_date := TO_DATE(p_fiscal_year || '-01-01', 'YYYY-MM-DD');
            
            -- تحديد الوصف
            v_description := NVL(p_description, 'رصيد افتتاحي للسنة المالية ' || p_fiscal_year);
            
            -- البحث عن رصيد موجود
            BEGIN
                SELECT ID INTO v_balance_id
                FROM CURRENT_BALANCES
                WHERE entity_type_code = 'SUPPLIER' 
                AND entity_id = p_supplier_code 
                AND currency_code = p_currency;
                
                -- تحديث الرصيد الموجود
                UPDATE CURRENT_BALANCES SET
                    OPENING_BALANCE = p_amount,
                    CURRENT_BALANCE = p_amount + NVL(CREDIT_AMOUNT, 0) - NVL(DEBIT_AMOUNT, 0),
                    DOCUMENT_DATE = v_opening_date,
                    DOCUMENT_MONTH = TO_CHAR(v_opening_date, 'YYYY-MM'),
                    DESCRIPTION = v_description,
                    UPDATED_AT = CURRENT_TIMESTAMP,
                    UPDATED_BY = 1
                WHERE ID = v_balance_id;
                
            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    -- إنشاء رصيد جديد
                    SELECT CURRENT_BALANCES_SEQ.NEXTVAL INTO v_balance_id FROM DUAL;
                    
                    INSERT INTO CURRENT_BALANCES (
                        ID, ENTITY_TYPE_CODE, ENTITY_ID, CURRENCY_CODE,
                        OPENING_BALANCE, DEBIT_AMOUNT, CREDIT_AMOUNT, CURRENT_BALANCE,
                        TOTAL_TRANSACTIONS_COUNT, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY,
                        DOCUMENT_DATE, DOCUMENT_MONTH, DESCRIPTION
                    ) VALUES (
                        v_balance_id, 'SUPPLIER', p_supplier_code, p_currency,
                        p_amount, 0, 0, p_amount, 0,
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1,
                        v_opening_date, TO_CHAR(v_opening_date, 'YYYY-MM'), v_description
                    );
            END;
            
            -- تسجيل العملية
            INSERT INTO SYSTEM_LOGS (
                id, log_type, table_name, operation, record_id, log_message, created_at
            ) VALUES (
                SYSTEM_LOGS_SEQ.NEXTVAL, 'ENHANCED_OPENING_POSTING', 'CURRENT_BALANCES', 'INSERT', v_balance_id,
                'ترحيل رصيد افتتاحي محسن للمورد ' || p_supplier_code || ' بمبلغ ' || p_amount || ' ' || p_currency,
                SYSDATE
            );
            
            RETURN 'SUCCESS: تم ترحيل الرصيد الافتتاحي المحسن بنجاح';
            
        EXCEPTION
            WHEN OTHERS THEN
                RETURN 'ERROR: ' || SQLERRM;
        END;
        """
        
        oracle_manager.execute_update(enhanced_opening_posting_sql)
        print("✅ تم إنشاء دالة ترحيل الأرصدة الافتتاحية المحسنة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الدالة: {e}")
        return False

def test_enhanced_functions():
    """اختبار الدوال المحسنة"""
    
    print("\n🧪 اختبار الدوال المحسنة")
    print("=" * 60)
    
    try:
        # اختبار دالة ترحيل أمر الشراء المحسنة
        print("🔧 اختبار دالة ترحيل أمر الشراء المحسنة...")
        
        # إنشاء أمر شراء تجريبي
        test_po_sql = """
        INSERT INTO PURCHASE_ORDERS (
            PO_NUMBER, SUPPLIER_CODE, SUPPLIER_NAME, TITLE,
            TOTAL_AMOUNT, CURRENCY, STATUS, CREATED_BY, CREATED_AT
        ) VALUES (
            :1, :2, :3, :4, :5, :6, :7, :8, SYSDATE
        )
        """
        
        smart_po_result = oracle_manager.execute_query("SELECT GENERATE_SMART_PO_NUMBER(2025) FROM DUAL")
        smart_po_number = smart_po_result[0][0] if smart_po_result else "PO-TEST-ENH"
        
        params = [
            smart_po_number,
            1138,
            "اثراء الطبيعة - اختبار التحسينات",
            "أمر شراء لاختبار الترحيل المحسن",
            50000,
            "USD",
            "مسودة",
            1
        ]
        
        result = oracle_manager.execute_update(test_po_sql, params)
        
        if result > 0:
            # الحصول على ID أمر الشراء الجديد
            new_po_id = oracle_manager.execute_query(
                "SELECT ID FROM PURCHASE_ORDERS WHERE PO_NUMBER = :1", 
                [smart_po_number]
            )
            
            if new_po_id:
                po_id = new_po_id[0][0]
                print(f"✅ تم إنشاء أمر شراء تجريبي: {smart_po_number} (ID: {po_id})")
                
                # اختبار الترحيل المحسن
                posting_result = oracle_manager.execute_query(
                    "SELECT ENHANCED_POST_PURCHASE_ORDER(:1) FROM DUAL", [po_id]
                )
                
                if posting_result:
                    message = posting_result[0][0]
                    print(f"📋 نتيجة الترحيل المحسن: {message}")
                    
                    if "SUCCESS" in message:
                        print("✅ نجح الترحيل المحسن!")
                        
                        # فحص البيانات المحسنة
                        enhanced_data = oracle_manager.execute_query("""
                            SELECT entity_id, current_balance, currency_code, 
                                   document_date, document_month, description
                            FROM CURRENT_BALANCES 
                            WHERE entity_id = :1
                        """, [1138])
                        
                        if enhanced_data:
                            data = enhanced_data[0]
                            print(f"📊 البيانات المحسنة:")
                            print(f"   👤 مورد: {data[0]}")
                            print(f"   💰 رصيد: {data[1]} {data[2]}")
                            print(f"   📅 تاريخ الوثيقة: {data[3]}")
                            print(f"   📅 الشهر: {data[4]}")
                            print(f"   📝 الوصف: {data[5]}")
                    
                    # حذف أمر الشراء التجريبي
                    oracle_manager.execute_update(
                        "DELETE FROM PURCHASE_ORDERS WHERE ID = :1", [po_id]
                    )
                    oracle_manager.commit()
                    print("🧹 تم حذف أمر الشراء التجريبي")
                    
                    return True
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    print("🚀 تحسين جدول CURRENT_BALANCES")
    print("📅 التاريخ: 2025-09-06")
    print("🕒 الوقت: 00:40")
    
    # تحليل البنية الحالية
    if analyze_current_table_structure():
        # إضافة الأعمدة الجديدة
        if add_new_columns():
            # تحديث البيانات الموجودة
            if update_existing_data():
                # إنشاء الدوال المحسنة
                if create_enhanced_posting_function():
                    if create_opening_balance_posting_function():
                        # اختبار الدوال
                        if test_enhanced_functions():
                            print("\n" + "=" * 60)
                            print("🎉 تم تحسين جدول CURRENT_BALANCES بنجاح!")
                            print("✅ تم إضافة الأعمدة الجديدة:")
                            print("   📅 DOCUMENT_DATE - تاريخ الوثيقة")
                            print("   📅 DOCUMENT_MONTH - الشهر")
                            print("   📝 DESCRIPTION - الوصف/البيان")
                            print("✅ تم إنشاء الدوال المحسنة:")
                            print("   🔧 ENHANCED_POST_PURCHASE_ORDER")
                            print("   🔧 ENHANCED_POST_OPENING_BALANCE")
                            print("✅ تم تحديث البيانات الموجودة")
                            print("🎯 النظام جاهز للاستخدام المحسن!")
                        else:
                            print("\n❌ فشل في اختبار الدوال")
                    else:
                        print("\n❌ فشل في إنشاء دالة الأرصدة الافتتاحية")
                else:
                    print("\n❌ فشل في إنشاء دالة الترحيل المحسنة")
            else:
                print("\n❌ فشل في تحديث البيانات الموجودة")
        else:
            print("\n❌ فشل في إضافة الأعمدة الجديدة")
    else:
        print("\n❌ فشل في تحليل البنية الحالية")
