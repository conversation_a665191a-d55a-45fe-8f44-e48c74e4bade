{% extends "base.html" %}

{% block title %}إنشاء أمر شراء جديد{% endblock %}

{% block content %}
<div class="container-fluid px-3">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء أمر شراء جديد
                    </h3>
                    <a href="{{ url_for('purchase_orders.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للقائمة
                    </a>
                </div>

                <div class="card-body">
                    <!-- رسالة توضيحية -->
                    <div class="alert alert-info mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle fa-2x me-3"></i>
                            <div>
                                <h6 class="alert-heading mb-1">طريقتان لإنشاء أمر الشراء:</h6>
                                <p class="mb-0">
                                    <strong>1. من عقد موجود:</strong> اختر العقد لملء بيانات المورد تلقائياً<br>
                                    <strong>2. إدخال يدوي:</strong> اتركه فارغاً واختر المورد مباشرة
                                </p>
                            </div>
                        </div>
                    </div>

                    <form id="purchaseOrderForm">
                        <!-- البيانات الأساسية -->
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    البيانات الأساسية
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- الصف الأول: الفرع + العنوان -->
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="branchId" class="form-label">الفرع *</label>
                                            <select class="form-select" id="branchId" name="branch_id" required>
                                                <option value="">اختر الفرع</option>
                                                {% if branches %}
                                                    {% for branch in branches %}
                                                        <option value="{{ branch.brn_no }}"
                                                                {% if branch.brn_no == 21 %}selected{% endif %}>
                                                            {{ branch.brn_lname }}
                                                        </option>
                                                    {% endfor %}
                                                {% else %}
                                                    <option value="21" selected>الفرع الرئيسي</option>
                                                {% endif %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">عنوان أمر الشراء *</label>
                                            <input type="text" class="form-control" id="title" name="title"
                                                   placeholder="أدخل عنوان أمر الشراء" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="orderNumber" class="form-label">رقم الأمر</label>
                                            <input type="text" class="form-control" id="orderNumber" name="order_number"
                                                   placeholder="تلقائي" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="poDate" class="form-label">تاريخ الأمر *</label>
                                            <input type="date" class="form-control" id="poDate" name="po_date" required>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الثاني: العقد المرجعي + تاريخ التسليم -->
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="contractSelect" class="form-label">العقد المرجعي (اختياري)</label>
                                            <select class="form-select" id="contractSelect" name="contract_id">
                                                <option value="">بدون عقد - إدخال يدوي</option>
                                                {% for contract in contracts %}
                                                <option value="{{ contract[0] }}"
                                                        data-supplier-name="{{ contract[2] }}"
                                                        data-supplier-id="{{ contract[4] if contract|length > 4 else '' }}"
                                                        {% if contract_data and contract[0] == contract_data[0] %}selected{% endif %}>
                                                    {{ contract[1] }} - {{ contract[2] }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <small>يتم عرض العقود النشطة والمتاحة للاستخدام فقط. العقود المنفذة كلياً لا تظهر في القائمة.</small>
                                            </div>
                                            <div class="form-text">اختر عقد لملء بيانات المورد تلقائياً أو اتركه فارغاً للإدخال اليدوي</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="deliveryDate" class="form-label">تاريخ التسليم المطلوب</label>
                                            <input type="date" class="form-control" id="deliveryDate" name="delivery_date">
                                        </div>
                                    </div>
                                </div>

                                <!-- قسم بيانات المورد -->
                                <div class="row">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-truck me-2"></i>
                                            بيانات المورد
                                        </h6>
                                    </div>
                                </div>

                                <!-- الصف الثالث: اختيار المورد + اسم المورد -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="supplierCode" class="form-label">كود المورد *</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="supplierCode" name="supplier_code"
                                                       placeholder="أدخل كود المورد أو اضغط F9 للبحث" required>
                                                <button type="button" class="btn btn-outline-secondary" id="searchSupplierBtn"
                                                        title="بحث عن مورد (F9)">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <strong>طريقتان للإدخال:</strong><br>
                                                <small>
                                                    • <strong>مباشرة:</strong> أدخل كود المورد كاملاً وسيتم البحث تلقائياً<br>
                                                    • <strong>البحث:</strong> اضغط F9 أو زر البحث لفتح نافذة البحث
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="supplierName" class="form-label">اسم المورد</label>
                                            <input type="text" class="form-control" id="supplierName"
                                                   name="supplier_name"
                                                   value="{{ contract_data[2] if contract_data else '' }}">
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الرابع: شروط الدفع + رقم فاتورة المورد -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="paymentTerms" class="form-label">شروط الدفع</label>
                                            <select class="form-select" id="paymentTerms" name="payment_terms">
                                                <option value="">اختر شروط الدفع...</option>
                                                <option value="نقداً">نقداً</option>
                                                <option value="آجل 30 يوم">آجل 30 يوم</option>
                                                <option value="آجل 60 يوم">آجل 60 يوم</option>
                                                <option value="آجل 90 يوم">آجل 90 يوم</option>
                                                <option value="تقسيط">تقسيط</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="supplierInvoiceNumber" class="form-label">
                                                <i class="fas fa-file-invoice me-1"></i>
                                                رقم فاتورة المورد
                                            </label>
                                            <input type="text" class="form-control" id="supplierInvoiceNumber"
                                                   name="supplier_invoice_number"
                                                   placeholder="أدخل رقم فاتورة المورد">
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <small>رقم الفاتورة الصادرة من المورد (اختياري)</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف الخامس: الحالة + الأولوية -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">الحالة</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="مسودة" selected>مسودة</option>
                                                <option value="مرسل">مرسل</option>
                                                <option value="مؤكد">مؤكد</option>
                                                <option value="جاري التنفيذ">جاري التنفيذ</option>
                                                <option value="مكتمل">مكتمل</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="priority" class="form-label">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                الأولوية
                                            </label>
                                            <select class="form-select" id="priority" name="priority">
                                                <option value="عادي" selected>عادي</option>
                                                <option value="مهم">مهم</option>
                                                <option value="عاجل">عاجل</option>
                                                <option value="عاجل جداً">عاجل جداً</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف السادس: أجور الشحن + أجور التخليص -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="shippingCost" class="form-label">
                                                <i class="fas fa-shipping-fast me-1"></i>
                                                أجور الشحن
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="shippingCost"
                                                       name="shipping_cost" step="0.01" min="0" value="0.00"
                                                       placeholder="0.00" onchange="calculateTotal()">
                                                <span class="input-group-text currency-symbol">ريال</span>
                                            </div>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <small>تكلفة شحن البضائع من المورد (اختياري)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="clearanceCost" class="form-label">
                                                <i class="fas fa-file-import me-1"></i>
                                                أجور التخليص
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="clearanceCost"
                                                       name="clearance_cost" step="0.01" min="0" value="0.00"
                                                       placeholder="0.00" onchange="calculateTotal()">
                                                <span class="input-group-text currency-symbol">ريال</span>
                                            </div>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <small>رسوم التخليص الجمركي والإجراءات (اختياري)</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف السابع: العملة + مدة التسليم -->
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="currency" class="form-label">
                                                <i class="fas fa-coins me-1"></i>العملة
                                            </label>
                                            <select class="form-select" id="currency" name="currency" onchange="updateCurrencyDisplay()">
                                                {% if currencies %}
                                                    {% for currency in currencies %}
                                                        <option value="{{ currency[1] }}"
                                                                data-symbol="{{ currency[4] }}"
                                                                data-name="{{ currency[2] }}"
                                                                {% if currency[5] == 1 %}selected{% endif %}>
                                                            {{ currency[4] }} - {{ currency[2] }}
                                                            {% if currency[5] == 1 %} (العملة الأساسية){% endif %}
                                                        </option>
                                                    {% endfor %}
                                                {% else %}
                                                    <option value="SAR" selected>﷼ - ريال سعودي</option>
                                                {% endif %}
                                            </select>
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                العملة المستخدمة في هذا الأمر
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="priority" class="form-label">الأولوية</label>
                                            <select class="form-select" id="priority" name="priority">
                                                <option value="عادي" selected>عادي</option>
                                                <option value="عاجل">عاجل</option>
                                                <option value="عاجل جداً">عاجل جداً</option>
                                                <option value="منخفض">منخفض</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="expectedDeliveryDays" class="form-label">مدة التسليم (أيام)</label>
                                            <input type="number" class="form-control" id="expectedDeliveryDays"
                                                   name="expected_delivery_days" min="1" placeholder="أيام">
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف السادس: الوصف والملاحظات -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="description" class="form-label">وصف الأمر</label>
                                            <input type="text" class="form-control" id="description" name="description"
                                                   placeholder="أدخل وصف أمر الشراء...">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">ملاحظات إضافية</label>
                                            <input type="text" class="form-control" id="notes" name="notes"
                                                   placeholder="أي ملاحظات أو تعليمات خاصة...">
                                        </div>
                                    </div>
                                </div>

                                <!-- الصف السابع: المبالغ المالية -->
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="grossAmount" class="form-label">
                                                <i class="fas fa-calculator me-1"></i>إجمالي الأصناف
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="grossAmount" name="gross_amount"
                                                       step="0.01" min="0" placeholder="0.00" readonly>
                                                <span class="input-group-text currency-symbol">ريال</span>
                                            </div>
                                            <small class="text-muted">يتم حسابه تلقائياً من الأصناف</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="discountAmount" class="form-label">
                                                <i class="fas fa-percentage me-1"></i>مبلغ الخصم
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control bg-light" id="discountAmount" name="discount_amount"
                                                       step="0.01" min="0" placeholder="0.00" onchange="calculateFinalAmounts()"
                                                       title="يتم حسابه تلقائياً من خصومات الأصناف، يمكن إضافة خصم إضافي"
                                                       style="background-color: #f8f9fa !important; border-left: 3px solid #28a745;">
                                                <span class="input-group-text currency-symbol">ريال</span>
                                            </div>
                                            <small class="text-muted">يتم حسابه تلقائياً من خصومات الأصناف + خصم إضافي</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="netAmount" class="form-label">
                                                <i class="fas fa-money-bill-wave me-1"></i>المبلغ الإجمالي النهائي
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="netAmount" name="net_amount"
                                                       step="0.01" min="0" placeholder="0.00" readonly>
                                                <span class="input-group-text currency-symbol">ريال</span>
                                            </div>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <small>المبلغ الإجمالي النهائي (يشمل الأصناف + الشحن + التخليص - الخصم)</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- قسم الأصناف والكميات -->
                                <div class="mt-4">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-list me-2"></i>
                                                    أصناف أمر الشراء
                                                </h5>
                                                <button type="button" class="btn btn-light btn-sm" onclick="addItem()">
                                                    <i class="fas fa-plus me-2"></i>
                                                    إضافة صنف
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-bordered" id="itemsTable">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th style="width: 120px;">
                                                                كود الصنف *
                                                                <i class="fas fa-exclamation-circle text-danger ms-1" title="حقل مطلوب"></i>
                                                            </th>
                                                            <th style="width: 280px;">
                                                                اسم الصنف *
                                                                <i class="fas fa-exclamation-circle text-danger ms-1" title="حقل مطلوب"></i>
                                                            </th>
                                                            <th style="width: 80px;">الوحدة</th>
                                                            <th style="width: 100px;">
                                                                الكمية *
                                                                <i class="fas fa-exclamation-circle text-danger ms-1" title="حقل مطلوب"></i>
                                                            </th>
                                                            <th style="width: 120px;">
                                                                السعر *
                                                                <i class="fas fa-exclamation-circle text-danger ms-1" title="حقل مطلوب"></i>
                                                            </th>
                                                            <th style="width: 130px;">تاريخ الإنتاج</th>
                                                            <th style="width: 130px;">
                                                                تاريخ الانتهاء *
                                                                <i class="fas fa-exclamation-circle text-danger ms-1" title="حقل مطلوب"></i>
                                                            </th>
                                                            <th style="width: 80px;">خصم (%)</th>
                                                            <th style="width: 120px;">الإجمالي</th>
                                                            <th style="width: 100px;">العمليات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="itemsTableBody">
                                                        <!-- سيتم إضافة الأصناف هنا بواسطة JavaScript -->
                                                    </tbody>
                                                    <tfoot>
                                                        <tr class="table-light">
                                                            <td colspan="8" class="text-end"><strong>المجموع الفرعي:</strong></td>
                                                            <td><strong id="subtotalAmount">0.00</strong></td>
                                                            <td></td>
                                                        </tr>
                                                        <tr class="table-light">
                                                            <td colspan="8" class="text-end"><strong>إجمالي الخصم:</strong></td>
                                                            <td><strong id="totalDiscount">0.00</strong></td>
                                                            <td></td>
                                                        </tr>
                                                        <tr class="table-light">
                                                            <td colspan="8" class="text-end"><strong>إجمالي الضريبة:</strong></td>
                                                            <td><strong id="totalTax">0.00</strong></td>
                                                            <td></td>
                                                        </tr>
                                                        <tr class="table-success">
                                                            <td colspan="8" class="text-end"><strong>الإجمالي النهائي:</strong></td>
                                                            <td><strong id="grandTotal">0.00 <span class="currency-symbol">ريال</span></strong></td>
                                                            <td></td>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                            <div class="card-footer">
                                                <div class="form-text">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    <strong>طرق إدخال الأصناف:</strong><br>
                                                    <small>
                                                        • <strong>مباشرة:</strong> أدخل كود الصنف كاملاً وسيتم البحث تلقائياً<br>
                                                        • <strong>البحث:</strong> اضغط F9 في حقل كود الصنف لفتح نافذة البحث<br>
                                                        • <strong>من العقد:</strong> سيتم إنزال الأصناف تلقائياً عند اختيار عقد
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                        <!-- أزرار الحفظ -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ url_for('purchase_orders.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ أمر الشراء
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let itemCounter = 0;

// تحديث بيانات المورد عند اختيار العقد
document.getElementById('contractSelect').addEventListener('change', function() {
    const contractId = this.value;
    const selectedOption = this.options[this.selectedIndex];

    // جلب بيانات المورد من العقد المختار (من جدول العقود)
    const supplierName = selectedOption.getAttribute('data-supplier-name') || '';
    const supplierId = selectedOption.getAttribute('data-supplier-id') || '';

    if (contractId && contractId !== '') {
        // تحديث بيانات المورد من العقد مباشرة
        const supplierCodeField = document.getElementById('supplierCode');
        const supplierNameField = document.getElementById('supplierName');

        // استخدام SUPPLIER_ID كـ كود المورد (من جدول العقود)
        supplierCodeField.value = supplierId || '';

        // استخدام SUPPLIER_NAME من جدول العقود
        supplierNameField.value = supplierName || '';

        console.log('📋 تم تحديث بيانات المورد من العقد:');
        console.log('   كود المورد:', supplierId);
        console.log('   اسم المورد:', supplierName);

        // تعطيل حقول المورد (readonly)
        supplierCodeField.readOnly = true;
        supplierCodeField.style.backgroundColor = '#e9ecef';
        supplierCodeField.title = 'تم تحديد المورد من العقد المختار';

        supplierNameField.readOnly = true;
        supplierNameField.style.backgroundColor = '#e9ecef';
        supplierNameField.style.color = '#198754';

        // عرض رسالة نجاح
        showTemporaryMessage(`✅ تم تحديد المورد من العقد: ${supplierName}`, 'success');

        // جلب تفاصيل العقد والأصناف
        loadContractDetails(contractId);

    } else {
        // تفعيل حقول المورد عند عدم اختيار عقد
        const supplierCodeField = document.getElementById('supplierCode');
        const supplierNameField = document.getElementById('supplierName');

        // مسح البيانات
        supplierCodeField.value = '';
        supplierNameField.value = '';

        // تفعيل الحقول
        supplierCodeField.readOnly = false;
        supplierNameField.readOnly = false;

        // إعادة تعيين الألوان
        supplierCodeField.style.backgroundColor = '';
        supplierNameField.style.backgroundColor = '';
        supplierNameField.style.color = '';

        // إزالة العنوان التوضيحي
        supplierCodeField.title = '';

        // مسح الأصناف الموجودة
        clearItems();
    }
});



// تحديث بيانات المورد عند إدخال كود المورد (البحث المباشر)
document.getElementById('supplierCode').addEventListener('blur', function() {
    const supplierCode = this.value.trim();

    // التحقق من أن الكود ليس فارغاً أو None
    if (supplierCode && supplierCode !== '' && supplierCode.toLowerCase() !== 'none' && supplierCode.toLowerCase() !== 'null') {
        // عرض مؤشر التحميل
        const supplierNameField = document.getElementById('supplierName');
        const originalValue = supplierNameField.value;
        supplierNameField.value = 'جاري البحث...';
        supplierNameField.style.color = '#6c757d';

        // البحث المباشر عن المورد بالكود
        fetch(`/purchase-orders/api/get-supplier-by-code/${encodeURIComponent(supplierCode)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.supplier) {
                // تم العثور على المورد - تحديث البيانات
                const supplier = data.supplier;

                supplierNameField.value = supplier.name;
                supplierNameField.style.color = '#198754'; // أخضر للنجاح

                // تحديث حقول إضافية إذا كانت موجودة
                const contactPersonField = document.getElementById('contactPerson');
                const phoneField = document.getElementById('phone');
                const emailField = document.getElementById('email');

                if (contactPersonField) contactPersonField.value = supplier.contact_person;
                if (phoneField) phoneField.value = supplier.phone;
                if (emailField) emailField.value = supplier.email;

                // إلغاء اختيار العقد
                document.getElementById('contractSelect').value = '';

                // عرض رسالة نجاح مؤقتة
                showTemporaryMessage(`✅ تم العثور على المورد: ${supplier.name}`, 'success');

                console.log('✅ تم العثور على المورد:', supplier);

            } else {
                // لم يتم العثور على المورد
                supplierNameField.value = '';
                supplierNameField.style.color = '#dc3545'; // أحمر للخطأ

                // عرض رسالة خطأ مؤقتة
                showTemporaryMessage(`❌ ${data.message}`, 'error');

                console.log('❌ لم يتم العثور على المورد:', data.message);
            }
        })
        .catch(error => {
            console.error('خطأ في البحث عن المورد:', error);
            supplierNameField.value = originalValue;
            supplierNameField.style.color = '#dc3545';

            showTemporaryMessage('❌ حدث خطأ أثناء البحث عن المورد', 'error');
        })
        .finally(() => {
            // إعادة لون النص للطبيعي بعد ثانيتين
            setTimeout(() => {
                supplierNameField.style.color = '';
            }, 2000);
        });

    } else {
        // إذا كان الحقل فارغ، مسح اسم المورد
        document.getElementById('supplierName').value = '';
    }
});

// دالة عرض الرسائل المؤقتة
function showTemporaryMessage(message, type = 'info') {
    // إزالة أي رسالة سابقة
    const existingMessage = document.getElementById('tempMessage');
    if (existingMessage) {
        existingMessage.remove();
    }

    // إنشاء عنصر الرسالة
    const messageDiv = document.createElement('div');
    messageDiv.id = 'tempMessage';
    messageDiv.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show`;
    messageDiv.style.position = 'fixed';
    messageDiv.style.top = '20px';
    messageDiv.style.right = '20px';
    messageDiv.style.zIndex = '9999';
    messageDiv.style.minWidth = '300px';
    messageDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    // إضافة الرسالة للصفحة
    document.body.appendChild(messageDiv);

    // إزالة الرسالة تلقائياً بعد 4 ثوان
    setTimeout(() => {
        if (messageDiv && messageDiv.parentElement) {
            messageDiv.remove();
        }
    }, 4000);
}

// جلب تفاصيل العقد والأصناف
function loadContractDetails(contractId) {
    console.log('🔍 جلب تفاصيل العقد:', contractId);
    fetch(`/purchase-orders/api/contract/${contractId}`)
    .then(response => response.json())
    .then(data => {
        console.log('📦 البيانات المستلمة:', data);
        if (data.success) {
            // تحديث بيانات المورد من العقد
            if (data.contract) {
                const supplierCodeField = document.getElementById('supplierCode');
                const supplierNameField = document.getElementById('supplierName');

                if (data.contract.supplier_code && data.contract.supplier_code !== 'None') {
                    supplierCodeField.value = data.contract.supplier_code;
                    console.log('✅ تم تحديث كود المورد:', data.contract.supplier_code);
                }

                if (data.contract.supplier_name && data.contract.supplier_name !== 'None') {
                    supplierNameField.value = data.contract.supplier_name;
                    console.log('✅ تم تحديث اسم المورد:', data.contract.supplier_name);
                }
            }

            // مسح الأصناف الموجودة أولاً
            clearItems();

            // إضافة أصناف العقد
            if (data.items && data.items.length > 0) {
                console.log(`📋 إضافة ${data.items.length} صنف من العقد`);
                data.items.forEach((item, index) => {
                    console.log(`📅 الصنف ${index + 1}:`, {
                        name: item.item_name,
                        production_date: item.production_date,
                        expiry_date: item.expiry_date
                    });
                    addItemFromContract(item);
                });

                // إعادة حساب الإجماليات
                calculateTotal();
            }
        } else {
            alert('خطأ في جلب تفاصيل العقد: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('خطأ في الاتصال بالخادم');
    });
}

// مسح جميع الأصناف
function clearItems() {
    const tbody = document.getElementById('itemsTableBody');
    tbody.innerHTML = '';
    itemCounter = 0;
    calculateTotal();
}

// إضافة صنف من العقد
function addItemFromContract(contractItem) {
    console.log('➕ إضافة صنف من العقد:', contractItem);
    console.log('📅 التواريخ:', {
        production_date: contractItem.production_date,
        expiry_date: contractItem.expiry_date
    });

    itemCounter++;
    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');

    // حساب السعر بعد الخصم
    const unitPrice = contractItem.unit_price || 0;
    const discountRate = contractItem.discount_percentage || 0;
    const quantity = contractItem.quantity || 0;
    const priceAfterDiscount = unitPrice * (1 - discountRate / 100);
    const total = quantity * priceAfterDiscount;

    row.innerHTML = `
        <td><input type="text" class="form-control form-control-sm" name="items[${itemCounter}][code]" value="${contractItem.item_id || ''}" placeholder="كود الصنف" style="width: 100%;"></td>
        <td><input type="text" class="form-control form-control-sm" name="items[${itemCounter}][name]" value="${contractItem.item_name || ''}" placeholder="اسم الصنف" required style="width: 100%;"></td>
        <td><input type="text" class="form-control form-control-sm" name="items[${itemCounter}][unit]" value="${contractItem.unit_name || ''}" placeholder="الوحدة" style="width: 100%;"></td>
        <td><input type="number" class="form-control form-control-sm" name="items[${itemCounter}][quantity]" value="${quantity}" placeholder="الكمية" step="0.01" min="0" required onchange="calculateRowTotal(this)" style="width: 100%;"></td>
        <td><input type="number" class="form-control form-control-sm" name="items[${itemCounter}][price]" value="${unitPrice}" placeholder="السعر" step="0.01" min="0" required onchange="calculateRowTotal(this)" style="width: 100%;"></td>
        <td><input type="date" class="form-control form-control-sm" name="items[${itemCounter}][production_date]" value="${contractItem.production_date || ''}" style="width: 100%;"></td>
        <td><input type="date" class="form-control form-control-sm" name="items[${itemCounter}][expiry_date]" value="${contractItem.expiry_date || ''}" style="width: 100%;"></td>
        <td><input type="number" class="form-control form-control-sm" name="items[${itemCounter}][discount]" value="${discountRate}" placeholder="0" step="0.01" min="0" max="100" onchange="calculateRowTotal(this)" style="width: 100%;"></td>
        <td><span class="row-total">${total.toFixed(2)}</span></td>
        <td><button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)"><i class="fas fa-trash"></i></button></td>
    `;
    tbody.appendChild(row);
}

// إضافة صنف جديد
function addItem() {
    itemCounter++;
    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');
    row.innerHTML = `
        <td><input type="text" class="form-control form-control-sm required-field" name="items[${itemCounter}][code]" placeholder="كود الصنف *" required style="width: 100%; border-left: 3px solid #dc3545;"></td>
        <td><input type="text" class="form-control form-control-sm required-field" name="items[${itemCounter}][name]" placeholder="اسم الصنف *" required style="width: 100%; border-left: 3px solid #dc3545;"></td>
        <td><input type="text" class="form-control form-control-sm" name="items[${itemCounter}][unit]" placeholder="الوحدة" style="width: 100%;"></td>
        <td><input type="number" class="form-control form-control-sm required-field" name="items[${itemCounter}][quantity]" placeholder="الكمية *" step="0.01" min="0.01" required onchange="calculateRowTotal(this)" style="width: 100%; border-left: 3px solid #dc3545;"></td>
        <td><input type="number" class="form-control form-control-sm required-field" name="items[${itemCounter}][price]" placeholder="السعر *" step="0.01" min="0.01" required onchange="calculateRowTotal(this)" style="width: 100%; border-left: 3px solid #dc3545;"></td>
        <td><input type="date" class="form-control form-control-sm" name="items[${itemCounter}][production_date]" style="width: 100%;"></td>
        <td><input type="date" class="form-control form-control-sm required-field" name="items[${itemCounter}][expiry_date]" required style="width: 100%; border-left: 3px solid #dc3545;" title="تاريخ الانتهاء مطلوب"></td>
        <td><input type="number" class="form-control form-control-sm" name="items[${itemCounter}][discount]" placeholder="0" step="0.01" min="0" max="100" value="0" onchange="calculateRowTotal(this)" style="width: 100%;"></td>
        <td><span class="row-total">0.00</span></td>
        <td><button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)"><i class="fas fa-trash"></i></button></td>
    `;
    tbody.appendChild(row);
}

// حذف صنف
function removeItem(button) {
    button.closest('tr').remove();
    calculateTotal();
}

// حساب إجمالي الصف
function calculateRowTotal(input) {
    const row = input.closest('tr');
    const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
    const price = parseFloat(row.querySelector('input[name*="[price]"]').value) || 0;
    const discountRate = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

    // حساب المجموع الفرعي
    const subtotal = quantity * price;

    // حساب الخصم
    const discountAmount = subtotal * (discountRate / 100);

    // الإجمالي النهائي (بعد الخصم فقط)
    const total = subtotal - discountAmount;

    row.querySelector('.row-total').textContent = total.toFixed(2);
    calculateTotal();
}

// حساب الإجمالي الكلي
function calculateTotal() {
    const rows = document.querySelectorAll('#itemsTableBody tr');
    let subtotal = 0;
    let totalDiscount = 0;
    let grandTotal = 0;

    rows.forEach(row => {
        const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
        const price = parseFloat(row.querySelector('input[name*="[price]"]').value) || 0;
        const discountRate = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

        const rowSubtotal = quantity * price;
        const rowDiscount = rowSubtotal * (discountRate / 100);
        const rowTotal = rowSubtotal - rowDiscount;

        subtotal += rowSubtotal;
        totalDiscount += rowDiscount;
        grandTotal += rowTotal;
    });

    // الحصول على رمز العملة المحدد
    const currencySelect = document.getElementById('currency');
    const selectedOption = currencySelect.options[currencySelect.selectedIndex];
    const currencySymbol = selectedOption ? selectedOption.getAttribute('data-symbol') || 'ريال' : 'ريال';

    // إضافة أجور الشحن والتخليص
    const shippingCost = parseFloat(document.getElementById('shippingCost').value) || 0;
    const clearanceCost = parseFloat(document.getElementById('clearanceCost').value) || 0;
    const additionalCosts = shippingCost + clearanceCost;

    // الإجمالي النهائي مع التكاليف الإضافية
    const finalTotal = grandTotal + additionalCosts;

    // تحديث العرض في جدول الإجماليات
    document.getElementById('subtotalAmount').textContent = subtotal.toFixed(2);
    document.getElementById('totalDiscount').textContent = totalDiscount.toFixed(2);
    document.getElementById('totalTax').textContent = '0.00'; // لا توجد ضريبة

    // إضافة عرض التكاليف الإضافية إذا كانت موجودة
    let grandTotalHTML = grandTotal.toFixed(2);
    if (additionalCosts > 0) {
        grandTotalHTML += ` + ${additionalCosts.toFixed(2)} (تكاليف إضافية) = ${finalTotal.toFixed(2)}`;
    }
    grandTotalHTML += ' <span class="currency-symbol">' + currencySymbol + '</span>';

    document.getElementById('grandTotal').innerHTML = grandTotalHTML;

    // تحديث الحقول في البيانات الأساسية
    const grossAmount = subtotal; // إجمالي الأصناف = المجموع قبل الخصم
    document.getElementById('grossAmount').value = grossAmount.toFixed(2);

    // حساب وعرض المبلغ الإجمالي النهائي (يشمل جميع التكاليف)
    const finalTotalAmount = finalTotal;
    document.getElementById('netAmount').value = finalTotalAmount.toFixed(2);

    // تحديث مبلغ الخصم من مجموع خصومات الأصناف
    const discountField = document.getElementById('discountAmount');
    const currentDiscount = parseFloat(discountField.value) || 0;
    const itemsDiscount = totalDiscount;

    // إذا كان هناك خصم إضافي مدخل يدوياً، نحافظ عليه
    const manualDiscount = currentDiscount - (parseFloat(discountField.dataset.itemsDiscount) || 0);
    const newTotalDiscount = itemsDiscount + Math.max(0, manualDiscount);

    discountField.value = newTotalDiscount.toFixed(2);
    discountField.dataset.itemsDiscount = itemsDiscount.toFixed(2);

    // إضافة تأثير بصري لإظهار التحديث
    if (itemsDiscount > 0) {
        discountField.style.borderLeftColor = '#28a745';
        discountField.style.borderLeftWidth = '3px';
    } else {
        discountField.style.borderLeftColor = '#dee2e6';
        discountField.style.borderLeftWidth = '1px';
    }

    // حساب صافي المبلغ (سيتم تحديثه في دالة calculateFinalAmounts)
    calculateFinalAmounts();
}

// حساب المبالغ النهائية (مع خصم إضافي وأجور الشحن والتخليص)
function calculateFinalAmounts() {
    const grossAmount = parseFloat(document.getElementById('grossAmount').value) || 0;
    const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
    const shippingCost = parseFloat(document.getElementById('shippingCost').value) || 0;
    const clearanceCost = parseFloat(document.getElementById('clearanceCost').value) || 0;

    // حساب صافي المبلغ بعد الخصم
    const netAmountAfterDiscount = grossAmount - discountAmount;

    // إضافة أجور الشحن والتخليص للمبلغ النهائي
    const finalAmount = netAmountAfterDiscount + shippingCost + clearanceCost;

    // تحديث المبلغ الإجمالي النهائي
    document.getElementById('netAmount').value = finalAmount.toFixed(2);

    // إضافة تأثير بصري للحقل
    const netAmountField = document.getElementById('netAmount');
    netAmountField.style.backgroundColor = '#d4edda';
    netAmountField.style.borderColor = '#28a745';
    netAmountField.style.fontWeight = 'bold';

    // تحديث لون الحقل حسب نوع الخصم
    const discountField = document.getElementById('discountAmount');
    const itemsDiscount = parseFloat(discountField.dataset.itemsDiscount) || 0;

    if (discountAmount > itemsDiscount) {
        // هناك خصم إضافي
        discountField.style.borderLeftColor = '#ffc107';
        discountField.title = `خصم الأصناف: ${itemsDiscount.toFixed(2)} + خصم إضافي: ${(discountAmount - itemsDiscount).toFixed(2)}`;
    } else if (itemsDiscount > 0) {
        // خصم الأصناف فقط
        discountField.style.borderLeftColor = '#28a745';
        discountField.title = `خصم الأصناف: ${itemsDiscount.toFixed(2)}`;
    } else {
        // لا يوجد خصم
        discountField.style.borderLeftColor = '#dee2e6';
        discountField.title = 'لا يوجد خصم';
    }
}

// حفظ أمر الشراء
document.getElementById('purchaseOrderForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {};
    
    // جمع البيانات الأساسية
    for (let [key, value] of formData.entries()) {
        if (!key.includes('items[')) {
            // تنظيف القيم - تجنب إرسال قيم فارغة أو None
            if (value === 'None' || value === 'none' || value === 'null') {
                data[key] = '';
            } else {
                data[key] = value || '';
            }
        }
    }

    // التأكد من وجود بيانات المورد
    if (!data.supplier_code) {
        data.supplier_code = '';
    }
    if (!data.supplier_name) {
        data.supplier_name = '';
    }

    // تشخيص: طباعة البيانات المجمعة
    console.log('🔍 البيانات المجمعة من النموذج:', data);
    console.log('📋 كود المورد:', data.supplier_code);
    console.log('📋 اسم المورد:', data.supplier_name);
    
    // جمع بيانات الأصناف
    const items = [];
    const rows = document.querySelectorAll('#itemsTableBody tr');
    rows.forEach(row => {
        const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
        const price = parseFloat(row.querySelector('input[name*="[price]"]').value) || 0;
        const discountRate = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

        const subtotal = quantity * price;
        const discountAmount = subtotal * (discountRate / 100);
        const totalPrice = subtotal - discountAmount;

        const item = {
            code: row.querySelector('input[name*="[code]"]').value,
            name: row.querySelector('input[name*="[name]"]').value,
            unit: row.querySelector('input[name*="[unit]"]').value,
            production_date: row.querySelector('input[name*="[production_date]"]').value,
            expiry_date: row.querySelector('input[name*="[expiry_date]"]').value,
            quantity: quantity,
            price: price,
            discount_rate: discountRate,
            total_price: totalPrice
        };
        if (item.name && item.quantity > 0 && item.price > 0) {
            items.push(item);
        }
    });
    
    data.items = items;

    // حساب الإجماليات
    data.subtotal = parseFloat(document.getElementById('subtotalAmount').textContent) || 0;
    data.total_discount = parseFloat(document.getElementById('totalDiscount').textContent) || 0;
    data.total_tax = parseFloat(document.getElementById('totalTax').textContent) || 0;
    data.total_amount = parseFloat(document.getElementById('grandTotal').textContent.replace(' ريال', '')) || 0;

    // إضافة الحقول الجديدة
    data.gross_amount = parseFloat(document.getElementById('grossAmount').value) || 0;
    data.discount_amount = parseFloat(document.getElementById('discountAmount').value) || 0;
    data.net_amount = parseFloat(document.getElementById('netAmount').value) || 0;

    // إضافة حقل الفرع
    data.branch_id = parseInt(document.getElementById('branchId').value) || 21;

    // تشخيص: طباعة الحقول المالية المهمة
    console.log('💰 الحقول المالية المرسلة:');
    console.log('   المبلغ الإجمالي (gross_amount):', data.gross_amount);
    console.log('   مبلغ الخصم (discount_amount):', data.discount_amount);
    console.log('   صافي المبلغ (net_amount):', data.net_amount);
    console.log('   المجموع الفرعي (subtotal):', data.subtotal);
    console.log('   إجمالي الخصم (total_discount):', data.total_discount);
    console.log('   المبلغ النهائي (total_amount):', data.total_amount);

    // التحقق الشامل من البيانات قبل الحفظ
    console.log('🔍 بدء التحقق الشامل من البيانات...');

    // 1. التحقق من البيانات الأساسية
    const missingBasicFields = validateBasicFields();
    if (missingBasicFields.length > 0) {
        showMissingFieldsWarning(missingBasicFields);
        return;
    }

    // 2. التحقق من بيانات الأصناف
    const itemsErrors = validateItemsData();
    if (itemsErrors.length > 0) {
        showItemsValidationErrors(itemsErrors);
        return;
    }

    // 3. التحقق من صحة الحقول المالية
    if (isNaN(data.gross_amount) || data.gross_amount < 0) {
        alert('خطأ في المبلغ الإجمالي. يرجى التحقق من البيانات.');
        return;
    }

    if (isNaN(data.discount_amount) || data.discount_amount < 0) {
        alert('خطأ في مبلغ الخصم. يرجى التحقق من البيانات.');
        return;
    }

    if (isNaN(data.net_amount) || data.net_amount < 0) {
        alert('خطأ في صافي المبلغ. يرجى التحقق من البيانات.');
        return;
    }

    // 4. التحقق من وجود أصناف صالحة
    if (!data.items || data.items.length === 0) {
        alert('يجب إضافة صنف واحد على الأقل لأمر الشراء.');
        return;
    }

    console.log('✅ تم اجتياز جميع فحوصات التحقق');
    console.log(`📊 عدد الأصناف: ${data.items.length}`);
    console.log(`💰 إجمالي المبلغ: ${data.gross_amount.toFixed(2)} ريال`);

    // التأكد من صحة المعادلة: صافي المبلغ = المبلغ الإجمالي - مبلغ الخصم
    const expectedNetAmount = data.gross_amount - data.discount_amount;
    if (Math.abs(data.net_amount - expectedNetAmount) > 0.01) {
        console.warn('⚠️ تحذير: عدم تطابق في حساب صافي المبلغ');
        console.warn(`   المتوقع: ${expectedNetAmount.toFixed(2)}`);
        console.warn(`   الفعلي: ${data.net_amount.toFixed(2)}`);

        // تصحيح القيمة
        data.net_amount = expectedNetAmount;
        document.getElementById('netAmount').value = expectedNetAmount.toFixed(2);
        console.log('✅ تم تصحيح صافي المبلغ إلى:', expectedNetAmount.toFixed(2));
    }

    // إرسال البيانات
    fetch('/purchase-orders/api/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            console.log('✅ تم حفظ أمر الشراء بنجاح');
            console.log('💰 الحقول المالية المحفوظة:');
            console.log(`   المبلغ الإجمالي: ${data.gross_amount.toFixed(2)} ريال`);
            console.log(`   مبلغ الخصم: ${data.discount_amount.toFixed(2)} ريال`);
            console.log(`   صافي المبلغ: ${data.net_amount.toFixed(2)} ريال`);

            alert(`تم حفظ أمر الشراء بنجاح!\n\nالمبلغ الإجمالي: ${data.gross_amount.toFixed(2)} ريال\nمبلغ الخصم: ${data.discount_amount.toFixed(2)} ريال\nصافي المبلغ: ${data.net_amount.toFixed(2)} ريال`);
            window.location.href = '/purchase-orders/';
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء حفظ أمر الشراء');
    });
});

// تحميل الفروع
async function loadBranches() {
    console.log('🔄 بدء تحميل الفروع...');

    try {
        const response = await fetch('/purchase-orders/api/branches');
        console.log('📡 استجابة API:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const branches = await response.json();
        console.log('📋 الفروع المستلمة:', branches);

        const branchSelect = document.getElementById('branchId');
        if (!branchSelect) {
            console.error('❌ لم يتم العثور على عنصر branchId');
            return;
        }

        branchSelect.innerHTML = '<option value="">اختر الفرع</option>';

        if (branches && branches.length > 0) {
            branches.forEach(branch => {
                const option = document.createElement('option');
                option.value = branch.brn_no;
                option.textContent = branch.brn_lname;
                branchSelect.appendChild(option);
                console.log(`✅ تم إضافة فرع: ${branch.brn_no} - ${branch.brn_lname}`);
            });

            // تعيين الفرع الافتراضي (21)
            branchSelect.value = '21';
            console.log('✅ تم تعيين الفرع الافتراضي: 21');
        } else {
            console.warn('⚠️ لا توجد فروع في الاستجابة');
            branchSelect.innerHTML = '<option value="21">الفرع الرئيسي</option>';
            branchSelect.value = '21';
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل الفروع:', error);

        // إضافة خيار افتراضي في حالة الخطأ
        const branchSelect = document.getElementById('branchId');
        if (branchSelect) {
            branchSelect.innerHTML = '<option value="21">الفرع الرئيسي</option>';
            branchSelect.value = '21';
            console.log('🔧 تم تعيين فرع افتراضي بسبب الخطأ');
        }
    }
}

// إضافة صنف افتراضي وتعيين التاريخ الحالي
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 تحميل صفحة أمر الشراء...');
    console.log('📊 وضع التعديل:', {{ 'true' if is_edit else 'false' }});

    // تحميل الفروع (احتياطي - البيانات محملة من الخادم)
    const branchSelect = document.getElementById('branchId');
    if (branchSelect && branchSelect.options.length <= 1) {
        console.log('🔄 تحميل الفروع احتياطياً...');
        loadBranches();
    } else {
        console.log('✅ الفروع محملة من الخادم:', branchSelect.options.length - 1, 'فرع');
    }

    {% if is_edit and po_data %}
        console.log('📝 بيانات أمر الشراء للتعديل:');
        console.log('   ID:', {{ po_data[0] }});
        console.log('   رقم الأمر:', '{{ po_data[1] }}');
        console.log('   العنوان:', '{{ po_data[4] }}');
        console.log('   كود المورد:', '{{ po_data[2] }}');
        console.log('   اسم المورد:', '{{ po_data[3] }}');

        // تحميل البيانات الأساسية
        loadPODataForEdit();

        {% if po_items %}
            console.log('📦 عدد الأصناف:', {{ po_items|length }});
            // تحميل الأصناف
            loadPOItemsForEdit();
        {% endif %}
    {% else %}
        console.log('➕ وضع إنشاء أمر جديد');
        // إضافة صنف افتراضي
        addItem();
    {% endif %}

    // تعيين التاريخ الحالي إذا لم يكن محدد
    const poDateField = document.getElementById('poDate');
    if (!poDateField.value) {
        const today = new Date().toISOString().split('T')[0];
        poDateField.value = today;
    }

    // تنظيف حقول المورد من أي قيم None
    const supplierCodeField = document.getElementById('supplierCode');
    const supplierNameField = document.getElementById('supplierName');

    if (supplierCodeField && (supplierCodeField.value === 'None' || supplierCodeField.value === 'none' || supplierCodeField.value === 'null')) {
        supplierCodeField.value = '';
    }

    if (supplierNameField && (supplierNameField.value === 'None' || supplierNameField.value === 'none' || supplierNameField.value === 'null')) {
        supplierNameField.value = '';
    }

    // تفعيل شاشة بحث الموردين
    initSupplierSearch();

    // تفعيل نظام بحث الأصناف
    initItemSearch();

    // تفعيل نظام التحقق من البيانات الأساسية
    initBasicFieldsValidation();

    // تحديث عرض العملة عند تحميل الصفحة
    setTimeout(() => {
        updateCurrencyDisplay();
    }, 100);
});

// تفعيل شاشة بحث الموردين
function initSupplierSearch() {
    const supplierCodeInput = document.getElementById('supplierCode');
    const searchBtn = document.getElementById('searchSupplierBtn');

    // التأكد من وجود العناصر
    if (!supplierCodeInput || !searchBtn) {
        console.error('عناصر البحث غير موجودة');
        return;
    }

    // فتح شاشة البحث بالضغط على F9
    document.addEventListener('keydown', function(e) {
        if (e.key === 'F9') {
            e.preventDefault();
            console.log('تم الضغط على F9');

            // التحقق من التركيز على حقل كود المورد
            if (document.activeElement === supplierCodeInput) {
                openSupplierSearchWindow();
            }
        }
    });

    // فتح شاشة البحث بالضغط على زر البحث
    searchBtn.addEventListener('click', function(e) {
        e.preventDefault();
        openSupplierSearchWindow();
    });

    // تفعيل البحث في النافذة
    const searchSuppliersBtn = document.getElementById('searchSuppliersBtn');
    if (searchSuppliersBtn) {
        searchSuppliersBtn.addEventListener('click', function() {
            console.log('بدء البحث');
            searchSuppliers();
        });
    }

    // مسح البحث
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', function() {
            console.log('مسح البحث');
            clearSupplierSearch();
        });
    }

    // البحث عند الضغط على Enter في حقول البحث
    ['searchSupplierCode', 'searchSupplierName'].forEach(function(fieldId) {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('بحث من Enter');
                    searchSuppliers();
                }
            });
        }
    });

    // إضافة معالج إضافي للنافذة
    const modalElement = document.getElementById('supplierSearchModal');
    if (modalElement) {
        // معالج عند إغلاق النافذة
        modalElement.addEventListener('hidden.bs.modal', function() {
            console.log('تم إغلاق نافذة البحث');
            // تنظيف أي backdrop متبقي
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            document.body.classList.remove('modal-open');
        });

        // معالج عند فتح النافذة
        modalElement.addEventListener('shown.bs.modal', function() {
            console.log('تم فتح نافذة البحث بنجاح');
        });
    }

    console.log('تم تفعيل نظام بحث الموردين');
}

// ==================== نظام بحث الأصناف ====================

// تفعيل نظام بحث الأصناف
function initItemSearch() {
    console.log('🔍 تفعيل نظام بحث الأصناف...');

    // تفعيل البحث بـ F9 في حقول كود الصنف
    document.addEventListener('keydown', function(e) {
        if (e.key === 'F9') {
            const activeElement = document.activeElement;
            if (activeElement && activeElement.name && activeElement.name.includes('[code]')) {
                e.preventDefault();
                openItemSearchModal();
                // حفظ مرجع للحقل النشط
                window.currentItemRow = activeElement.closest('tr');
            }
        }
    });

    // تفعيل البحث المباشر عند blur في حقول كود الصنف
    document.addEventListener('blur', function(e) {
        if (e.target && e.target.name && e.target.name.includes('[code]')) {
            handleDirectItemSearch(e.target);
        }
    }, true);



    console.log('✅ تم تفعيل نظام بحث الأصناف');
}

// ==================== دوال تحميل البيانات للتعديل ====================

// تحميل البيانات الأساسية لأمر الشراء
function loadPODataForEdit() {
    console.log('📝 تحميل البيانات الأساسية للتعديل...');

    {% if po_data %}
        // تحميل البيانات الأساسية
        const poData = {
            id: {{ po_data[0] }},
            po_number: '{{ po_data[1] }}',
            supplier_code: '{{ po_data[2] or "" }}',
            supplier_name: '{{ po_data[3] or "" }}',
            title: '{{ po_data[4] or "" }}',
            po_date: '{{ po_data[5].strftime("%Y-%m-%d") if po_data[5] else "" }}',
            delivery_date: '{{ po_data[6].strftime("%Y-%m-%d") if po_data[6] else "" }}',
            delivery_address: '{{ po_data[7] or "" }}',
            payment_terms: '{{ po_data[8] or "" }}',
            currency: '{{ po_data[9] or "ريال" }}',
            priority: '{{ po_data[10] or "عادي" }}',
            expected_delivery_days: {{ po_data[11] or 0 }},
            description: '{{ po_data[12] or "" }}',
            notes: '{{ po_data[13] or "" }}',
            status: '{{ po_data[14] or "مسودة" }}',
            gross_amount: {{ po_data[17] or 0 }},
            discount_amount: {{ po_data[18] or 0 }},
            net_amount: {{ po_data[19] or 0 }},
            contract_id: {{ po_data[20] or 'null' }}
        };

        console.log('📊 البيانات المحملة:', poData);

        // دالة مساعدة لتعيين قيمة حقل مع التحقق من وجوده
        function setFieldValue(fieldId, value) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.value = value;
                console.log(`✅ تم تعيين ${fieldId}: ${value}`);
            } else {
                console.warn(`⚠️ لم يتم العثور على الحقل: ${fieldId}`);
            }
        }

        // تعبئة الحقول مع التحقق من وجودها
        setFieldValue('title', poData.title);
        setFieldValue('poDate', poData.po_date);
        setFieldValue('supplierCode', poData.supplier_code);
        setFieldValue('supplierName', poData.supplier_name);
        setFieldValue('deliveryDate', poData.delivery_date);
        setFieldValue('deliveryAddress', poData.delivery_address);
        setFieldValue('paymentTerms', poData.payment_terms);
        setFieldValue('currency', poData.currency);
        setFieldValue('priority', poData.priority);
        setFieldValue('expectedDeliveryDays', poData.expected_delivery_days);
        setFieldValue('description', poData.description);
        setFieldValue('notes', poData.notes);
        setFieldValue('status', poData.status);
        setFieldValue('grossAmount', poData.gross_amount.toFixed(2));
        setFieldValue('discountAmount', poData.discount_amount.toFixed(2));
        setFieldValue('netAmount', poData.net_amount.toFixed(2));

        // تحديد العقد إذا كان موجود
        if (poData.contract_id && poData.contract_id !== 'null') {
            const contractSelect = document.getElementById('contractId');
            if (contractSelect) {
                contractSelect.value = poData.contract_id;
                console.log(`✅ تم تحديد العقد: ${poData.contract_id}`);
            } else {
                console.warn('⚠️ لم يتم العثور على حقل العقد');
            }
        }

        console.log('✅ تم تحميل البيانات الأساسية بنجاح');
    {% else %}
        console.log('📭 لا توجد بيانات أساسية لتحميلها');
    {% endif %}
}

// تحميل أصناف أمر الشراء
function loadPOItemsForEdit() {
    console.log('📦 تحميل أصناف أمر الشراء للتعديل...');

    {% if po_items %}
        // مسح الجدول أولاً
        const tbody = document.getElementById('itemsTableBody');
        if (!tbody) {
            console.error('❌ لم يتم العثور على جدول الأصناف');
            return;
        }

        tbody.innerHTML = '';
        itemCounter = 0;

        // تحميل الأصناف
        const items = [
            {% for item in po_items %}
            {
                id: {{ item[0] }},
                code: '{{ item[1] or "" }}',
                name: '{{ item[2] or "" }}',
                unit: '{{ item[3] or "" }}',
                production_date: '{{ item[4].strftime("%Y-%m-%d") if item[4] else "" }}',
                expiry_date: '{{ item[5].strftime("%Y-%m-%d") if item[5] else "" }}',
                quantity: {{ item[6] or 0 }},
                unit_price: {{ item[7] or 0 }},
                total_price: {{ item[8] or 0 }}
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        console.log('📊 الأصناف المحملة:', items);
        console.log(`📈 عدد الأصناف: ${items.length}`);

        if (items.length === 0) {
            console.warn('⚠️ لا توجد أصناف لتحميلها');
            // إضافة صنف فارغ
            addItem();
            return;
        }

        // إضافة كل صنف للجدول
        items.forEach((item, index) => {
            console.log(`📦 معالجة الصنف ${index + 1}:`, item);
            try {
                addItemFromData(item);
            } catch (error) {
                console.error(`❌ خطأ في إضافة الصنف ${index + 1}:`, error);
            }
        });

        // حساب الإجماليات
        try {
            console.log('🧮 حساب الإجماليات...');
            calculateTotal();
        } catch (error) {
            console.error('❌ خطأ في حساب الإجماليات:', error);
        }

        console.log('✅ تم تحميل الأصناف بنجاح');
    {% else %}
        console.log('📭 لا توجد أصناف محفوظة - إضافة صنف فارغ');
        try {
            addItem();
        } catch (error) {
            console.error('❌ خطأ في إضافة صنف فارغ:', error);
        }
    {% endif %}
}

// إضافة صنف من البيانات المحملة
function addItemFromData(itemData) {
    itemCounter++;
    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');

    console.log(`📦 إضافة صنف ${itemCounter}:`, itemData);

    // حساب نسبة الخصم من السعر والإجمالي
    const subtotal = itemData.quantity * itemData.unit_price;
    const discountAmount = subtotal - itemData.total_price;
    const discountRate = subtotal > 0 ? (discountAmount / subtotal * 100) : 0;

    console.log(`   المجموع الفرعي: ${subtotal}, الخصم: ${discountAmount}, نسبة الخصم: ${discountRate}%`);

    row.innerHTML = `
        <td><input type="text" class="form-control form-control-sm required-field" name="items[${itemCounter}][code]" value="${itemData.code}" placeholder="كود الصنف *" required style="width: 100%; border-left: 3px solid #198754;"></td>
        <td><input type="text" class="form-control form-control-sm required-field" name="items[${itemCounter}][name]" value="${itemData.name}" placeholder="اسم الصنف *" required style="width: 100%; border-left: 3px solid #198754;"></td>
        <td><input type="text" class="form-control form-control-sm" name="items[${itemCounter}][unit]" value="${itemData.unit}" placeholder="الوحدة" style="width: 100%;"></td>
        <td><input type="number" class="form-control form-control-sm required-field" name="items[${itemCounter}][quantity]" value="${itemData.quantity}" placeholder="الكمية *" step="0.01" min="0.01" required onchange="calculateRowTotal(this)" style="width: 100%; border-left: 3px solid #198754;"></td>
        <td><input type="number" class="form-control form-control-sm required-field" name="items[${itemCounter}][price]" value="${itemData.unit_price}" placeholder="السعر *" step="0.01" min="0.01" required onchange="calculateRowTotal(this)" style="width: 100%; border-left: 3px solid #198754;"></td>
        <td><input type="date" class="form-control form-control-sm" name="items[${itemCounter}][production_date]" value="${itemData.production_date}" style="width: 100%;"></td>
        <td><input type="date" class="form-control form-control-sm required-field" name="items[${itemCounter}][expiry_date]" value="${itemData.expiry_date}" required style="width: 100%; border-left: 3px solid #198754;" title="تاريخ الانتهاء مطلوب"></td>
        <td><input type="number" class="form-control form-control-sm" name="items[${itemCounter}][discount]" value="${discountRate.toFixed(2)}" placeholder="0" step="0.01" min="0" max="100" onchange="calculateRowTotal(this)" style="width: 100%;"></td>
        <td><span class="row-total">${itemData.total_price.toFixed(2)}</span></td>
        <td><button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)"><i class="fas fa-trash"></i></button></td>
    `;

    tbody.appendChild(row);

    console.log(`✅ تم إضافة الصنف ${itemCounter} بنجاح`);
}

// ==================== نظام التحقق من البيانات الأساسية ====================

// قائمة الحقول المطلوبة في البيانات الأساسية
const requiredBasicFields = [
    { id: 'title', name: 'عنوان أمر الشراء' },
    { id: 'poDate', name: 'تاريخ أمر الشراء' },
    { id: 'supplierCode', name: 'كود المورد' },
    { id: 'supplierName', name: 'اسم المورد' }
];

// التحقق من البيانات الأساسية
function validateBasicFields() {
    const missingFields = [];

    for (const field of requiredBasicFields) {
        const element = document.getElementById(field.id);
        if (!element || !element.value.trim()) {
            missingFields.push(field.name);
        }
    }

    return missingFields;
}

// عرض رسالة تحذيرية للحقول المفقودة
function showMissingFieldsWarning(missingFields) {
    if (missingFields.length === 0) return false;

    const fieldsList = missingFields.join('، ');
    const message = `يجب إدخال البيانات التالية أولاً:\n\n${fieldsList}\n\nيرجى إكمال البيانات الأساسية قبل إضافة الأصناف.`;

    // عرض رسالة تحذيرية أنيقة
    showTemporaryMessage(`⚠️ بيانات مفقودة: ${fieldsList}`, 'warning');

    // عرض alert مفصل
    alert(message);

    // تركيز على أول حقل مفقود
    const firstMissingField = requiredBasicFields.find(field => missingFields.includes(field.name));
    if (firstMissingField) {
        const element = document.getElementById(firstMissingField.id);
        if (element) {
            element.focus();
            element.style.borderColor = '#dc3545';
            element.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';

            // إزالة التأثير بعد 3 ثوان
            setTimeout(() => {
                element.style.borderColor = '';
                element.style.boxShadow = '';
            }, 3000);
        }
    }

    return true;
}

// معالج النقر على جدول الأصناف
function handleItemsTableClick(event) {
    const missingFields = validateBasicFields();
    if (missingFields.length > 0) {
        event.preventDefault();
        event.stopPropagation();
        showMissingFieldsWarning(missingFields);
        return false;
    }
    return true;
}

// تفعيل التحقق من البيانات الأساسية
function initBasicFieldsValidation() {
    // إضافة معالج للنقر على جدول الأصناف
    const itemsTable = document.getElementById('itemsTable');
    if (itemsTable) {
        itemsTable.addEventListener('click', handleItemsTableClick);
    }

    // إضافة معالج لحقول الإدخال في جدول الأصناف
    document.addEventListener('focusin', function(e) {
        if (e.target.closest('#itemsTable')) {
            const missingFields = validateBasicFields();
            if (missingFields.length > 0) {
                e.target.blur();
                showMissingFieldsWarning(missingFields);
            }
        }
    });

    // إضافة معالج لتحديث لون الحقول عند التعبئة
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('required-field')) {
            if (e.target.value.trim()) {
                e.target.style.borderLeftColor = '#198754'; // أخضر عند التعبئة
            } else {
                e.target.style.borderLeftColor = '#dc3545'; // أحمر عند الفراغ
            }
        }
    });

    console.log('✅ تم تفعيل نظام التحقق من البيانات الأساسية');
}

// ==================== التحقق من بيانات الأصناف ====================

// قائمة الحقول المطلوبة في كل صنف
const requiredItemFields = [
    { name: '[code]', label: 'كود الصنف' },
    { name: '[name]', label: 'اسم الصنف' },
    { name: '[quantity]', label: 'الكمية' },
    { name: '[price]', label: 'سعر الوحدة' },
    { name: '[expiry_date]', label: 'تاريخ الانتهاء' }
];

// التحقق من بيانات الأصناف
function validateItemsData() {
    const rows = document.querySelectorAll('#itemsTableBody tr');
    const errors = [];

    if (rows.length === 0) {
        errors.push('يجب إضافة صنف واحد على الأقل');
        return errors;
    }

    rows.forEach((row, index) => {
        const rowNumber = index + 1;
        const missingFields = [];

        for (const field of requiredItemFields) {
            const input = row.querySelector(`input[name*="${field.name}"]`);
            if (!input || !input.value.trim()) {
                missingFields.push(field.label);
            }
        }

        if (missingFields.length > 0) {
            errors.push(`الصنف رقم ${rowNumber}: ${missingFields.join('، ')}`);
        }

        // التحقق من صحة القيم الرقمية
        const quantityInput = row.querySelector('input[name*="[quantity]"]');
        const priceInput = row.querySelector('input[name*="[price]"]');

        if (quantityInput && quantityInput.value) {
            const quantity = parseFloat(quantityInput.value);
            if (isNaN(quantity) || quantity <= 0) {
                errors.push(`الصنف رقم ${rowNumber}: الكمية يجب أن تكون رقم أكبر من صفر`);
            }
        }

        if (priceInput && priceInput.value) {
            const price = parseFloat(priceInput.value);
            if (isNaN(price) || price <= 0) {
                errors.push(`الصنف رقم ${rowNumber}: السعر يجب أن يكون رقم أكبر من صفر`);
            }
        }
    });

    return errors;
}

// عرض أخطاء بيانات الأصناف
function showItemsValidationErrors(errors) {
    if (errors.length === 0) return false;

    const errorMessage = `يرجى إصلاح الأخطاء التالية:\n\n${errors.join('\n')}`;

    // عرض رسالة تحذيرية
    showTemporaryMessage('❌ أخطاء في بيانات الأصناف', 'error');

    // عرض alert مفصل
    alert(errorMessage);

    return true;
}

// فتح نافذة بحث الأصناف (نافذة منفصلة)
function openItemSearchModal() {
    try {
        // إنشاء URL لنافذة البحث
        const searchUrl = '/purchase-orders/item-search-window';

        // فتح نافذة منفصلة
        const searchWindow = window.open(
            searchUrl,
            'itemSearchWindow',
            'width=1000,height=700,scrollbars=yes,resizable=yes,centerscreen=yes'
        );

        if (!searchWindow) {
            alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
            return;
        }

        // معالج استقبال البيانات من النافذة
        window.addEventListener('message', function(event) {
            if (event.origin !== window.location.origin) return;

            if (event.data.type === 'itemSelected') {
                const itemData = event.data;

                // التأكد من وجود الصف النشط
                if (window.currentItemRow) {
                    const row = window.currentItemRow;

                    // تحديث حقول الصنف
                    const codeInput = row.querySelector('input[name*="[code]"]');
                    const nameInput = row.querySelector('input[name*="[name]"]');
                    const unitInput = row.querySelector('input[name*="[unit]"]');

                    if (codeInput) codeInput.value = itemData.code || '';
                    if (nameInput) nameInput.value = itemData.name || '';
                    if (unitInput) unitInput.value = itemData.unit || '';

                    // عرض رسالة نجاح
                    showTemporaryMessage(`✅ تم اختيار الصنف: ${itemData.name}`, 'success');

                    // تركيز على الحقل التالي
                    setTimeout(() => {
                        const quantityInput = row.querySelector('input[name*="[quantity]"]');
                        if (quantityInput) {
                            quantityInput.focus();
                        }
                    }, 500);
                }

                searchWindow.close();
            }
        });

        console.log('تم فتح نافذة بحث الأصناف');

    } catch (error) {
        console.error('خطأ في فتح نافذة البحث:', error);
        alert('حدث خطأ في فتح نافذة البحث');
    }
}

// البحث المباشر عن صنف بالكود
function handleDirectItemSearch(codeInput) {
    const itemCode = codeInput.value.trim();

    if (!itemCode || itemCode.toLowerCase() === 'none') {
        return;
    }

    const row = codeInput.closest('tr');
    const nameInput = row.querySelector('input[name*="[name]"]');
    const unitInput = row.querySelector('input[name*="[unit]"]');

    // عرض مؤشر التحميل
    if (nameInput) {
        nameInput.value = 'جاري البحث...';
        nameInput.style.color = '#6c757d';
    }

    // البحث المباشر
    fetch(`/purchase-orders/api/get-item-by-code/${encodeURIComponent(itemCode)}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.item) {
            // تم العثور على الصنف
            const item = data.item;

            if (nameInput) {
                nameInput.value = item.name;
                nameInput.style.color = '#198754'; // أخضر للنجاح
            }

            if (unitInput) {
                unitInput.value = item.unit;
            }

            // عرض رسالة نجاح مؤقتة
            showTemporaryMessage(`✅ تم العثور على الصنف: ${item.name}`, 'success');

            console.log('✅ تم العثور على الصنف:', item);

        } else {
            // لم يتم العثور على الصنف
            if (nameInput) {
                nameInput.value = '';
                nameInput.style.color = '#dc3545'; // أحمر للخطأ
            }

            if (unitInput) {
                unitInput.value = '';
            }

            // عرض رسالة خطأ مؤقتة
            showTemporaryMessage(`❌ ${data.message}`, 'error');

            console.log('❌ لم يتم العثور على الصنف:', data.message);
        }
    })
    .catch(error => {
        console.error('خطأ في البحث عن الصنف:', error);

        if (nameInput) {
            nameInput.value = '';
            nameInput.style.color = '#dc3545';
        }

        showTemporaryMessage('❌ حدث خطأ أثناء البحث عن الصنف', 'error');
    })
    .finally(() => {
        // إعادة لون النص للطبيعي بعد ثانيتين
        setTimeout(() => {
            if (nameInput) {
                nameInput.style.color = '';
            }
        }, 2000);
    });
}





// إضافة معالج Enter للبحث السريع في حقل كود المورد
document.getElementById('supplierCode').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        // تشغيل البحث المباشر
        this.blur(); // سيؤدي إلى تشغيل معالج blur
    }
});

// فتح نافذة بحث الموردين (حل بسيط وفعال)
function openSupplierSearchWindow() {
    const width = 1000;
    const height = 600;
    const left = (screen.width - width) / 2;
    const top = (screen.height - height) / 2;

    const searchWindow = window.open(
        '/purchase-orders/supplier-search',
        'supplierSearch',
        `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
    );

    if (searchWindow) {
        searchWindow.focus();

        // معالج استقبال البيانات من النافذة
        window.addEventListener('message', function(event) {
            if (event.origin !== window.location.origin) return;

            if (event.data.type === 'supplierSelected') {
                document.getElementById('supplierCode').value = event.data.code || '';
                document.getElementById('supplierName').value = event.data.name || '';

                // إلغاء اختيار العقد
                document.getElementById('contractSelect').value = '';

                searchWindow.close();
            }
        });
    } else {
        alert('لم يتم فتح نافذة البحث. تأكد من السماح للنوافذ المنبثقة.');
    }
}

// فتح شاشة بحث الموردين (النسخة القديمة - احتياطي)
function openSupplierSearch() {
    try {
        const modalElement = document.getElementById('supplierSearchModal');

        // التأكد من وجود العنصر
        if (!modalElement) {
            console.error('نافذة البحث غير موجودة');
            alert('خطأ: نافذة البحث غير متاحة');
            return;
        }

        // إزالة أي modal مفتوح مسبقاً
        const existingModal = bootstrap.Modal.getInstance(modalElement);
        if (existingModal) {
            existingModal.dispose();
        }

        // إنشاء modal جديد
        const modal = new bootstrap.Modal(modalElement, {
            backdrop: 'static',
            keyboard: true
        });

        modal.show();

        // تركيز على حقل البحث الأول
        modalElement.addEventListener('shown.bs.modal', function() {
            document.getElementById('searchSupplierCode').focus();
        }, { once: true });

        console.log('تم فتح نافذة البحث');

    } catch (error) {
        console.error('خطأ في فتح نافذة البحث:', error);
        alert('حدث خطأ في فتح نافذة البحث');
    }
}

// البحث عن الموردين
function searchSuppliers() {
    const searchData = {
        code: document.getElementById('searchSupplierCode').value.trim(),
        name: document.getElementById('searchSupplierName').value.trim(),
        type: document.getElementById('searchSupplierType').value
    };

    const resultsDiv = document.getElementById('supplierSearchResults');

    // عرض مؤشر التحميل
    resultsDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري البحث...</span>
            </div>
            <p class="mt-2">جاري البحث عن الموردين...</p>
        </div>
    `;

    // إرسال طلب البحث
    fetch('/purchase-orders/api/search-suppliers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displaySupplierResults(data.suppliers);
        } else {
            resultsDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${data.message || 'لم يتم العثور على موردين'}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('خطأ في البحث:', error);
        resultsDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-times-circle me-2"></i>
                حدث خطأ أثناء البحث عن الموردين
            </div>
        `;
    });
}

// عرض نتائج البحث
function displaySupplierResults(suppliers) {
    const resultsDiv = document.getElementById('supplierSearchResults');

    if (!suppliers || suppliers.length === 0) {
        resultsDiv.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                لم يتم العثور على موردين مطابقين لمعايير البحث
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-primary">
                    <tr>
                        <th>كود المورد</th>
                        <th>اسم المورد</th>
                        <th>النوع</th>
                        <th>الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>الحالة</th>
                        <th>اختيار</th>
                    </tr>
                </thead>
                <tbody>
    `;

    suppliers.forEach(function(supplier) {
        const statusBadge = supplier.is_active ?
            '<span class="badge bg-success">نشط</span>' :
            '<span class="badge bg-secondary">غير نشط</span>';

        html += `
            <tr style="cursor: pointer;" onclick="selectSupplier('${supplier.code}', '${supplier.name}')">
                <td><strong>${supplier.code || 'غير محدد'}</strong></td>
                <td>${supplier.name || 'غير محدد'}</td>
                <td>${getSupplierTypeLabel(supplier.type)}</td>
                <td>${supplier.phone || '-'}</td>
                <td>${supplier.email || '-'}</td>
                <td>${statusBadge}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-primary"
                            onclick="selectSupplier('${supplier.code}', '${supplier.name}')">
                        <i class="fas fa-check me-1"></i>اختيار
                    </button>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    resultsDiv.innerHTML = html;
}

// اختيار مورد من نتائج البحث
function selectSupplier(code, name) {
    try {
        // تحديث حقول المورد
        const supplierCodeField = document.getElementById('supplierCode');
        const supplierNameField = document.getElementById('supplierName');

        if (supplierCodeField) {
            supplierCodeField.value = code || '';
        }

        if (supplierNameField) {
            supplierNameField.value = name || '';
        }

        // إغلاق نافذة البحث
        const modalElement = document.getElementById('supplierSearchModal');
        const modal = bootstrap.Modal.getInstance(modalElement);

        if (modal) {
            modal.hide();
        } else {
            // إغلاق بديل
            modalElement.style.display = 'none';
            modalElement.classList.remove('show');
            document.body.classList.remove('modal-open');

            // إزالة backdrop
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
        }

        // إلغاء اختيار العقد
        const contractSelect = document.getElementById('contractSelect');
        if (contractSelect) {
            contractSelect.value = '';
        }

        // تركيز على الحقل التالي
        setTimeout(function() {
            if (supplierNameField) {
                supplierNameField.focus();
            }
        }, 300);

        console.log('تم اختيار المورد:', code, name);

    } catch (error) {
        console.error('خطأ في اختيار المورد:', error);
        alert('حدث خطأ في اختيار المورد');
    }
}

// مسح البحث
function clearSupplierSearch() {
    document.getElementById('searchSupplierCode').value = '';
    document.getElementById('searchSupplierName').value = '';
    document.getElementById('searchSupplierType').value = '';

    document.getElementById('supplierSearchResults').innerHTML = `
        <div class="text-center">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <p class="text-muted">اضغط "بحث" لعرض الموردين أو استخدم معايير البحث أعلاه</p>
        </div>
    `;
}

// تسميات أنواع الموردين
function getSupplierTypeLabel(type) {
    const types = {
        'company': 'شركة',
        'individual': 'فرد',
        'government': 'جهة حكومية',
        'international': 'مورد دولي'
    };
    return types[type] || 'غير محدد';
}

// إغلاق قسري للنافذة
function forceCloseModal() {
    try {
        const modalElement = document.getElementById('supplierSearchModal');

        // طريقة 1: استخدام Bootstrap Modal
        const modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.hide();
        }

        // طريقة 2: إغلاق يدوي
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
        modalElement.setAttribute('aria-hidden', 'true');
        modalElement.removeAttribute('aria-modal');

        // إزالة backdrop
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());

        // إزالة class من body
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        console.log('تم إغلاق النافذة قسرياً');

    } catch (error) {
        console.error('خطأ في الإغلاق القسري:', error);

        // إغلاق طارئ
        location.reload();
    }
}

// إضافة معالج ESC للإغلاق
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const supplierModal = document.getElementById('supplierSearchModal');

        if (supplierModal && supplierModal.classList.contains('show')) {
            forceCloseModal();
        }
    }
});

// تحديث عرض العملة
function updateCurrencyDisplay() {
    const currencySelect = document.getElementById('currency');
    const selectedOption = currencySelect.options[currencySelect.selectedIndex];

    if (selectedOption) {
        const symbol = selectedOption.getAttribute('data-symbol');
        const name = selectedOption.getAttribute('data-name');

        console.log(`💰 تم تغيير العملة إلى: ${symbol} - ${name}`);

        // يمكن إضافة المزيد من التحديثات هنا مثل:
        // - تحديث رموز العملة في جدول الأصناف
        // - تحديث حسابات الإجمالي
        // - تحديث عرض الأسعار

        // إعادة حساب الإجمالي مع العملة الجديدة
        calculateTotal();

        // تحديث رموز العملة في الجدول
        updateCurrencySymbolsInTable();
    }
}

// تحديث رموز العملة في جدول الأصناف
function updateCurrencySymbolsInTable() {
    const currencySelect = document.getElementById('currency');
    const selectedOption = currencySelect.options[currencySelect.selectedIndex];

    if (selectedOption) {
        const symbol = selectedOption.getAttribute('data-symbol') || 'ريال';

        console.log(`🔄 تحديث رموز العملة إلى: ${symbol}`);

        // تحديث رموز العملة في ملخص الإجمالي
        const totalLabels = document.querySelectorAll('.currency-symbol');
        totalLabels.forEach(label => {
            label.textContent = symbol;
        });

        // تحديث placeholder في حقول الأسعار
        const priceInputs = document.querySelectorAll('input[name*="[unit_price]"], input[name*="[price]"]');
        priceInputs.forEach(input => {
            const currentPlaceholder = input.getAttribute('placeholder') || '';
            if (currentPlaceholder && !currentPlaceholder.includes(symbol)) {
                // تحديث placeholder ليشمل رمز العملة
                input.setAttribute('placeholder', `0.00 ${symbol}`);
            }
        });

        console.log(`✅ تم تحديث ${totalLabels.length} رمز عملة و ${priceInputs.length} حقل سعر`);
    }
}
</script>

<style>
/* تحسين مظهر النموذج */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* تحسين مظهر التبويبات */
.nav-tabs .nav-link {
    border-radius: 0.375rem 0.375rem 0 0;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    background-color: #0d6efd;
    color: white;
    border-color: #0d6efd;
}

/* تحسين مظهر الجدول */
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-top: none;
}

.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

/* تحسين مظهر الأزرار */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

/* تحسين مظهر البطاقات */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0;
}

/* تحسين المساحات */
.mb-3 {
    margin-bottom: 1rem !important;
}

/* تحسين مظهر الحقول الصغيرة */
.col-md-3 .form-control,
.col-md-3 .form-select {
    font-size: 0.9rem;
}

/* تحسين مظهر الرسائل التوضيحية */
.alert-info {
    border-left: 4px solid #0dcaf0;
    background-color: #f8f9fa;
    border-color: #b8daff;
}

/* تحسين مظهر الأيقونات */
.fas {
    margin-right: 0.5rem;
}

/* تحسين responsive */
@media (max-width: 768px) {
    .col-md-3, .col-md-4, .col-md-6, .col-md-8 {
        margin-bottom: 1rem;
    }
}

/* تحسين نافذة بحث الموردين */
#supplierSearchModal .modal-dialog {
    max-width: 90%;
}

#supplierSearchModal .table {
    font-size: 0.9rem;
}

#supplierSearchModal .table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

#supplierSearchModal .table tbody tr:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

#supplierSearchModal .badge {
    font-size: 0.75rem;
}

/* تحسين حقل البحث */
.input-group .btn-outline-secondary {
    border-color: #ced4da;
}

.input-group .btn-outline-secondary:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

/* تحسين مؤشر التحميل */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* تحسين الجدول المتجاوب */
.table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

.table-responsive::-webkit-scrollbar {
    width: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* تحسين حقل كود المورد */
#supplierCode {
    transition: all 0.3s ease;
}

#supplierCode:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تحسين حقل اسم المورد */
#supplierName {
    transition: color 0.3s ease;
}

/* تحسين النص التوضيحي */
.form-text {
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    border-left: 3px solid #667eea;
    margin-top: 8px;
}

.form-text i {
    color: #667eea;
}

/* تحسين الرسائل المؤقتة */
#tempMessage {
    animation: slideInRight 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسين زر البحث */
.input-group .btn-outline-secondary {
    transition: all 0.3s ease;
}

.input-group .btn-outline-secondary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
    transform: scale(1.05);
}

/* تحسين جدول الأصناف */
#itemsTable {
    table-layout: fixed;
    width: 100%;
}

#itemsTable th,
#itemsTable td {
    padding: 8px 4px;
    vertical-align: middle;
    text-align: center;
}

/* تخصيص عرض الأعمدة */
#itemsTable th:nth-child(1), /* كود الصنف */
#itemsTable td:nth-child(1) {
    width: 120px;
    min-width: 120px;
}

#itemsTable th:nth-child(2), /* اسم الصنف */
#itemsTable td:nth-child(2) {
    width: 280px;
    min-width: 280px;
    text-align: right;
}

#itemsTable th:nth-child(3), /* الوحدة */
#itemsTable td:nth-child(3) {
    width: 80px;
    min-width: 80px;
}

#itemsTable th:nth-child(4), /* الكمية */
#itemsTable td:nth-child(4) {
    width: 100px;
    min-width: 100px;
}

#itemsTable th:nth-child(5), /* السعر */
#itemsTable td:nth-child(5) {
    width: 120px;
    min-width: 120px;
}

#itemsTable th:nth-child(6), /* تاريخ الإنتاج */
#itemsTable td:nth-child(6) {
    width: 130px;
    min-width: 130px;
}

#itemsTable th:nth-child(7), /* تاريخ الانتهاء */
#itemsTable td:nth-child(7) {
    width: 130px;
    min-width: 130px;
}

#itemsTable th:nth-child(8), /* الخصم */
#itemsTable td:nth-child(8) {
    width: 80px;
    min-width: 80px;
}

#itemsTable th:nth-child(9), /* الإجمالي */
#itemsTable td:nth-child(9) {
    width: 120px;
    min-width: 120px;
}

#itemsTable th:nth-child(10), /* العمليات */
#itemsTable td:nth-child(10) {
    width: 100px;
    min-width: 100px;
}

/* تحسين حقول الإدخال في الجدول */
#itemsTable input {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 6px;
    font-size: 13px;
}

#itemsTable input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.1rem rgba(102, 126, 234, 0.25);
}

/* تحسين عرض الإجمالي */
.row-total {
    font-weight: bold;
    color: #198754;
    font-size: 14px;
}

/* تنسيق الحقول المطلوبة */
.required-field {
    border-left: 3px solid #dc3545 !important;
}

.required-field:focus {
    border-left: 3px solid #0d6efd !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.required-field:valid {
    border-left: 3px solid #198754 !important;
}

/* تحسين رسائل التحذير */
.warning-message {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;
    color: #856404;
    font-weight: 500;
}

.error-message {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 1px solid #dc3545;
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;
    color: #721c24;
    font-weight: 500;
}

/* تحسين عناوين الأعمدة المطلوبة */
th .fa-exclamation-circle {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
</style>



<!-- نافذة بحث الموردين -->
<div class="modal fade" id="supplierSearchModal" tabindex="-1" aria-labelledby="supplierSearchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="supplierSearchModalLabel">
                    <i class="fas fa-search me-2"></i>البحث عن مورد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <!-- شريط البحث -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="searchSupplierCode" class="form-label">كود المورد</label>
                        <input type="text" class="form-control" id="searchSupplierCode" placeholder="ابحث بالكود...">
                    </div>
                    <div class="col-md-4">
                        <label for="searchSupplierName" class="form-label">اسم المورد</label>
                        <input type="text" class="form-control" id="searchSupplierName" placeholder="ابحث بالاسم...">
                    </div>
                    <div class="col-md-4">
                        <label for="searchSupplierType" class="form-label">نوع المورد</label>
                        <select class="form-select" id="searchSupplierType">
                            <option value="">جميع الأنواع</option>
                            <option value="company">شركة</option>
                            <option value="individual">فرد</option>
                            <option value="government">جهة حكومية</option>
                            <option value="international">مورد دولي</option>
                        </select>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-primary" id="searchSuppliersBtn">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                        <button type="button" class="btn btn-secondary" id="clearSearchBtn">
                            <i class="fas fa-times me-2"></i>مسح
                        </button>
                        <button type="button" class="btn btn-warning" onclick="forceCloseModal()">
                            <i class="fas fa-door-open me-2"></i>إغلاق النافذة
                        </button>
                    </div>
                </div>

                <!-- نتائج البحث -->
                <div id="supplierSearchResults">
                    <div class="text-center">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <p class="text-muted">اضغط "بحث" لعرض الموردين أو استخدم معايير البحث أعلاه</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}
