#!/usr/bin/env python3
"""
OneDrive Authorization Code Flow للحسابات الشخصية
OneDrive Authorization Code Flow for Personal Accounts
"""

import requests
import json
import os
import webbrowser
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import time

class AuthHandler(BaseHTTPRequestHandler):
    """معالج callback للمصادقة"""
    
    def do_GET(self):
        """معالجة GET request"""
        if self.path.startswith('/auth/onedrive/callback'):
            # استخراج authorization code
            query = urllib.parse.urlparse(self.path).query
            params = urllib.parse.parse_qs(query)
            
            if 'code' in params:
                self.server.auth_code = params['code'][0]
                self.send_response(200)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                self.wfile.write(b'''
                <html>
                <body>
                <h1>تم بنجاح!</h1>
                <p>تم الحصول على authorization code. يمكنك إغلاق هذه النافذة.</p>
                <script>window.close();</script>
                </body>
                </html>
                ''')
            else:
                self.send_response(400)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                self.wfile.write(b'<h1>خطأ: لم يتم الحصول على authorization code</h1>')
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        """تعطيل رسائل السجل"""
        pass

def get_authorization_code(client_id, redirect_uri):
    """الحصول على authorization code"""
    
    # إعداد الخادم المحلي
    server = HTTPServer(('localhost', 5000), AuthHandler)
    server.auth_code = None
    
    # تشغيل الخادم في thread منفصل
    server_thread = threading.Thread(target=server.serve_forever)
    server_thread.daemon = True
    server_thread.start()
    
    # إنشاء URL للمصادقة
    auth_url = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize"
    
    params = {
        "client_id": client_id,
        "response_type": "code",
        "redirect_uri": redirect_uri,
        "scope": "Files.ReadWrite Files.ReadWrite.All offline_access",
        "response_mode": "query"
    }
    
    auth_url_full = f"{auth_url}?" + urllib.parse.urlencode(params)
    
    print(f"🌐 فتح المتصفح للمصادقة...")
    print(f"📋 إذا لم يفتح المتصفح تلقائياً، انسخ هذا الرابط:")
    print(f"{auth_url_full}")
    
    # فتح المتصفح
    webbrowser.open(auth_url_full)
    
    # انتظار authorization code
    print(f"⏳ انتظار المصادقة...")
    timeout = 120  # دقيقتان
    start_time = time.time()
    
    while server.auth_code is None and (time.time() - start_time) < timeout:
        time.sleep(1)
    
    server.shutdown()
    
    if server.auth_code:
        print(f"✅ تم الحصول على authorization code!")
        return server.auth_code
    else:
        print(f"❌ انتهت مهلة الانتظار")
        return None

def get_access_token_from_code(client_id, client_secret, redirect_uri, auth_code):
    """الحصول على access token من authorization code"""
    
    token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
    
    data = {
        "client_id": client_id,
        "client_secret": client_secret,
        "code": auth_code,
        "redirect_uri": redirect_uri,
        "grant_type": "authorization_code"
    }
    
    response = requests.post(token_url, data=data)
    
    if response.status_code == 200:
        token_data = response.json()
        return token_data.get("access_token")
    else:
        print(f"❌ خطأ في الحصول على access token: {response.status_code}")
        try:
            error_data = response.json()
            print(f"📋 تفاصيل الخطأ: {error_data}")
        except:
            print(f"📄 Raw response: {response.text}")
        return None

def upload_file_to_onedrive(access_token, file_path, filename):
    """رفع ملف إلى OneDrive الحقيقي"""
    
    upload_url = f"https://graph.microsoft.com/v1.0/me/drive/root:/{filename}:/content"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/octet-stream"
    }
    
    with open(file_path, 'rb') as f:
        response = requests.put(upload_url, data=f, headers=headers)
    
    if response.status_code in [200, 201]:
        file_data = response.json()
        print(f"✅ تم رفع الملف بنجاح!")
        return file_data.get("id")
    else:
        print(f"❌ فشل في رفع الملف: {response.status_code}")
        try:
            error_data = response.json()
            print(f"📋 تفاصيل الخطأ: {error_data}")
        except:
            print(f"📄 Raw response: {response.text}")
        return None

def create_share_link(access_token, file_id):
    """إنشاء رابط مشاركة حقيقي"""
    
    share_url = f"https://graph.microsoft.com/v1.0/me/drive/items/{file_id}/createLink"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "type": "view",
        "scope": "anonymous"
    }
    
    response = requests.post(share_url, json=data, headers=headers)
    
    if response.status_code == 201:
        link_data = response.json()
        return link_data.get("link", {}).get("webUrl")
    else:
        print(f"❌ فشل في إنشاء رابط المشاركة: {response.status_code}")
        try:
            error_data = response.json()
            print(f"📋 تفاصيل الخطأ: {error_data}")
        except:
            print(f"📄 Raw response: {response.text}")
        return None

def test_real_onedrive_upload():
    """اختبار رفع حقيقي لـ OneDrive"""
    
    print("🧪 اختبار OneDrive الحقيقي مع Authorization Code Flow")
    print("=" * 70)
    
    # قراءة الإعدادات
    config_path = os.path.join('app', 'shipments', 'cloud_config.json')
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    onedrive_config = config.get('onedrive', {})
    client_id = onedrive_config.get('client_id')
    client_secret = onedrive_config.get('client_secret')
    redirect_uri = onedrive_config.get('redirect_uri')
    
    print(f"📋 الإعدادات:")
    print(f"  - Client ID: {client_id}")
    print(f"  - Redirect URI: {redirect_uri}")
    
    # الحصول على authorization code
    auth_code = get_authorization_code(client_id, redirect_uri)
    
    if not auth_code:
        print(f"❌ فشل في الحصول على authorization code")
        return False
    
    # الحصول على access token
    access_token = get_access_token_from_code(client_id, client_secret, redirect_uri, auth_code)
    
    if not access_token:
        print(f"❌ فشل في الحصول على access token")
        return False
    
    print(f"✅ تم الحصول على access token!")
    print(f"  - Token Length: {len(access_token)}")
    
    # إنشاء ملف تجريبي
    import tempfile
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("SASERP OneDrive Real Test - Authorization Code Flow\n")
        f.write("هذا ملف تجريبي لاختبار OneDrive الحقيقي\n")
        f.write(f"تاريخ الإنشاء: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        temp_file_path = f.name
    
    try:
        # رفع الملف
        filename = "saserp_real_test.txt"
        file_id = upload_file_to_onedrive(access_token, temp_file_path, filename)
        
        if not file_id:
            return False
        
        # إنشاء رابط مشاركة
        share_link = create_share_link(access_token, file_id)
        
        if share_link:
            print(f"🎉 تم إنشاء رابط مشاركة حقيقي!")
            print(f"🔗 الرابط: {share_link}")
            print(f"💡 يمكنك نسخ هذا الرابط وفتحه في المتصفح")
            
            # فتح الرابط في المتصفح
            try:
                webbrowser.open(share_link)
                print(f"🌐 تم فتح الرابط في المتصفح")
            except:
                print(f"⚠️ لم يتم فتح المتصفح تلقائياً")
            
            return True
        else:
            return False
    
    finally:
        # تنظيف الملف المؤقت
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

if __name__ == "__main__":
    success = test_real_onedrive_upload()
    
    if success:
        print(f"\n🎉 نجح الاختبار!")
        print(f"💡 الآن يمكنك دمج هذا الكود في النظام الأساسي")
    else:
        print(f"\n❌ فشل الاختبار")
        print(f"💡 تحقق من الإعدادات والصلاحيات")
