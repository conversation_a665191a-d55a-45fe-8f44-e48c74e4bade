#!/usr/bin/env python3
"""
إضافة أعمدة روابط المشاركة لجدول contract_documents
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oracle_manager import get_oracle_manager

def add_share_columns():
    """إضافة أعمدة روابط المشاركة"""
    
    oracle_manager = get_oracle_manager()
    
    try:
        print("🔧 إضافة أعمدة روابط المشاركة لجدول contract_documents...")
        
        # قائمة الأعمدة المطلوبة
        columns_to_add = [
            {
                'name': 'nextcloud_share_link',
                'type': 'VARCHAR2(500)',
                'description': 'رابط مشاركة Nextcloud'
            },
            {
                'name': 'onedrive_share_link', 
                'type': 'VARCHAR2(500)',
                'description': 'رابط مشاركة OneDrive'
            },
            {
                'name': 'nextcloud_created_at',
                'type': 'DATE',
                'description': 'تاريخ إنشاء رابط Nextcloud'
            },
            {
                'name': 'onedrive_created_at',
                'type': 'DATE', 
                'description': 'تاريخ إنشاء رابط OneDrive'
            }
        ]
        
        # فحص الأعمدة الموجودة
        existing_columns_query = """
            SELECT column_name 
            FROM user_tab_columns 
            WHERE table_name = 'CONTRACT_DOCUMENTS'
        """
        
        existing_result = oracle_manager.execute_query(existing_columns_query, [])
        existing_columns = [row[0].lower() for row in existing_result] if existing_result else []
        
        print(f"📋 الأعمدة الموجودة: {len(existing_columns)}")
        for col in existing_columns:
            print(f"  - {col}")
        
        # إضافة الأعمدة المفقودة
        added_count = 0
        for column in columns_to_add:
            column_name = column['name'].lower()
            
            if column_name not in existing_columns:
                try:
                    add_column_sql = f"""
                        ALTER TABLE contract_documents 
                        ADD {column['name']} {column['type']}
                    """
                    
                    oracle_manager.execute_update(add_column_sql, [])
                    print(f"✅ تم إضافة العمود: {column['name']} ({column['description']})")
                    added_count += 1
                    
                except Exception as e:
                    print(f"⚠️ فشل في إضافة العمود {column['name']}: {e}")
            else:
                print(f"ℹ️ العمود {column['name']} موجود مسبقاً")
        
        if added_count > 0:
            oracle_manager.commit()
            print(f"\n✅ تم إضافة {added_count} عمود جديد بنجاح")
        else:
            print("\nℹ️ جميع الأعمدة موجودة مسبقاً")
        
        # فحص النتيجة النهائية
        final_columns_result = oracle_manager.execute_query(existing_columns_query, [])
        final_count = len(final_columns_result) if final_columns_result else 0
        print(f"📊 إجمالي الأعمدة الآن: {final_count}")
        
        # اختبار الجدول
        print("\n🧪 اختبار الجدول...")
        test_query = """
            SELECT id, nextcloud_share_link, onedrive_share_link 
            FROM contract_documents 
            WHERE ROWNUM <= 1
        """
        try:
            test_result = oracle_manager.execute_query(test_query, [])
            print("✅ الأعمدة الجديدة تعمل بشكل صحيح")
        except Exception as e:
            print(f"❌ خطأ في اختبار الأعمدة الجديدة: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إضافة أعمدة روابط المشاركة...")
    success = add_share_columns()
    
    if success:
        print("\n✅ تم الانتهاء بنجاح")
    else:
        print("\n❌ فشل في العملية")
        sys.exit(1)
