#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقبة محاولات إنشاء الشحنات في الوقت الفعلي
"""

import sys
import os
import time
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def monitor_shipment_attempts():
    """مراقبة محاولات إنشاء الشحنات"""
    print("👁️ بدء مراقبة محاولات إنشاء الشحنات...")
    print("اضغط Ctrl+C للتوقف")
    print("=" * 60)
    
    try:
        from oracle_manager import get_oracle_manager
        
        # تتبع آخر معرفات معروفة
        last_shipment_id = None
        last_item_id = None
        last_container_id = None
        
        while True:
            try:
                db_manager = get_oracle_manager()
                if not db_manager.connect():
                    print("❌ فشل الاتصال بقاعدة البيانات")
                    time.sleep(5)
                    continue
                
                current_time = datetime.now().strftime("%H:%M:%S")
                
                # فحص آخر شحنة
                shipment_query = "SELECT MAX(id) FROM cargo_shipments"
                shipment_result = db_manager.execute_query(shipment_query)
                current_shipment_id = shipment_result[0][0] if shipment_result and shipment_result[0][0] else 0
                
                # فحص آخر صنف
                item_query = "SELECT MAX(id) FROM cargo_shipment_items"
                item_result = db_manager.execute_query(item_query)
                current_item_id = item_result[0][0] if item_result and item_result[0][0] else 0
                
                # فحص آخر حاوية
                container_query = "SELECT MAX(id) FROM cargo_containers"
                container_result = db_manager.execute_query(container_query)
                current_container_id = container_result[0][0] if container_result and container_result[0][0] else 0
                
                # فحص sequences
                seq_shipment_query = "SELECT cargo_shipments_seq.CURRVAL FROM DUAL"
                seq_item_query = "SELECT cargo_shipment_items_seq.CURRVAL FROM DUAL"
                seq_container_query = "SELECT cargo_containers_seq.CURRVAL FROM DUAL"
                
                try:
                    seq_shipment_result = db_manager.execute_query(seq_shipment_query)
                    seq_shipment_val = seq_shipment_result[0][0] if seq_shipment_result else "غير متاح"
                except:
                    seq_shipment_val = "غير متاح"
                
                try:
                    seq_item_result = db_manager.execute_query(seq_item_query)
                    seq_item_val = seq_item_result[0][0] if seq_item_result else "غير متاح"
                except:
                    seq_item_val = "غير متاح"
                
                try:
                    seq_container_result = db_manager.execute_query(seq_container_query)
                    seq_container_val = seq_container_result[0][0] if seq_container_result else "غير متاح"
                except:
                    seq_container_val = "غير متاح"
                
                # التحقق من التغييرات
                changes_detected = False
                
                if last_shipment_id is None:
                    last_shipment_id = current_shipment_id
                    last_item_id = current_item_id
                    last_container_id = current_container_id
                    print(f"[{current_time}] 📊 الحالة الأولية:")
                    print(f"  📦 آخر شحنة: {current_shipment_id}")
                    print(f"  📋 آخر صنف: {current_item_id}")
                    print(f"  📦 آخر حاوية: {current_container_id}")
                    print(f"  🔢 Sequences: شحنات={seq_shipment_val}, أصناف={seq_item_val}, حاويات={seq_container_val}")
                    changes_detected = True
                
                if current_shipment_id != last_shipment_id:
                    print(f"[{current_time}] 🆕 شحنة جديدة: {last_shipment_id} → {current_shipment_id}")
                    
                    # جلب تفاصيل الشحنة الجديدة
                    new_shipment_query = """
                        SELECT shipment_number, tracking_number, created_at
                        FROM cargo_shipments
                        WHERE id = :1
                    """
                    new_shipment_result = db_manager.execute_query(new_shipment_query, [current_shipment_id])
                    if new_shipment_result:
                        shipment_data = new_shipment_result[0]
                        print(f"    📋 رقم الشحنة: {shipment_data[0]}")
                        print(f"    🔍 رقم التتبع: {shipment_data[1]}")
                        print(f"    📅 تاريخ الإنشاء: {shipment_data[2]}")
                    
                    last_shipment_id = current_shipment_id
                    changes_detected = True
                
                if current_item_id != last_item_id:
                    print(f"[{current_time}] 📋 صنف جديد: {last_item_id} → {current_item_id}")
                    
                    # فحص إذا كان الصنف مرتبط بشحنة موجودة
                    orphan_check_query = """
                        SELECT i.cargo_shipment_id, s.shipment_number
                        FROM cargo_shipment_items i
                        LEFT JOIN cargo_shipments s ON i.cargo_shipment_id = s.id
                        WHERE i.id = :1
                    """
                    orphan_result = db_manager.execute_query(orphan_check_query, [current_item_id])
                    if orphan_result:
                        item_data = orphan_result[0]
                        if item_data[1]:
                            print(f"    ✅ مرتبط بالشحنة: {item_data[1]} (ID: {item_data[0]})")
                        else:
                            print(f"    ❌ صنف يتيم! cargo_shipment_id: {item_data[0]} غير موجود")
                    
                    last_item_id = current_item_id
                    changes_detected = True
                
                if current_container_id != last_container_id:
                    print(f"[{current_time}] 📦 حاوية جديدة: {last_container_id} → {current_container_id}")
                    
                    # فحص إذا كانت الحاوية مرتبطة بشحنة موجودة
                    container_orphan_check_query = """
                        SELECT c.cargo_shipment_id, s.shipment_number, c.container_number
                        FROM cargo_containers c
                        LEFT JOIN cargo_shipments s ON c.cargo_shipment_id = s.id
                        WHERE c.id = :1
                    """
                    container_orphan_result = db_manager.execute_query(container_orphan_check_query, [current_container_id])
                    if container_orphan_result:
                        container_data = container_orphan_result[0]
                        if container_data[1]:
                            print(f"    ✅ مرتبطة بالشحنة: {container_data[1]} (ID: {container_data[0]})")
                            print(f"    📦 رقم الحاوية: {container_data[2]}")
                        else:
                            print(f"    ❌ حاوية يتيمة! cargo_shipment_id: {container_data[0]} غير موجود")
                            print(f"    📦 رقم الحاوية: {container_data[2]}")
                    
                    last_container_id = current_container_id
                    changes_detected = True
                
                if changes_detected:
                    print("-" * 60)
                
                db_manager.close()
                time.sleep(2)  # فحص كل ثانيتين
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"[{current_time}] ❌ خطأ في المراقبة: {e}")
                time.sleep(5)
    
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف المراقبة")
    except Exception as e:
        print(f"❌ خطأ عام في المراقبة: {e}")

if __name__ == "__main__":
    monitor_shipment_attempts()
