-- إضافة حقلي الفرع والمنفذ الجمركي لجدول المخلصين الجمركيين
-- Adding branch and customs port fields to customs agents table

-- التحقق من وجود الأعمدة قبل الإضافة
DECLARE
    column_exists NUMBER;
BEGIN
    -- التحقق من عمود branch_id
    SELECT COUNT(*) INTO column_exists
    FROM user_tab_columns
    WHERE table_name = 'CUSTOMS_AGENTS' AND column_name = 'BRANCH_ID';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE customs_agents ADD (branch_id NUMBER)';
        DBMS_OUTPUT.PUT_LINE('✅ تم إضافة عمود branch_id');
    ELSE
        DBMS_OUTPUT.PUT_LINE('⚠️ عمود branch_id موجود بالفعل');
    END IF;
    
    -- التحقق من عمود customs_port_id
    SELECT COUNT(*) INTO column_exists
    FROM user_tab_columns
    WHERE table_name = 'CUSTOMS_AGENTS' AND column_name = 'CUSTOMS_PORT_ID';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE customs_agents ADD (customs_port_id NUMBER)';
        DBMS_OUTPUT.PUT_LINE('✅ تم إضافة عمود customs_port_id');
    ELSE
        DBMS_OUTPUT.PUT_LINE('⚠️ عمود customs_port_id موجود بالفعل');
    END IF;
END;
/

-- إضافة المفاتيح الخارجية
DECLARE
    constraint_exists NUMBER;
BEGIN
    -- التحقق من وجود foreign key للفرع
    SELECT COUNT(*) INTO constraint_exists
    FROM user_constraints
    WHERE table_name = 'CUSTOMS_AGENTS' AND constraint_name = 'FK_CUSTOMS_AGENTS_BRANCH';
    
    IF constraint_exists = 0 THEN
        BEGIN
            EXECUTE IMMEDIATE 'ALTER TABLE customs_agents ADD CONSTRAINT FK_CUSTOMS_AGENTS_BRANCH FOREIGN KEY (branch_id) REFERENCES branches(brn_no)';
            DBMS_OUTPUT.PUT_LINE('✅ تم إضافة foreign key للفرع');
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('⚠️ لم يتم إضافة foreign key للفرع: ' || SQLERRM);
        END;
    ELSE
        DBMS_OUTPUT.PUT_LINE('⚠️ foreign key للفرع موجود بالفعل');
    END IF;
    
    -- التحقق من وجود foreign key للمنفذ الجمركي
    SELECT COUNT(*) INTO constraint_exists
    FROM user_constraints
    WHERE table_name = 'CUSTOMS_AGENTS' AND constraint_name = 'FK_CUSTOMS_AGENTS_PORT';
    
    IF constraint_exists = 0 THEN
        BEGIN
            EXECUTE IMMEDIATE 'ALTER TABLE customs_agents ADD CONSTRAINT FK_CUSTOMS_AGENTS_PORT FOREIGN KEY (customs_port_id) REFERENCES customs_ports(id)';
            DBMS_OUTPUT.PUT_LINE('✅ تم إضافة foreign key للمنفذ الجمركي');
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('⚠️ لم يتم إضافة foreign key للمنفذ الجمركي: ' || SQLERRM);
        END;
    ELSE
        DBMS_OUTPUT.PUT_LINE('⚠️ foreign key للمنفذ الجمركي موجود بالفعل');
    END IF;
END;
/

-- إضافة تعليقات على الأعمدة الجديدة
COMMENT ON COLUMN customs_agents.branch_id IS 'رقم الفرع المرتبط بالمخلص الجمركي';
COMMENT ON COLUMN customs_agents.customs_port_id IS 'رقم المنفذ الجمركي المرتبط بالمخلص';

-- إضافة فهارس للبحث السريع
BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_customs_agents_branch ON customs_agents(branch_id)';
    DBMS_OUTPUT.PUT_LINE('✅ تم إضافة فهرس للفرع');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -955 THEN -- ORA-00955: name is already used
            DBMS_OUTPUT.PUT_LINE('⚠️ فهرس الفرع موجود بالفعل');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في إضافة فهرس الفرع: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX idx_customs_agents_port ON customs_agents(customs_port_id)';
    DBMS_OUTPUT.PUT_LINE('✅ تم إضافة فهرس للمنفذ الجمركي');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -955 THEN -- ORA-00955: name is already used
            DBMS_OUTPUT.PUT_LINE('⚠️ فهرس المنفذ الجمركي موجود بالفعل');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ خطأ في إضافة فهرس المنفذ الجمركي: ' || SQLERRM);
        END IF;
END;
/

-- تحديث البيانات الموجودة (اختياري)
-- يمكن تعيين قيم افتراضية للمخلصين الموجودين
UPDATE customs_agents 
SET branch_id = (SELECT MIN(brn_no) FROM branches WHERE ROWNUM = 1),
    customs_port_id = (SELECT MIN(id) FROM customs_ports WHERE is_active = 1 AND ROWNUM = 1)
WHERE branch_id IS NULL OR customs_port_id IS NULL;

COMMIT;

-- عرض النتائج
SELECT 'تم تحديث جدول المخلصين الجمركيين بنجاح!' as message FROM dual;
SELECT COUNT(*) as total_agents FROM customs_agents;
SELECT 
    ca.agent_name,
    b.brn_lname as branch_name,
    cp.port_name_ar as port_name
FROM customs_agents ca
LEFT JOIN branches b ON ca.branch_id = b.brn_no
LEFT JOIN customs_ports cp ON ca.customs_port_id = cp.id
WHERE ROWNUM <= 5;
